{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "Bash(for feature in authentication workflow delegation monitoring history reports user-management organization)", "Bash(do mkdir -p \"src/features/$feature\"/{components,hooks,services,types,utils,constants})", "Bash(done)", "Bash(rm:*)", "Bash(npm install:*)", "Bash(npm view:*)", "Bash(rg:*)", "Bash(grep:*)", "Bash(yarn install)", "mcp__sequential-thinking__sequentialthinking"], "deny": []}}