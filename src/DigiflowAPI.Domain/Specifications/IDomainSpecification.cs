using System.Linq.Expressions;

namespace DigiflowAPI.Domain.Specifications;

/// <summary>
/// Domain specification pattern interface for expressing business rules.
/// </summary>
public interface IDomainSpecification<T>
{
    /// <summary>
    /// Converts the specification to a LINQ expression.
    /// </summary>
    Expression<Func<T, bool>> ToExpression();
    
    /// <summary>
    /// Checks if the given entity satisfies the specification.
    /// </summary>
    bool IsSatisfiedBy(T entity);
}