using System.Linq.Expressions;
using DigiflowAPI.Domain.Aggregates;
using DigiflowAPI.Domain.ValueObjects;

namespace DigiflowAPI.Domain.Specifications;

/// <summary>
/// Specification for active workflows.
/// </summary>
public class ActiveWorkflowSpecification : Specification<WorkflowInstance>
{
    public override Expression<Func<WorkflowInstance, bool>> ToExpression()
    {
        return workflow => workflow.Status == WorkflowStatus.Active;
    }
}

/// <summary>
/// Specification for workflows owned by a specific user.
/// </summary>
public class WorkflowOwnedByUserSpecification : Specification<WorkflowInstance>
{
    private readonly long _userId;
    
    public WorkflowOwnedByUserSpecification(long userId)
    {
        _userId = userId;
    }
    
    public override Expression<Func<WorkflowInstance, bool>> ToExpression()
    {
        return workflow => workflow.OwnerLoginId == _userId;
    }
}

/// <summary>
/// Specification for workflows with high priority or above.
/// </summary>
public class HighPriorityWorkflowSpecification : Specification<WorkflowInstance>
{
    public override Expression<Func<WorkflowInstance, bool>> ToExpression()
    {
        return workflow => workflow.Priority.Value >= Priority.High.Value;
    }
}

/// <summary>
/// Specification for overdue workflows (active for more than specified days).
/// </summary>
public class OverdueWorkflowSpecification : Specification<WorkflowInstance>
{
    private readonly int _daysOverdue;
    
    public OverdueWorkflowSpecification(int daysOverdue)
    {
        _daysOverdue = daysOverdue;
    }
    
    public override Expression<Func<WorkflowInstance, bool>> ToExpression()
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-_daysOverdue);
        return workflow => workflow.Status == WorkflowStatus.Active && 
                          workflow.StartTime < cutoffDate;
    }
}

/// <summary>
/// Specification for suspended workflows.
/// </summary>
public class SuspendedWorkflowSpecification : Specification<WorkflowInstance>
{
    public override Expression<Func<WorkflowInstance, bool>> ToExpression()
    {
        return workflow => workflow.Status == WorkflowStatus.Suspended;
    }
}

/// <summary>
/// Specification for completed workflows within a date range.
/// </summary>
public class CompletedWorkflowInDateRangeSpecification : Specification<WorkflowInstance>
{
    private readonly DateTime _startDate;
    private readonly DateTime _endDate;
    
    public CompletedWorkflowInDateRangeSpecification(DateTime startDate, DateTime endDate)
    {
        _startDate = startDate;
        _endDate = endDate;
    }
    
    public override Expression<Func<WorkflowInstance, bool>> ToExpression()
    {
        return workflow => workflow.Status == WorkflowStatus.Completed &&
                          workflow.EndTime.HasValue &&
                          workflow.EndTime.Value >= _startDate &&
                          workflow.EndTime.Value <= _endDate;
    }
}