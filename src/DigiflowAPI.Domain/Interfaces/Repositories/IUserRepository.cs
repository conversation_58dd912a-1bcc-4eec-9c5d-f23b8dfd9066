﻿using DigiflowAPI.Domain.Entities;

namespace DigiflowAPI.Domain.Interfaces.Repositories;

public interface IUserRepository
{
    Task<long> GetUserInfo(Dictionary<string, object> parameters);
    Task<DpHrDeps?> GetDepartmentById(decimal departmentId);
    Task<IEnumerable<DpHrDeps>> GetDepartmentsAtLevel(long ustBolumId);
    Task<long> GetUserDepartmentId(long userId);
    Task<IEnumerable<VwUserInformation>> GetUsersByDepartmentId(long departmentId, bool excludeActiveUser);
    Task<IEnumerable<VwUserInformation>> GetForwardPersonel(string username);
    Task<IEnumerable<VwUserInformation?>> GetAllAsync();
    Task<VwUserInformation?> GetByIdAsync(long? id);
    Task<VwUserInformation?> GetByUsernameAsync(string username);
    // TODO: Replace SelectOptionDto with domain entity or primitive types
    // Task<IEnumerable<SelectOptionDto>> GetUsersByDepartmentIdAsync(long departmentId, bool excludeActiveUser = false);
    Task<long> GetUserInfo();
    string GetActiveUserName();
}