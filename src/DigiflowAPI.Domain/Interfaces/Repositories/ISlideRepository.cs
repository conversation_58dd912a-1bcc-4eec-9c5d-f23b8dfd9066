using DigiflowAPI.Domain.Entities;

namespace DigiflowAPI.Domain.Interfaces.Repositories;

public interface ISlideRepository
{
    Task<IEnumerable<HomeSlide>> GetAllSlidesAsync();
    Task<IEnumerable<HomeSlide>> GetActiveSlidesAsync();
    Task<HomeSlide?> GetSlideByIdAsync(int id);
    Task<HomeSlide> UpdateSlideAsync(HomeSlide slide);
    Task<bool> DeleteSlideAsync(int id);
    Task<bool> SlideExistsAsync(int id);
}
