﻿using System.Linq.Expressions;

namespace DigiflowAPI.Domain.Interfaces.Repositories;

/// <summary>
/// Generic repository interface defining basic CRUD operations.
/// </summary>
public interface IRepository
{
    //Asynchronous Methods
    /// <summary>
    /// Retrieves an entity by its identifier.
    /// </summary>
    Task<T> GetByIdAsync<T>(int id) where T : class;

    /// <summary>
    /// Retrieves all entities.
    /// </summary>
    Task<IEnumerable<T>> GetAllAsync<T>() where T : class;

    /// <summary>
    /// Finds entities based on a predicate.
    /// </summary>
    Task<IEnumerable<T>> FindAsync<T>(Expression<Func<T, bool>> predicate) where T : class;

    /// <summary>
    /// Adds a new entity.
    /// </summary>
    Task AddAsync<T>(T entity) where T : class;

    /// <summary>
    /// Adds a range of entities.
    /// </summary>
    Task AddRangeAsync<T>(IEnumerable<T> entities) where T : class;

    /// <summary>
    /// Removes an entity.
    /// </summary>
    Task RemoveAsync<T>(T entity) where T : class;

    /// <summary>
    /// Removes a range of entities.
    /// </summary>
    Task RemoveRangeAsync<T>(IEnumerable<T> entities) where T : class;

    /// <summary>
    /// Retrieves a single entity using a key.
    /// </summary>
    Task<T> GetEntityAsync<T>(object key) where T : class;

    /// <summary>
    /// Counts the number of entities satisfying a predicate.
    /// </summary>
    Task<int> CountAsync<T>(Expression<Func<T, bool>> predicate) where T : class;

    /// <summary>
    /// Checks if any entity satisfies a predicate.
    /// </summary>
    Task<bool> AnyAsync<T>(Expression<Func<T, bool>> predicate) where T : class;

    /// <summary>
    /// Updates an entity.
    /// </summary>
    Task UpdateAsync<T>(T entity) where T : class;

    /// <summary>
    /// Retrieves the first entity that satisfies a predicate or null if none is found.
    /// </summary>
    Task<T> FirstOrDefaultAsync<T>(Expression<Func<T, bool>> predicate) where T : class;

    /// <summary>
    /// Retrieves a list of entities based on a raw SQL query.
    /// </summary>
    Task<IEnumerable<T>> FromSqlRawAsync<T>(string sql, params object[] parameters) where T : class;

    /// <summary>
    /// Executes a raw SQL command (e.g., for updates or deletes).
    /// </summary>
    Task<int> ExecuteSqlRawAsync(string sql, params object[] parameters);

    /// <summary>
    /// Retrieves entities with paging and optional filter, sort, and includes.
    /// </summary>
    Task<(IEnumerable<T> Items, int TotalCount)> GetPagedAsync<T>(
        int pageIndex, int pageSize,
        Expression<Func<T, bool>> filter = null,
        Func<IQueryable<T>, IOrderedQueryable<T>> orderBy = null,
        string includeProperties = "") where T : class;

    //Synchronous Methods
    /// <summary>
    /// Retrieves an entity by its identifier synchronously.
    /// </summary>
    T GetById<T>(int id) where T : class;

    /// <summary>
    /// Retrieves all entities synchronously.
    /// </summary>
    IEnumerable<T> GetAll<T>() where T : class;

    /// <summary>
    /// Finds entities based on a predicate synchronously.
    /// </summary>
    IEnumerable<T> Find<T>(Expression<Func<T, bool>> predicate) where T : class;

    /// <summary>
    /// Adds a new entity synchronously.
    /// </summary>
    void Add<T>(T entity) where T : class;

    /// <summary>
    /// Adds a range of entities synchronously.
    /// </summary>
    void AddRange<T>(IEnumerable<T> entities) where T : class;

    /// <summary>
    /// Removes an entity synchronously.
    /// </summary>
    void Remove<T>(T entity) where T : class;

    /// <summary>
    /// Removes a range of entities synchronously.
    /// </summary>
    void RemoveRange<T>(IEnumerable<T> entities) where T : class;

    /// <summary>
    /// Removes a range of entities synchronously.
    /// </summary>
    void GetEntity<T>(object key) where T : class;

    /// <summary>
    /// Counts the number of entities satisfying a predicate synchronously.
    /// </summary>
    int Count<T>(Expression<Func<T, bool>> predicate) where T : class;

    /// <summary>
    /// Checks if any entity satisfies a predicate synchronously.
    /// </summary>
    bool Any<T>(Expression<Func<T, bool>> predicate) where T : class;

    /// <summary>
    /// Updates an entity synchronously.
    /// </summary>
    void Update<T>(T entity) where T : class;

    /// <summary>
    /// Retrieves the first entity that satisfies a predicate or null if none is found synchronously.
    /// </summary>
    T FirstOrDefault<T>(Expression<Func<T, bool>> predicate) where T : class;

    // Additional Methods
    /// <summary>
    /// Saves changes to the context synchronously.
    /// </summary>
    void SaveChanges();

    /// <summary>
    /// Executes a raw SQL query and returns the resulting data as a list synchronously.
    /// </summary>
    IEnumerable<T> ExecuteQuery<T>(string sql, params object[] parameters) where T : class;

    /// <summary>
    /// Executes a raw SQL command (e.g., for updates or deletes) and returns the number of affected rows synchronously.
    /// </summary>
    int ExecuteSqlCommand(string sql, params object[] parameters);

    /// <summary>
    /// Retrieves entities with paging and optional filter, sort, and includes synchronously.
    /// </summary>
    (IEnumerable<T> Items, int TotalCount) GetPaged<T>(
        int pageIndex, int pageSize,
        Expression<Func<T, bool>> filter = null,
        Func<IQueryable<T>, IOrderedQueryable<T>> orderBy = null,
        string includeProperties = "") where T : class;
}