using System.Linq.Expressions;

namespace DigiflowAPI.Domain.Interfaces.Repositories
{
    /// <summary>
    /// Read-only repository interface for query operations
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    public interface IReadRepository<T> where T : class
    {
        Task<T?> GetByIdAsync(object id);
        Task<IEnumerable<T>> GetAllAsync();
        Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate);
        Task<T?> SingleOrDefaultAsync(Expression<Func<T, bool>> predicate);
        Task<bool> AnyAsync(Expression<Func<T, bool>> predicate);
        Task<int> CountAsync(Expression<Func<T, bool>> predicate);
    }
}