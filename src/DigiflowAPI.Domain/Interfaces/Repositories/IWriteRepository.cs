namespace DigiflowAPI.Domain.Interfaces.Repositories
{
    /// <summary>
    /// Write repository interface for command operations
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    public interface IWriteRepository<T> where T : class
    {
        Task<T> AddAsync(T entity);
        Task AddRangeAsync(IEnumerable<T> entities);
        Task UpdateAsync(T entity);
        Task UpdateRangeAsync(IEnumerable<T> entities);
        Task RemoveAsync(T entity);
        Task RemoveRangeAsync(IEnumerable<T> entities);
    }
}