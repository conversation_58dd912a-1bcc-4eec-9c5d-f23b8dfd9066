﻿using DigiflowAPI.Domain.Entities;

namespace DigiflowAPI.Domain.Interfaces.Repositories;

public interface IInboxRepository
{
    Task<(IEnumerable<Inbox> inbox, IEnumerable<Inbox> delegated, IEnumerable<Inbox> commented)> GetAllAsync(long userId);
    Task<IEnumerable<Inbox?>> GetAllInboxAsync(long userId);
    Task<IEnumerable<Inbox?>> GetAllDelegatedAsync(long userId);
    Task<IEnumerable<Inbox?>> GetAllCommentedAsync(long userId);
}
