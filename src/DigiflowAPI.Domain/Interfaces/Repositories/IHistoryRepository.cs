﻿using DigiflowAPI.Domain.Entities;
using DigiflowAPI.Domain.Entities.History;

namespace DigiflowAPI.Domain.Interfaces.Repositories;

public interface IHistoryRepository
{
    Task<IEnumerable<TbWorkflowHistoryView?>> GetAllAsync(string workflowType, string? years);
    Task<IEnumerable<VwWorkflowHistory>> GetAdminHistoryAsync(long wfDefId, long loginId, int pageNo, int pageSize, string sortColumn, string sortDirection);
    Task<IEnumerable<Inbox>> GetAdminWorkflowsAsync(long wfDefId);
    Task<IEnumerable<Inbox>> GetSuspendedWorkflowsAsync(long userId);
}
