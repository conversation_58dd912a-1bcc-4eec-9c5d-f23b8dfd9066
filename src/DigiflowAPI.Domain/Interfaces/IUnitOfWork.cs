using DigiflowAPI.Domain.Interfaces.Repositories;

namespace DigiflowAPI.Domain.Interfaces;

/// <summary>
/// Unit of Work pattern interface to manage transactions and coordinate the work of multiple repositories.
/// </summary>
public interface IUnitOfWork : IDisposable
{
    // Repository properties
    IUserRepository Users { get; }
    IWorkflowRepository Workflows { get; }
    IHistoryRepository Histories { get; }
    IInboxRepository Inboxes { get; }
    IOrganizationRepository Organizations { get; }
    IPermissionRepository Permissions { get; }
    IEmailTemplateRepository EmailTemplates { get; }
    ISlideRepository Slides { get; }
    ILogicalGroupRepository LogicalGroups { get; }
    ISharePointRepository SharePoint { get; }
    
    /// <summary>
    /// Generic repository access for entities not covered by specific repositories
    /// </summary>
    IRepository Repository { get; }
    
    /// <summary>
    /// Saves all changes made in this unit of work to the database.
    /// </summary>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Saves all changes made in this unit of work to the database synchronously.
    /// </summary>
    int SaveChanges();
    
    /// <summary>
    /// Begins a new transaction.
    /// </summary>
    Task<IDbContextTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Commits the current transaction.
    /// </summary>
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Rolls back the current transaction.
    /// </summary>
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Represents a database transaction
/// </summary>
public interface IDbContextTransaction : IDisposable
{
    /// <summary>
    /// Commits the transaction.
    /// </summary>
    Task CommitAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Rolls back the transaction.
    /// </summary>
    Task RollbackAsync(CancellationToken cancellationToken = default);
}