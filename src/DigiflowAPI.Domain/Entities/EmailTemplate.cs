﻿using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities
{
    [Table("WF_EMAIL_TEMPLATE")]
    public class EmailTemplate
    {
        [Column("WF_EMAIL_TEMPLATE_ID")]
        public long Id { get; set; }

        [Column("SUBJECT")]
        public string Subject { get; set; }

        [Column("SOURCE")]
        public string Source { get; set; }

        [Column("DESCRIPTION")]
        public string Description { get; set; }
    }

}
