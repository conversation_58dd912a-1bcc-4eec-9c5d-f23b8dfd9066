﻿using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities
{
    public class MultipleApprovalDef
    {
        [Column("WF_WORKFLOW_DEF_ID")]
        public long? WfWorkflowDefId { get; set; }
        [Column("WF_WORKFLOW_ENTITY")]
        public string? WfWorkflowEntity { get; set; }
        [Column("WF_WORKFLOW_ENTITY_VALUE")]
        public string? WfWorkflowEntityValue { get; set; }
        [Column("LOGIN_ID")]
        public long? LoginId { get; set; }
        [Column("CONDITION")]
        public string? Condition { get; set; }
    }
}