using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities
{
  [Table("DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE")]
  public class HomeSlide
  {
    [Column("ID")]
    public int Id { get; set; }

    [Column("MENU_NAME_ID")]
    public int MenuNameId { get; set; }

    [Column("SLIDE_NAME")]
    public string Title { get; set; } = string.Empty;

    [Column("SLIDE_IMAGE_PATH")]
    public string ImageUrl { get; set; } = string.Empty; // Assuming SLIDE_IMAGE_PATH is not nullable based on schema context

    [Column("THUMBNAIL_IMAGE_PATH")]
    public string ThumbnailImagePath { get; set; } = string.Empty; // Assuming THUMBNAIL_IMAGE_PATH is not nullable

    [Column("SLIDE_CLICK_ACTION")]
    public int SlideClickAction { get; set; }

    [Column("SLIDE_TARGET_LINK")]
    public string? LinkUrl { get; set; }

    [Column("SLIDE_TARGET_CONTENT")]
    public string? Description { get; set; }

    [Column("SLIDE_POPUP_WIDTH")]
    public int? SlidePopupWidth { get; set; }

    [Column("SLIDE_POPUP_HEIGHT")]
    public int? SlidePopupHeight { get; set; }

    [Column("ACTIVE")]
    public char IsActive { get; set; } = '1'; // Defaulting, DB stores '1'/'0'

    [Column("ORDER_NO")]
    public int Order { get; set; }

    [Column("VALID_DATE_START")]
    public DateTime ValidDateStart { get; set; }

    [Column("VALID_DATE_END")]
    public DateTime ValidDateEnd { get; set; }

    [Column("CREATED")]
    public DateTime CreatedDate { get; set; } = DateTime.Now;

    [Column("CREATED_BY")]
    public long CreatedBy { get; set; } // Assuming CREATED_BY is not nullable

    [Column("LAST_UPDATED")]
    public DateTime? ModifiedDate { get; set; }

    [Column("LAST_UPDATED_BY")]
    public long? ModifiedBy { get; set; }

    [Column("DELETED")]
    public char IsDeleted { get; set; } // Mapped to '1'/'0' in repository

    [Column("SLIDE_TARGET_HEADLINE")]
    public string? SlideTargetHeadline { get; set; }
  }
}
