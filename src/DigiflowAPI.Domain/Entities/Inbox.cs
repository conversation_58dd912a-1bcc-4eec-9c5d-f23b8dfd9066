﻿using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities
{
    public class Inbox
    {
        [Column("ENTITYREFID")]
        public long? EntityRefId { get; set; }
        [Column("FLOWNAME")]
        public string? FlowName { get; set; }
        [Column("STATENAME")]
        public string? StateName { get; set; }
        [Column("ROUTE")]
        public string? Route { get; set; }
        [Column("WF_ACTION_STATUS_TYPE_CD")]
        public string WfActionStatusTypeCd { get; set; }
        [Column("OWNER_LOGIN_ID")]
        public long? OwnerLoginId { get; set; }
        [Column("WF_ACTION_TASK_INSTANCE_ID")]
        public long WfActionTaskInstanceId { get; set; }
        [Column("WF_ACTION_TASK_STATUS_T_CD")]
        public string? WfActionTaskStatusTCd { get; set; }
        [Column("START_TIME")]
        public DateTime? StartTime { get; set; }
        [Column("WFINSID")]
        public long WfInsId { get; set; }
        [Column("TASK_SCREEN")]
        public string? TaskScreen { get; set; }
        [Column("WF_ACTION_DEF_ID")]
        public long? WfActionDefId { get; set; }
        [Column("WFINSTANCEDEF")]
        public string? WfInstanceDef { get; set; }
        [Column("WFOWNER")]
        public char? WfOwner { get; set; }
        [Column("WF_WORKFLOW_DEF_ID")]
        public long? WfWorkflowDefId { get; set; }
        [Column("LASTLOGINID")]
        public long? LastLoginId { get; set; }
        [Column("WFLASTMODIFIEDBY")]
        public char? WfLastModifiedBy { get; set; }
        [Column("WFLASTMODIFIEDBY_NOM")]
        public char? WfLastModifiedByNom { get; set; }
        [Column("WFDATE")]
        public DateTime? WfDate { get; set; }
    }
}