﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiflowAPI.Domain.Entities.Config
{
    public class SharePointUrlMappings
    {
        [Column("MAPPING_ID")]
        public long MappingId { get; set; }
        [Column("CONFIG_KEY")]
        public string ConfigKey { get; set; }
        [Column("SHAREPOINT_URL")]
        public string SharePointUrl { get; set; }
    }
}
