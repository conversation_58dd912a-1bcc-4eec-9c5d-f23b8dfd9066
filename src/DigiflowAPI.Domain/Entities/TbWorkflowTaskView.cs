﻿namespace DigiflowAPI.Domain.Entities
{
    public class TbWorkflowTaskView
    {
        public long ActionInstanceId { get; set; }
        public long WorkflowInstanceId { get; set; }
        public long WorkflowDefId { get; set; }
        public long OwnerLoginId { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public string TaskStatusCode { get; set; }
        public long TaskDefId { get; set; }
        public string Description { get; set; }
        public string WorkflowName { get; set; }
        public long StateInstanceId { get; set; }
        public long? TaskOwnerId { get; set; }
        public string TaskType { get; set; }
        public string InstanceName { get; set; }
        public long ActionDefId { get; set; }
        public long StateDefId { get; set; }
        public string ActionName { get; set; }
        public string ActionDescription { get; set; }
        public string TaskScreen { get; set; }
    }
}
