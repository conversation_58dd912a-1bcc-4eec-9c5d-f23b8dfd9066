﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_TRANSITION_DEF", Schema = "FRAMEWORK")]
    public class FWfTransitionDef
    {
        [Key]
        [Column("WF_TRANSITION_DEF_ID")]
        public long WfTransitionDefId { get; set; }

        [ForeignKeyMapping("FromStateDefId", typeof(FWfStateDef), "WF_STATE_DEF_ID", "FromStateDef")]
        [Column("FROM_STATE_DEF_ID")]
        public long FromStateDefId { get; set; }

        [ForeignKeyMapping("ToStateDefId", typeof(FWfStateDef), "WF_STATE_DEF_ID", "ToStateDef")]
        [Column("TO_STATE_DEF_ID")]
        public long ToStateDefId { get; set; }

        public virtual FWfStateDef FromStateDef { get; set; }
        public virtual FWfStateDef ToStateDef { get; set; }
    }
}
