﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_INSTANCE_PARAMETER", Schema = "FRAMEWORK")]
    public class FWfInstanceParameter
    {
        [Key]
        [Column("WF_INSTANCE_PARAMETER_ID")]
        public long WfInstanceParameterId { get; set; }

        [Column("NAME")]
        public string Name { get; set; }

        [ForeignKeyMapping("WfWorkflowInstanceId", typeof(FWfWorkflowInstance), "WF_WORKFLOW_INSTANCE_ID", "WfWorkflowInstance")]
        [Column("WF_WORKFLOW_INSTANCE_ID")]
        public long WfWorkflowInstanceId { get; set; }

        [Column("VALUE")]
        public string Value { get; set; }

        [ForeignKeyMapping("WfParameterDataTypeCd", typeof(FWfParameterDataType), "WF_PARAMETER_DATA_TYPE_CD", "WfParameterDataType")]
        [Column("WF_PARAMETER_DATA_TYPE_CD")]
        public string WfParameterDataTypeCd { get; set; }

        public virtual FWfWorkflowInstance WfWorkflowInstance { get; set; }
        public virtual FWfParameterDataType WfParameterDataType { get; set; }

        public override string ToString()
        {
            return Name;
        }
    }
}
