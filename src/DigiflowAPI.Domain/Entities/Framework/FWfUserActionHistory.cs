﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_WORKFLOW_HISTORY", Schema = "FRAMEWORK")]
    public class FWfUserActionHistory
    {
        [Key]
        [Column("WF_HISTORY_ID")]
        public long WfHistoryId { get; set; }
        [Column("WF_WORKFLOW_INSTANCE_ID")]
        public long WFInstanceId { get; set; }
        [Column("WF_STATE_INSTANCE_ID")]
        public string WfStateInstanceId { get; set; }
        [Column("WF_ACTION_INSTANCE_ID")]
        public string WfActionInstanceId { get; set; }
        [Column("ASSIGNED_LOGIN_ID")]
        public string AssignedUserId { get; set; }
        [Column("ACTION_LOGIN_ID")]
        public string ActionUserId { get; set; }
        [Column("TASK_SCREEN")]
        public string TaskScreen { get; set; }
        [Column("WF_WORKFLOW_HISTORY_TYPE_CD")]
        public string FWfWorkFlowHistoryActionType { get; set; }
        [Column("ACTION_DATE")]
        public string ActionDate { get; set; }
        [Column("TASKCOMMENT")]
        public string TaskComment { get; set; }
        public override string ToString()
        {
            return this.TaskComment.ToString();
        }
    }
}