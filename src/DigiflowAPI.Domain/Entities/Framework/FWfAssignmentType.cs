﻿using DigiflowAPI.Domain.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_ASSIGNMENT_TYPE", Schema = "FRAMEWORK")]
    public class FWfAssignmentType
    {
        [Key]
        [Column("WF_ASSIGNMENT_TYPE_CD")]
        public string WfAssignmentTypeCd { get; set; }

        [Column("NAME")]
        public string Name { get; set; }

        [Column("DESCRIPTION")]
        public string Description { get; set; }

        [Column("OWNER_TYPE_CODE")]
        public string OwnerTypeCode { get; set; }

        public override string ToString()
        {
            return Name;
        }
    }
}
