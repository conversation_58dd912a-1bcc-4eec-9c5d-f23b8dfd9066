﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_EVENT_JMPTOSTATE_DEF", Schema = "FRAMEWORK")]
    public class FWfEventJumptostateDef
    {
        [Key]
        [Column("WF_EVENT_JUMPTOSTATE_DEF_ID")]
        public long WfEventJumptostateDefId { get; set; }

        [ForeignKeyMapping("ToStateDefId", typeof(FWfStateDef), "WF_STATE_DEF_ID", "ToStateDef")]
        [Column("TO_STATE_DEF_ID")]
        public long ToStateDefId { get; set; }
        public virtual FWfStateDef ToStateDef { get; set; }
    }
}
