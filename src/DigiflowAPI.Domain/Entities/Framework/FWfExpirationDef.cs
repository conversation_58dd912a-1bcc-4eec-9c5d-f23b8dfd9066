﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_EXPIRATION_DEF", Schema = "FRAMEWORK")]
    public class FWfExpirationDef
    {
        [Key]
        [Column("WF_EXPIRATION_DEF_ID")]
        public long WfExpirationDefId { get; set; }

        [Column("EXPIRE_DAY")]
        public int ExpireDay { get; set; }

        [Column("EXPIRE_HOUR")]
        public int ExpireHour { get; set; }

        [Column("OWNER_REF_ID")]
        public long? OwnerRefId { get; set; }

        [Column("REPEAT")]
        public bool? Repeat { get; set; }

        [ForeignKeyMapping("WfExpirationOwnerTypeCd", typeof(FWfExpirationOwnerType), "WF_EXPIRATION_OWNER_TYPE_CD", "WfExpirationOwnerType")]
        [Column("WF_EXPIRATION_OWNER_TYPE_CD")]
        public string WfExpirationOwnerTypeCd { get; set; }

        public virtual FWfExpirationOwnerType WfExpirationOwnerType { get; set; }

        public override string ToString()
        {
            return WfExpirationDefId.ToString();
        }
    }
}
