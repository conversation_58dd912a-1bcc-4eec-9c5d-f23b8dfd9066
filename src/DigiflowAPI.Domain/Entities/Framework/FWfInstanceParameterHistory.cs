﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_INSTANCE_PARAMETER_HISTORY", Schema = "FRAMEWORK")]
    public class FWfInstanceParameterHistory
    {
        [Key]
        [Column("WF_INSTANCE_PARAMETER_HISTORY_ID")]
        public long WfInstanceParameterHistoryId { get; set; }

        [Column("NAME")]
        public string Name { get; set; }

        [Column("VALUE")]
        public string Value { get; set; }
        public override string ToString()
        {
            return Name;
        }
    }
}
