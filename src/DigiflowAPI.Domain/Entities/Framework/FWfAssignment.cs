﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_ASSIGNMENT", Schema = "FRAMEWORK")]
    public class FWfAssignment
    {
        [Key]
        [Column("WF_ASSIGNMENT_ID")]
        public long WfAssignmentId { get; set; }

        [ForeignKeyMapping("WfAssignmentTypeCd", typeof(FWfAssignmentType), "WF_ASSIGNMENT_TYPE_CD", "WfAssignmentType")]
        [Column("WF_ASSIGNMENT_TYPE_CD")]
        public string WfAssignmentTypeCd { get; set; }

        [ForeignKeyMapping("WfAssignedTypeCd", typeof(FWfAssignedType), "WF_ASSIGNED_TYPE_CD", "WfAssignedType")]
        [Column("WF_ASSIGNED_TYPE_CD")]
        public string WfAssignedTypeCd { get; set; }

        [Column("ASSIGNMENT_OWNER_REF_ID")]
        public long AssignmentOwnerRefId { get; set; }

        [Column("ASSIGNED_OWNER_REF_ID")]
        public long AssignedOwnerRefId { get; set; }

        [Column("IS_DEF_ASSIGNMENT")]
        public bool? IsDefAssignment { get; set; }

        [Column("DENY")]
        public bool? Deny { get; set; }

        public virtual FWfAssignmentType WfAssignmentType { get; set; }
        public virtual FWfAssignedType WfAssignedType { get; set; }

        public override string ToString()
        {
            return WfAssignmentId.ToString();
        }
    }
}
