﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_WORKFLOW_DEF", Schema = "FRAMEWORK")]
    public class FWfWorkflowDef
    {
        [Key]
        [Column("WF_WORKFLOW_DEF_ID")]
        public long WfWorkflowDefId { get; set; }

        [Column("CAN_CANCEL")]
        public bool? CanCancel { get; set; }

        [Column("IS_ASYNC")]
        public bool? IsAsync { get; set; }

        [Column("IS_SUBFLOW")]
        public bool? IsSubflow { get; set; }

        [Column("NAME")]
        public string Name { get; set; }

        [Column("PRIORITY")]
        public long? Priority { get; set; }

        [Column("INSTANCE_DESCRIPTION_TEMPLATE")]
        public string InstanceDescriptionTemplate { get; set; }

        [Column("HAS_ENTITY_LOCK")]
        public bool? HasEntityLock { get; set; }

        [Column("DESCRIPTION")]
        public string Description { get; set; }

        [ForeignKeyMapping("InitialStateId", typeof(FWfStateDef), "WF_STATE_DEF_ID", "InitialState")]
        [Column("INITIAL_STATE_ID")]
        public long InitialStateId { get; set; }

        [Column("ENTITY_ASSEMBLY")]
        public string EntityAssembly { get; set; }

        [ForeignKeyMapping("WfVersionStatusTypeCd", typeof(FWfVersionStatusType), "WF_VERSION_STATUS_TYPE_CD", "WfVersionStatusType")]
        [Column("WF_VERSION_STATUS_TYPE_CD")]
        public string WfVersionStatusTypeCd { get; set; }

        [Column("CAN_FINALIZE")]
        public bool? CanFinalize { get; set; }

        [Column("VERSION_NUMBER")]
        public long VersionNumber { get; set; }

        public virtual FWfStateDef InitialState { get; set; }
        public virtual FWfVersionStatusType WfVersionStatusType { get; set; }

        public virtual ICollection<FWfStateDef> WfWorkflowDefFWfStateDefList { get; set; } = new List<FWfStateDef>();
        public virtual ICollection<FWfDefParameter> WfWorkflowDefFWfDefParameterList { get; set; } = new List<FWfDefParameter>();

        public override string ToString()
        {
            return WfWorkflowDefId.ToString();
        }
    }
}
