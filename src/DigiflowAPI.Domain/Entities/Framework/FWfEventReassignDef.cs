﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_EVENT_REASSIGN_DEF", Schema = "FRAMEWORK")]
    public class FWfEventReassignDef
    {
        [Key]
        [Column("WF_EVENT_REASSIGN_DEF_ID")]
        public long WfEventReassignDefId { get; set; }

        [ForeignKeyMapping("WfReassignTypeCd", typeof(FWfReassignType), "WF_REASSIGN_TYPE_CD", "WfReassignType")]
        [Column("WF_REASSIGN_TYPE_CD")]
        public string WfReassignTypeCd { get; set; }
        public virtual FWfReassignType WfReassignType { get; set; }

        [ForeignKeyMapping("WfAssignmentTypeCd", typeof(FWfAssignmentType), "WF_ASSIGNMENT_TYPE_CD", "WfAssignmentType")]
        [Column("WF_ASSIGNMENT_TYPE_CD")]
        public string WfAssignmentTypeCd { get; set; }
        public virtual FWfAssignmentType WfAssignmentType { get; set; }

        [ForeignKeyMapping("WfAssignedTypeCd", typeof(FWfAssignedType), "WF_ASSIGNED_TYPE_CD", "WfAssignedType")]
        [Column("WF_ASSIGNED_TYPE_CD")]
        public string WfAssignedTypeCd { get; set; }
        public virtual FWfAssignedType WfAssignedType { get; set; }

        [Column("ASSIGNMENT_OWNER_REF_ID")]
        public long AssignmentOwnerRefId { get; set; }

        [Column("ASSIGNED_OWNER_REF_ID")]
        public long AssignedOwnerRefId { get; set; }
    }
}
