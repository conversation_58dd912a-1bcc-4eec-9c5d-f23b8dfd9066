﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_EXPIRATION_INSTANCE", Schema = "FRAMEWORK")]
    public class FWfExpirationInstance
    {
        [Key]
        [Column("WF_EXPIRATION_INSTANCE_ID")]
        public string WfExpirationInstanceId { get; set; }

        [ForeignKeyMapping("WfExpirationStatusTypeCd", typeof(FWfExpirationStatusType), "WF_EXPIRATION_STATUS_TYPE_CD", "WfExpirationStatusType")]
        [Column("WF_EXPIRATION_STATUS_TYPE_CD")]
        public string WfExpirationStatusTypeCd { get; set; }

        [ForeignKeyMapping("WfExpirationDefId", typeof(FWfExpirationDef), "WF_EXPIRATION_DEF_ID", "WfExpirationDef")]
        [Column("WF_EXPIRATION_DEF_ID")]
        public long WfExpirationDefId { get; set; }

        [Column("CREATION_DATE")]
        public DateTime? CreationDate { get; set; }

        [Column("EXECUTION_DATE")]
        public DateTime? ExecutionDate { get; set; }

        [Column("OWNER_REF_ID")]
        public long OwnerRefId { get; set; }

        public virtual FWfExpirationStatusType WfExpirationStatusType { get; set; }
        public virtual FWfExpirationDef WfExpirationDef { get; set; }

        public override string ToString()
        {
            return WfExpirationInstanceId.ToString();
        }
    }
}
