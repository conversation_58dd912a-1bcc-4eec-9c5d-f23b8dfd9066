﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_STATE_DEF", Schema = "FRAMEWORK")]
    public class FWfStateDef
    {
        [Key]
        [Column("WF_STATE_DEF_ID")]
        public long WfStateDefId { get; set; }

        [Column("NAME")]
        public string Name { get; set; }

        [Column("DESCRIPTION")]
        public string Description { get; set; }

        [ForeignKeyMapping("WfWorkflowDefId", typeof(FWfWorkflowDef), "WF_WORKFLOW_DEF_ID", "WfWorkflowDef")]
        [Column("WF_WORKFLOW_DEF_ID")]
        public long WfWorkflowDefId { get; set; }

        [ForeignKeyMapping("WfStateTypeCd", typeof(FWfStateType), "WF_STATE_TYPE_CD", "WfStateType")]
        [Column("WF_STATE_TYPE_CD")]
        public string WfStateTypeCd { get; set; }

        public virtual FWfWorkflowDef WfWorkflowDef { get; set; }
        public virtual FWfStateType WfStateType { get; set; }

        public virtual ICollection<FWfActionDef> WfStateDefFWfActionDefList { get; set; }
        public virtual ICollection<FWfTransitionDef> FromStateDefFWfTransitionDefList { get; set; }
        public virtual ICollection<FWfTransitionDef> ToStateDefFWfTransitionDefList { get; set; }

        public override string ToString()
        {
            return Name;
        }
    }
}
