﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_PARAMETER_DATA_TYPE", Schema = "FRAMEWORK")]
    public class FWfParameterDataType
    {
        [Key]
        [Column("WF_PARAMETER_DATA_TYPE_CD")]
        public string WfParameterDataTypeCd { get; set; }

        [Column("NAME")]
        public string Name { get; set; }

        [Column("DESCRIPTION")]
        public string Description { get; set; }
        public override string ToString()
        {
            return Name;
        }
    }
}
