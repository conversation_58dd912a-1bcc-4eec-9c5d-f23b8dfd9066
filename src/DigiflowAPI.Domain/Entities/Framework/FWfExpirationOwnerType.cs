﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_EXPIRATION_OWNER_TYPE", Schema = "FRAMEWORK")]
    public class FWfExpirationOwnerType
    {
        [Key]
        [Column("WF_EXPIRATION_OWNER_TYPE_CD")]
        public string WfExpirationOwnerTypeCd { get; set; }

        [Column("NAME")]
        public string Name { get; set; }

        [Column("DESCRIPTION")]
        public string Description { get; set; }
        public override string ToString()
        {
            return Name;
        }
    }
}
