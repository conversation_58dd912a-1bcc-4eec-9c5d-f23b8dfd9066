﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_EVENT_NOTIFICATE_DEF", Schema = "FRAMEWORK")]
    public class FWfEventNotificationDef
    {
        [Key]
        [Column("WF_EVENT_NOTIFICATION_DEF_ID")]
        public string WfEventNotificationDefId { get; set; }

        [Column("TEMPLATE_ID")]
        public long? TemplateId { get; set; }

        [ForeignKeyMapping("WfNotificationTypeCd", typeof(FWfNotificationType), "WF_NOTIFICATION_TYPE_CD", "WfNotificationType")]
        [Column("WF_NOTIFICATION_TYPE_CD")]
        public string WfNotificationTypeCd { get; set; }
        public virtual FWfNotificationType WfNotificationType { get; set; }
    }
}
