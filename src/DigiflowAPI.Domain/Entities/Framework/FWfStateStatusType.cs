﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_STATE_STATUS_TYPE", Schema = "FRAMEWORK")]
    public class FWfStateStatusType
    {
        [Key]
        [Column("WF_STATE_STATUS_TYPE_CD")]
        public string WfStateStatusTypeCd { get; set; }

        [Column("NAME")]
        public string Name { get; set; }

        public override string ToString()
        {
            return Name;
        }
    }
}
