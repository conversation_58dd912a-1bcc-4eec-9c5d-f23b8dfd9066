﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_DELEGATION", Schema = "FRAMEWORK")]
    public class FWfDelegation
    {
        [Key]
        [Column("WF_DELEGATION_ID")]
        public long WfDelegationId { get; set; }

        [Column("DELEGATION_OWNER_REF_ID")]
        public long DelegationOwnerRefId { get; set; }

        [Column("DELEGATE_REF_ID")]
        public long DelegateRefId { get; set; }

        [Column("DELEGATION_START_DATE")]
        public DateTime DelegationStartDate { get; set; }

        [Column("DELEGATION_END_DATE")]
        public DateTime DelegationEndDate { get; set; }

        [Column("WORKFLOW_DEF_ID")]
        public long WorkflowDefId { get; set; }

        [Column("DELEGATION_COMMENT")]
        public string DelegationComment { get; set; }

        public override string ToString()
        {
            return DelegationComment;
        }
    }
}
