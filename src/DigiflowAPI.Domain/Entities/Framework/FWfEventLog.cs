﻿using DigiflowAPI.Domain.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_EVENT_LOG", Schema = "FRAMEWORK")]
    public class FWfEventLog
    {
        [Key]
        [Column("WF_EVENT_LOG_ID")]
        public long WfEventLogId { get; set; }

        [Column("TIME")]
        public DateTime? Time { get; set; }

        [Column("OWNER_REF_ID")]
        public long OwnerRefId { get; set; }

        [ForeignKeyMapping("WfEventDefId", typeof(FWfEventDef), "WF_EVENT_DEF_ID", "WfEventDef")]
        [Column("WF_EVENT_DEF_ID")]
        public long WfEventDefId { get; set; }

        public virtual FWfEventDef WfEventDef { get; set; }

        public override string ToString()
        {
            return WfEventLogId.ToString();
        }
    }
}
