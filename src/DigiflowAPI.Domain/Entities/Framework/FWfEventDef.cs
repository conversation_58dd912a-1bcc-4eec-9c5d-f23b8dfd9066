﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_EVENT_DEF", Schema = "FRAMEWORK")]
    public class FWfEventDef
    {
        [Key]
        [Column("WF_EVENT_DEF_ID")]
        public long WfEventDefId { get; set; }

        [Column("OWNER_REF_ID")]
        public long? OwnerRefId { get; set; }

        [Column("ORDER_NO")]
        public long OrderNo { get; set; }

        [ForeignKeyMapping("WfEventTypeCd", typeof(FWfEventType), "WF_EVENT_TYPE_CD", "WfEventType")]
        [Column("WF_EVENT_TYPE_CD")]
        public string WfEventTypeCd { get; set; }
        public virtual FWfEventType WfEventType { get; set; }

        [ForeignKeyMapping("WfEventOwnerTypeCd", typeof(FWfEventOwnerType), "WF_EVENT_OWNER_TYPE_CD", "WfEventOwnerType")]
        [Column("WF_EVENT_OWNER_TYPE_CD")]
        public string WfEventOwnerTypeCd { get; set; }
        public virtual FWfEventOwnerType WfEventOwnerType { get; set; }

        public override string ToString()
        {
            return WfEventDefId.ToString();
        }
    }
}
