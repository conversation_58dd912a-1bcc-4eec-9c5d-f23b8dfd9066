﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_STATE_INSTANCE", Schema = "FRAMEWORK")]
    public class FWfStateInstance
    {
        [Key]
        [Column("WF_STATE_INSTANCE_ID")]
        public long WfStateInstanceId { get; set; }

        [ForeignKeyMapping("WfWorkflowInstanceId", typeof(FWfWorkflowInstance), "WF_WORKFLOW_INSTANCE_ID", "WfWorkflowInstance")]
        [Column("WF_WORKFLOW_INSTANCE_ID")]
        public long WfWorkflowInstanceId { get; set; }

        public virtual FWfWorkflowInstance WfWorkflowInstance { get; set; }

        [Column("WF_STATE_STATUS_TYPE_CD")]
        public string WfStateStatusTypeCd { get; set; }

        [ForeignKeyMapping("WfStateDefId", typeof(FWfStateDef), "WF_STATE_DEF_ID", "WfStateDef")]
        [Column("WF_STATE_DEF_ID")]
        public long WfStateDefId { get; set; }

        [Column("WF_CURRENT_ACTION_TYPE_CD")]
        public string WfCurrentActionTypeCd { get; set; }

        [ForeignKeyMapping("WfCurrentActionInstanceId", typeof(FWfActionInstance), "WF_ACTION_INSTANCE_ID","WfCurrentActionInstance")]
        [Column("WF_CURRENT_ACTION_INSTANCE_ID")]
        public long WfCurrentActionInstanceId { get; set; }

        [Column("START_TIME")]
        public DateTime StartTime { get; set; }

        [Column("END_TIME")]
        public DateTime? EndTime { get; set; }

        public virtual FWfStateDef WfStateDef { get; set; }

        public virtual FWfActionInstance WfCurrentActionInstance { get; set; }

    }
}
