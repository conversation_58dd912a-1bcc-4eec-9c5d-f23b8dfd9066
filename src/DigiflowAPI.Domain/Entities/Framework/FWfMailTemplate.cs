﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_MAIL_TEMPLATE", Schema = "FRAMEWORK")]
    public class FWfMailTemplate
    {
        [Key]
        [Column("WF_MAIL_TEMPLATE_ID")]
        public long WfMailTemplateId { get; set; }

        [Column("SUBJECT")]
        public string Subject { get; set; }

        [Column("SOURCE")]
        public string Source { get; set; }

        [Column("DESCRIPTION")]
        public string Description { get; set; }
    }
}
