﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_EVENT_CLASS_DEF", Schema = "FRAMEWORK")]
    public class FWfEventClassDef
    {
        [Key]
        [Column("WF_EVENT_CLASS_DEF_ID")]
        public long WfEventClassDefId { get; set; }

        [Column("EXECUTION_CLASS")]
        public string ExecutionClass { get; set; }
    }
}
