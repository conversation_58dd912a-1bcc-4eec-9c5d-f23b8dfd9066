﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_DEF_PARAMETER", Schema = "FRAMEWORK")]
    public class FWfDefParameter
    {
        [Key]
        [Column("WF_DEF_PARAMETER_ID")]
        public long WfDefParameterId { get; set; }

        [ForeignKeyMapping("WfWorkflowDefId", typeof(FWfWorkflowDef), "WF_WORKFLOW_DEF_ID", "WfWorkflowDef")]
        [Column("WF_WORKFLOW_DEF_ID")]
        public long? WfWorkflowDefId { get; set; }

        [Column("NAME")]
        public string Name { get; set; }

        [Column("VALUE")]
        public string Value { get; set; }

        [ForeignKeyMapping("WfParameterDataTypeCd", typeof(FWfParameterDataType), "WF_PARAMETER_DATA_TYPE_CD", "WfParameterDataType")]
        [Column("WF_PARAMETER_DATA_TYPE_CD")]
        public string WfParameterDataTypeCd { get; set; }

        public virtual FWfWorkflowDef WfWorkflowDef { get; set; }
        public virtual FWfParameterDataType WfParameterDataType { get; set; }

        public override string ToString()
        {
            return Name;
        }
    }
}
