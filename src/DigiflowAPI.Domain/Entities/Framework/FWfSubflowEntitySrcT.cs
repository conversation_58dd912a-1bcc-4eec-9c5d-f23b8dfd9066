﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_SUBFLOW_ENTITY_SRC_T", Schema = "FRAMEWORK")]
    public class FWfSubflowEntitySrcT
    {
        [Key]
        [Column("WF_SUBFLOW_ENTITY_SRC_T_CD")]
        public string WfSubflowEntitySrcTCd { get; set; }

        [Column("NAME")]
        public string Name { get; set; }

        [Column("DESCRIPTION")]
        public string Description { get; set; }

        public override string ToString()
        {
            return Name;
        }
    }
}
