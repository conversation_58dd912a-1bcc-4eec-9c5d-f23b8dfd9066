﻿using DigiflowAPI.Domain.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Entities.Framework
{
    [Table("F_WF_WORKFLOW_INSTANCE", Schema = "FRAMEWORK")]
    public class FWfWorkflowInstance
    {
        [Key]
        [Column("WF_WORKFLOW_INSTANCE_ID")]
        public long WfWorkflowInstanceId { get; set; }

        [Column("END_TIME")]
        public DateTime? EndTime { get; set; }

        [Column("PRIORITY")]
        public long? Priority { get; set; }

        [ForeignKeyMapping("WfCurrentStateId", typeof(FWfStateInstance), "WF_STATE_INSTANCE_ID", "WfCurrentState")]
        [Column("WF_CURRENT_STATE_ID")]
        public long? WfCurrentStateId { get; set; }

        [Column("ENTITY_REF_ID")]
        public long? EntityRefId { get; set; }

        [Column("DESCRIPTION")]
        public string Description { get; set; }

        [ForeignKeyMapping("WfWorkflowDefId", typeof(FWfWorkflowDef), "WF_WORKFLOW_DEF_ID", "WfWorkflowDef")]
        [Column("WF_WORKFLOW_DEF_ID")]
        public long? WfWorkflowDefId { get; set; }

        [ForeignKeyMapping("OwnerLoginId", typeof(FLogin), "LOGIN_ID", "OwnerLogin")]
        [Column("OWNER_LOGIN_ID")]
        public long? OwnerLoginId { get; set; }

        [ForeignKeyMapping("ParentActionId", typeof(FWfActionSubflowIns), "WF_ACTION_SUBFLOW_INS_ID", "ParentAction")]
        [Column("PARENT_ACTION_ID")]
        public int? ParentActionId { get; set; }

        [Column("START_TIME")]
        public DateTime? StartTime { get; set; }

        [ForeignKeyMapping("WfWorkflowStatusTypeCd", typeof(FWfWorkflowStatusType), "WF_WORKFLOW_STATUS_TYPE_CD", "WfWorkflowStatusType")]
        [Column("WF_WORKFLOW_STATUS_TYPE_CD")]
        public string WfWorkflowStatusTypeCd { get; set; }

        [Column("IS_SUBFLOW")]
        public bool? IsSubflow { get; set; }

        [Column("USE_INSTANCE_ASSIGNMENT")]
        public bool? UseInstanceAssignment { get; set; }

        public virtual FWfStateInstance WfCurrentState { get; set; }
        public virtual FWfWorkflowDef WfWorkflowDef { get; set; }
        public virtual FLogin OwnerLogin { get; set; }
        public virtual FWfActionSubflowIns ParentAction { get; set; }
        public virtual FWfWorkflowStatusType WfWorkflowStatusType { get; set; }
        public virtual ICollection<FWfInstanceParameter> WfWorkflowInstanceFWfInstanceParameterList { get; set; } = new List<FWfInstanceParameter>();
        public virtual ICollection<FWfStateInstance> WfWorkflowInstanceFWfStateInstanceList { get; set; } = new List<FWfStateInstance>();

        public override string ToString()
        {
            return WfWorkflowInstanceId.ToString();
        }
    }
}
