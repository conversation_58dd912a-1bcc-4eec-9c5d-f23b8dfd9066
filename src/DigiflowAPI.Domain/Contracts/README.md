# Domain Contracts

This directory contains interfaces and contracts that define the ports for the application.
These contracts follow the Dependency Inversion Principle (DIP) where high-level modules define the interfaces that low-level modules implement.

## Structure

- `Data/` - Data access contracts (repositories, unit of work)
- `Services/` - Domain service contracts
- `Infrastructure/` - Infrastructure service contracts (email, file storage, etc.)
- `Security/` - Security and authentication contracts
- `Configuration/` - Configuration contracts

## Principles

1. No dependencies on external libraries
2. No implementation details
3. Only abstractions and contracts
4. Can reference other Domain types