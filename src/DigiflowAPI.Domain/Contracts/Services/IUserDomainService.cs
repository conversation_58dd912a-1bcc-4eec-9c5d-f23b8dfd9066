using DigiflowAPI.Domain.Entities;
using DigiflowAPI.Domain.Entities.Framework;

namespace DigiflowAPI.Domain.Contracts.Services
{
    /// <summary>
    /// Domain-level user service interface
    /// </summary>
    public interface IUserDomainService
    {
        Task<DpHrUsers?> GetUserInfo(long? userId = null);
        Task<long> GetCurrentUserId();
        Task<FLogin?> GetFLoginInfo(long? loginId);
        Task<string> GetActiveUserNameAsync();
        Task<long> GetActiveUserIdAsync();
        Task<DpHrDeps?> GetDepartmentById(decimal departmentId);
        Task<IEnumerable<DpHrDeps>> GetDepartmentsAtLevel(long ustBolumId);
        Task<long> GetUserDepartmentId(long userId);
        Task<IEnumerable<VwUserInformation>> GetUsersByDepartmentId(long departmentId, bool excludeActiveUser = false);
        Task<IEnumerable<VwUserInformation>> GetForwardPersonel(string username);
        Task<IEnumerable<VwUserInformation?>> GetAllAsync();
        Task<VwUserInformation?> GetByIdAsync(long id);
        Task<VwUserInformation?> GetByUsernameAsync(string username);
    }
}