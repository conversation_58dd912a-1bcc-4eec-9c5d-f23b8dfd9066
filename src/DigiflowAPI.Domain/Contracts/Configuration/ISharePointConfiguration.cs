namespace DigiflowAPI.Domain.Contracts.Configuration
{
  /// <summary>
  /// Interface for SharePoint configuration
  /// </summary>
  public interface ISharePointConfiguration
  {
    /// <summary>
    /// Loads SharePoint paths from configuration
    /// </summary>
    void LoadSharePointPaths();

    /// <summary>
    /// Gets SharePoint path by key
    /// </summary>
    /// <param name="key">Path key</param>
    /// <returns>SharePoint path</returns>
    string GetSharePointPath(string key);
  }
}
