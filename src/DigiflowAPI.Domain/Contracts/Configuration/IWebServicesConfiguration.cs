namespace DigiflowAPI.Domain.Contracts.Configuration
{
  /// <summary>
  /// Interface for Web Services configuration
  /// </summary>
  public interface IWebServicesConfiguration
  {
    /// <summary>
    /// Whether to use proxy
    /// </summary>
    bool IsProxyUsing { get; }

    /// <summary>
    /// Whether to use credentials
    /// </summary>
    bool IsCredentialUsing { get; }

    /// <summary>
    /// User name for authentication
    /// </summary>
    string UserName { get; }

    /// <summary>
    /// Password for authentication
    /// </summary>
    string Password { get; }

    /// <summary>
    /// Domain for authentication
    /// </summary>
    string Domain { get; }

    /// <summary>
    /// Proxy services IP address
    /// </summary>
    string ProxyServicesIp { get; }
  }
}
