namespace DigiflowAPI.Domain.Contracts.Data
{
    /// <summary>
    /// Generic data access repository interface without implementation-specific dependencies
    /// </summary>
    public interface IDataAccessRepository
    {
        Task<List<T>> ExecuteQueryAsync<T>(string sql, IDictionary<string, object>? parameters = null) where T : new();
        Task<List<T>> ExecuteQueryAsync<T>(string sql, Func<IDataReader, T> mapFunction, IDictionary<string, object>? parameters = null);
        Task<T?> ExecuteSingleQueryAsync<T>(string sql, IDictionary<string, object>? parameters = null) where T : new();
        Task<T?> ExecuteSingleQueryAsync<T>(string sql, Func<IDataReader, T> mapFunction, IDictionary<string, object>? parameters = null);
        Task<T?> ExecuteScalarAsync<T>(string sql, IDictionary<string, object>? parameters = null);
        Task<T?> ExecuteScalarAsyncForValueType<T>(string sql, IDictionary<string, object>? parameters = null) where T : struct;
        Task<int> ExecuteUpdateAsync(string sql, IDictionary<string, object>? parameters = null);
        Task<T?> ExecuteInsertAsync<T>(string sql, IDictionary<string, object>? parameters = null);
        Task<T?> GetEntityAsync<T>(object key) where T : class, new();
        Task<T?> GetEntityAsync<T>(string keyField, object keyValue) where T : class, new();
        Task<T?> GetInheritedEntityAsync<T>(object key) where T : class, new();
        Task<T> ExecuteProcedureAsync<T>(string procedureName, IDictionary<string, object>? parameters);
        Task ExecuteProcedureAsync(string procedureName, IDictionary<string, object>? parameters);
        Task<object?> GetEntityAsync(Type entityType, string keyField, object keyValue);
        Task<List<T>> GetEntityListAsync<T>() where T : class, new();
        Task<List<T>> GetEntityListAsync<T>(IDictionary<string, object> filters) where T : class, new();
        Task<List<T>> GetInheritedEntityListAsync<T>() where T : class, new();
    }
    
    /// <summary>
    /// Data reader abstraction to avoid database-specific dependencies
    /// </summary>
    public interface IDataReader
    {
        object this[string name] { get; }
        object this[int ordinal] { get; }
        bool Read();
        bool IsDBNull(int ordinal);
        int GetOrdinal(string name);
        string GetString(int ordinal);
        int GetInt32(int ordinal);
        long GetInt64(int ordinal);
        decimal GetDecimal(int ordinal);
        DateTime GetDateTime(int ordinal);
        bool GetBoolean(int ordinal);
        object GetValue(int ordinal);
    }
}