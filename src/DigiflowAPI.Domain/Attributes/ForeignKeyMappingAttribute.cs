﻿using System;

namespace DigiflowAPI.Domain.Attributes
{
    [AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
    public class ForeignKeyMappingAttribute : Attribute
    {
        public string ForeignKey { get; }
        public Type ForeignTable { get; }
        public string ForeignField { get; }
        public string VirtualField { get; }

        public ForeignKeyMappingAttribute(string foreignKey, Type foreignTable, string foreignField, string virtualField)
        {
            ForeignKey = foreignKey;
            ForeignTable = foreignTable;
            ForeignField = foreignField;
            VirtualField = virtualField;
        }
    }
}
