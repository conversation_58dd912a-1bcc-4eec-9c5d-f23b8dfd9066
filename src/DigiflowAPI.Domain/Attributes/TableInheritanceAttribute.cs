﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiflowAPI.Domain.Attributes
{
    [AttributeUsage(AttributeTargets.Class, Inherited = false, AllowMultiple = false)]
    public class TableInheritanceAttribute : Attribute
    {
        public Type ParentType { get; }

        public TableInheritanceAttribute(Type parentType)
        {
            ParentType = parentType;
        }
    }
}
