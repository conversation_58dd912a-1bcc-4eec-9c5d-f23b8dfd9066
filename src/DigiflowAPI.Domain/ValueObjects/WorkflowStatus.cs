using DigiflowAPI.Domain.Common;
using DigiflowAPI.Domain.Exceptions;

namespace DigiflowAPI.Domain.ValueObjects;

/// <summary>
/// Represents the status of a workflow instance.
/// </summary>
public class WorkflowStatus : ValueObject
{
    public string Code { get; }
    public string Name { get; }
    
    // Common workflow statuses
    public static readonly WorkflowStatus Active = new("ACTIVE", "Active");
    public static readonly WorkflowStatus Completed = new("COMPLETED", "Completed");
    public static readonly WorkflowStatus Cancelled = new("CANCELLED", "Cancelled");
    public static readonly WorkflowStatus Suspended = new("SUSPENDED", "Suspended");
    public static readonly WorkflowStatus Failed = new("FAILED", "Failed");
    
    private WorkflowStatus(string code, string name)
    {
        Code = code;
        Name = name;
    }
    
    public static WorkflowStatus Create(string code, string name = null)
    {
        if (string.IsNullOrWhiteSpace(code))
        {
            throw new DomainValidationException("Workflow status code cannot be empty.");
        }
        
        return new WorkflowStatus(code.ToUpper(), name ?? code);
    }
    
    public static WorkflowStatus FromCode(string code)
    {
        if (string.IsNullOrWhiteSpace(code))
        {
            throw new DomainValidationException("Workflow status code cannot be empty.");
        }
        
        return code.ToUpper() switch
        {
            "ACTIVE" => Active,
            "COMPLETED" => Completed,
            "CANCELLED" => Cancelled,
            "SUSPENDED" => Suspended,
            "FAILED" => Failed,
            _ => Create(code)
        };
    }
    
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return Code;
    }
    
    public override string ToString() => Code;
}