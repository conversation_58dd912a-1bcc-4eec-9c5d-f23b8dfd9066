using System.Text.RegularExpressions;
using DigiflowAPI.Domain.Common;
using DigiflowAPI.Domain.Exceptions;

namespace DigiflowAPI.Domain.ValueObjects;

/// <summary>
/// Represents a valid email address.
/// </summary>
public class EmailAddress : ValueObject
{
    private static readonly Regex EmailRegex = new(
        @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
        RegexOptions.Compiled | RegexOptions.IgnoreCase);
    
    public string Value { get; }
    
    private EmailAddress(string value)
    {
        Value = value;
    }
    
    public static EmailAddress Create(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
        {
            throw new DomainValidationException("Email address cannot be empty.");
        }
        
        email = email.Trim().ToLower();
        
        if (!EmailRegex.IsMatch(email))
        {
            throw new DomainValidationException($"'{email}' is not a valid email address.");
        }
        
        return new EmailAddress(email);
    }
    
    public static EmailAddress CreateOrNull(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
        {
            return null;
        }
        
        try
        {
            return Create(email);
        }
        catch
        {
            return null;
        }
    }
    
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return Value;
    }
    
    public override string ToString() => Value;
    
    public static implicit operator string(EmailAddress email) => email?.Value;
    public static explicit operator EmailAddress(string email) => Create(email);
}