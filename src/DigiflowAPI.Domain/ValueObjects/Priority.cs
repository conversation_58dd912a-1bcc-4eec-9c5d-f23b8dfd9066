using DigiflowAPI.Domain.Common;
using DigiflowAPI.Domain.Exceptions;

namespace DigiflowAPI.Domain.ValueObjects;

/// <summary>
/// Represents the priority of a workflow or task.
/// </summary>
public class Priority : ValueObject
{
    public int Value { get; }
    public string Name { get; }
    
    // Common priority levels
    public static readonly Priority Low = new(1, "Low");
    public static readonly Priority Normal = new(2, "Normal");
    public static readonly Priority High = new(3, "High");
    public static readonly Priority Urgent = new(4, "Urgent");
    public static readonly Priority Critical = new(5, "Critical");
    
    private Priority(int value, string name)
    {
        Value = value;
        Name = name;
    }
    
    public static Priority Create(int value)
    {
        if (value < 1 || value > 5)
        {
            throw new DomainValidationException("Priority value must be between 1 and 5.");
        }
        
        return value switch
        {
            1 => Low,
            2 => Normal,
            3 => High,
            4 => Urgent,
            5 => Critical,
            _ => new Priority(value, $"Priority {value}")
        };
    }
    
    public static Priority FromValue(long? value)
    {
        if (!value.HasValue)
        {
            return Normal; // Default priority
        }
        
        return Create((int)value.Value);
    }
    
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return Value;
    }
    
    public override string ToString() => $"{Name} ({Value})";
    
    public static implicit operator int(Priority priority) => priority.Value;
    public static explicit operator Priority(int value) => Create(value);
}