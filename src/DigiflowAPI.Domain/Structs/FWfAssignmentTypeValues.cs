﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiflowAPI.Domain.Structs
{
    public struct FWfAssignmentTypeValues
    {
        public const string WorkflowModify = "WFMODIFY";

        public const string CommentActionTask = "TASKCOMMENT";

        public const string TaskInbox = "TASKINBOX";

        public const string MailTo = "MAILACTIONTO";

        public const string MailCC = "MAILACTIONCC";

        public const string EventNotificationTo = "EVENTNOTIFICATIONTO";

        public const string EventNotificationCC = "EVENTNOTIFICATIONCC";

        public const string EventEscalation = "EVENTESCALATION";
    }
}
