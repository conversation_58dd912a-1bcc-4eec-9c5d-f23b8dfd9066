﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiflowAPI.Domain.Structs
{
    public struct FWfActionStatusTypeValues
    {
        /// <summary>
        /// Hareket örneği oluşturulmu henü<PERSON> çalıştırılmamış
        /// </summary>
        public const string Created = "CREATED";
        /// <summary>
        /// Hareket örneği çalışıyor
        /// </summary>
        public const string Started = "STARTED";
        /// <summary>
        /// Hareket örneği çalışmasını tamalamış
        /// </summary>
        public const string Completed = "COMPLETED";
        /// <summary>
        /// Hareket örneği herhangi bir nedenle durdurulup varsa bir sonraki Harekete geçilmiş
        /// </summary>
        public const string ByPassed = "BYPASSED";

    }
}
