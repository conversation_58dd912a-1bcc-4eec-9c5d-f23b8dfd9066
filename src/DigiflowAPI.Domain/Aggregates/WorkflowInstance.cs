using DigiflowAPI.Domain.Common;
using DigiflowAPI.Domain.Events;
using DigiflowAPI.Domain.Exceptions;
using DigiflowAPI.Domain.ValueObjects;
using DigiflowAPI.Domain.Entities.Framework;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Aggregates;

/// <summary>
/// Workflow instance aggregate root - represents a running instance of a workflow definition.
/// </summary>
[Table("F_WF_WORKFLOW_INSTANCE", Schema = "FRAMEWORK")]
public class WorkflowInstance : AggregateRoot
{
    private readonly List<WorkflowState> _states = new();
    private readonly List<WorkflowParameter> _parameters = new();
    
    [Column("WF_WORKFLOW_INSTANCE_ID")]
    public override long Id { get; set; }
    
    [Column("WF_WORKFLOW_DEF_ID")]
    public long WorkflowDefinitionId { get; private set; }
    
    [Column("DESCRIPTION")]
    public string Description { get; private set; }
    
    [Column("PRIORITY")]
    private long? _priorityValue { get; set; }
    
    public Priority Priority => Priority.FromValue(_priorityValue);
    
    [Column("WF_WORKFLOW_STATUS_TYPE_CD")]
    private string _statusCode { get; set; }
    
    public WorkflowStatus Status => WorkflowStatus.FromCode(_statusCode);
    
    [Column("WF_CURRENT_STATE_ID")]
    public long? CurrentStateId { get; private set; }
    
    [Column("OWNER_LOGIN_ID")]
    public long OwnerLoginId { get; private set; }
    
    [Column("START_TIME")]
    public DateTime StartTime { get; private set; }
    
    [Column("END_TIME")]
    public DateTime? EndTime { get; private set; }
    
    [Column("ENTITY_REF_ID")]
    public long? EntityReferenceId { get; private set; }
    
    [Column("IS_SUBFLOW")]
    public bool IsSubflow { get; private set; }
    
    [Column("PARENT_ACTION_ID")]
    public int? ParentActionId { get; private set; }
    
    public IReadOnlyList<WorkflowState> States => _states.AsReadOnly();
    public IReadOnlyList<WorkflowParameter> Parameters => _parameters.AsReadOnly();
    
    // Required for EF
    protected WorkflowInstance() { }
    
    /// <summary>
    /// Creates a new workflow instance.
    /// </summary>
    public static WorkflowInstance Create(
        long workflowDefinitionId,
        string description,
        long ownerLoginId,
        Priority priority = null,
        long? entityReferenceId = null,
        bool isSubflow = false,
        int? parentActionId = null)
    {
        if (workflowDefinitionId <= 0)
            throw new DomainValidationException("Workflow definition ID must be positive.");
            
        if (string.IsNullOrWhiteSpace(description))
            throw new DomainValidationException("Workflow description cannot be empty.");
            
        if (ownerLoginId <= 0)
            throw new DomainValidationException("Owner login ID must be positive.");
            
        if (isSubflow && !parentActionId.HasValue)
            throw new DomainValidationException("Subflows must have a parent action ID.");
        
        var instance = new WorkflowInstance
        {
            WorkflowDefinitionId = workflowDefinitionId,
            Description = description,
            OwnerLoginId = ownerLoginId,
            _priorityValue = priority?.Value ?? Priority.Normal.Value,
            _statusCode = WorkflowStatus.Active.Code,
            StartTime = DateTime.UtcNow,
            EntityReferenceId = entityReferenceId,
            IsSubflow = isSubflow,
            ParentActionId = parentActionId
        };
        
        instance.AddDomainEvent(new WorkflowStartedEvent(
            instance.Id,
            workflowDefinitionId,
            ownerLoginId,
            instance.StartTime,
            description));
        
        return instance;
    }
    
    /// <summary>
    /// Transitions the workflow to a new state.
    /// </summary>
    public void TransitionToState(long newStateId, long transitionedByUserId, string reason = null)
    {
        if (Status != WorkflowStatus.Active)
            throw new WorkflowException($"Cannot transition workflow in {Status} status.");
            
        if (newStateId <= 0)
            throw new DomainValidationException("State ID must be positive.");
        
        var previousStateId = CurrentStateId ?? 0;
        CurrentStateId = newStateId;
        LastUpdated = DateTime.UtcNow;
        LastUpdatedBy = transitionedByUserId;
        
        AddDomainEvent(new WorkflowStateChangedEvent(
            Id,
            previousStateId,
            newStateId,
            DateTime.UtcNow,
            transitionedByUserId.ToString(),
            reason));
    }
    
    /// <summary>
    /// Completes the workflow.
    /// </summary>
    public void Complete(long completedByUserId)
    {
        if (Status != WorkflowStatus.Active)
            throw new WorkflowException($"Cannot complete workflow in {Status} status.");
        
        _statusCode = WorkflowStatus.Completed.Code;
        EndTime = DateTime.UtcNow;
        LastUpdated = DateTime.UtcNow;
        LastUpdatedBy = completedByUserId;
        
        AddDomainEvent(new WorkflowCompletedEvent(
            Id,
            EndTime.Value,
            completedByUserId.ToString()));
    }
    
    /// <summary>
    /// Cancels the workflow.
    /// </summary>
    public void Cancel(long cancelledByUserId, string reason)
    {
        if (Status == WorkflowStatus.Completed || Status == WorkflowStatus.Cancelled)
            throw new WorkflowException($"Cannot cancel workflow in {Status} status.");
        
        _statusCode = WorkflowStatus.Cancelled.Code;
        EndTime = DateTime.UtcNow;
        LastUpdated = DateTime.UtcNow;
        LastUpdatedBy = cancelledByUserId;
        
        // Could add a WorkflowCancelledEvent here
    }
    
    /// <summary>
    /// Suspends the workflow.
    /// </summary>
    public void Suspend(long suspendedByUserId, string reason)
    {
        if (Status != WorkflowStatus.Active)
            throw new WorkflowException($"Cannot suspend workflow in {Status} status.");
        
        _statusCode = WorkflowStatus.Suspended.Code;
        LastUpdated = DateTime.UtcNow;
        LastUpdatedBy = suspendedByUserId;
        
        // Could add a WorkflowSuspendedEvent here
    }
    
    /// <summary>
    /// Resumes a suspended workflow.
    /// </summary>
    public void Resume(long resumedByUserId)
    {
        if (Status != WorkflowStatus.Suspended)
            throw new WorkflowException("Can only resume suspended workflows.");
        
        _statusCode = WorkflowStatus.Active.Code;
        LastUpdated = DateTime.UtcNow;
        LastUpdatedBy = resumedByUserId;
        
        // Could add a WorkflowResumedEvent here
    }
    
    /// <summary>
    /// Updates the workflow priority.
    /// </summary>
    public void UpdatePriority(Priority newPriority, long updatedByUserId)
    {
        if (newPriority == null)
            throw new DomainValidationException("Priority cannot be null.");
            
        if (Status == WorkflowStatus.Completed || Status == WorkflowStatus.Cancelled)
            throw new WorkflowException($"Cannot update priority for workflow in {Status} status.");
        
        _priorityValue = newPriority.Value;
        LastUpdated = DateTime.UtcNow;
        LastUpdatedBy = updatedByUserId;
    }
    
    /// <summary>
    /// Adds a parameter to the workflow.
    /// </summary>
    public void AddParameter(string name, string value, long addedByUserId)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new DomainValidationException("Parameter name cannot be empty.");
        
        var parameter = WorkflowParameter.Create(Id, name, value);
        _parameters.Add(parameter);
        
        LastUpdated = DateTime.UtcNow;
        LastUpdatedBy = addedByUserId;
    }
    
    /// <summary>
    /// Updates an existing parameter value.
    /// </summary>
    public void UpdateParameter(string name, string newValue, long updatedByUserId)
    {
        var parameter = _parameters.FirstOrDefault(p => p.Name == name);
        if (parameter == null)
            throw new DomainValidationException($"Parameter '{name}' not found.");
        
        parameter.UpdateValue(newValue);
        
        LastUpdated = DateTime.UtcNow;
        LastUpdatedBy = updatedByUserId;
    }
}

/// <summary>
/// Represents a state in the workflow.
/// </summary>
public class WorkflowState : BaseEntity
{
    public long WorkflowInstanceId { get; private set; }
    public long StateDefinitionId { get; private set; }
    public DateTime EnteredTime { get; private set; }
    public DateTime? ExitedTime { get; private set; }
    public bool IsActive => !ExitedTime.HasValue;
    
    protected WorkflowState() { }
    
    internal static WorkflowState Create(long workflowInstanceId, long stateDefinitionId)
    {
        return new WorkflowState
        {
            WorkflowInstanceId = workflowInstanceId,
            StateDefinitionId = stateDefinitionId,
            EnteredTime = DateTime.UtcNow
        };
    }
    
    internal void Exit()
    {
        if (ExitedTime.HasValue)
            throw new DomainValidationException("State has already been exited.");
            
        ExitedTime = DateTime.UtcNow;
    }
}

/// <summary>
/// Represents a workflow parameter.
/// </summary>
public class WorkflowParameter : BaseEntity
{
    public long WorkflowInstanceId { get; private set; }
    public string Name { get; private set; }
    public string Value { get; private set; }
    
    protected WorkflowParameter() { }
    
    internal static WorkflowParameter Create(long workflowInstanceId, string name, string value)
    {
        return new WorkflowParameter
        {
            WorkflowInstanceId = workflowInstanceId,
            Name = name,
            Value = value
        };
    }
    
    internal void UpdateValue(string newValue)
    {
        Value = newValue;
        LastUpdated = DateTime.UtcNow;
    }
}