namespace DigiflowAPI.Domain.Events;

/// <summary>
/// Domain event raised when a workflow is started.
/// </summary>
public class WorkflowStartedEvent : IDomainEvent
{
    public long WorkflowInstanceId { get; }
    public long WorkflowDefinitionId { get; }
    public long InitiatorUserId { get; }
    public DateTime StartTime { get; }
    public string Description { get; }
    public DateTime OccurredOn { get; }
    
    public WorkflowStartedEvent(
        long workflowInstanceId,
        long workflowDefinitionId,
        long initiatorUserId,
        DateTime startTime,
        string description)
    {
        WorkflowInstanceId = workflowInstanceId;
        WorkflowDefinitionId = workflowDefinitionId;
        InitiatorUserId = initiatorUserId;
        StartTime = startTime;
        Description = description;
        OccurredOn = DateTime.UtcNow;
    }
}