namespace DigiflowAPI.Domain.Events;

/// <summary>
/// Domain event raised when a workflow is completed.
/// </summary>
public class WorkflowCompletedEvent : IDomainEvent
{
    public long WorkflowInstanceId { get; }
    public DateTime CompletionTime { get; }
    public string CompletedByUserId { get; }
    public DateTime OccurredOn { get; }
    
    public WorkflowCompletedEvent(
        long workflowInstanceId,
        DateTime completionTime,
        string completedByUserId)
    {
        WorkflowInstanceId = workflowInstanceId;
        CompletionTime = completionTime;
        CompletedByUserId = completedByUserId;
        OccurredOn = DateTime.UtcNow;
    }
}