namespace DigiflowAPI.Domain.Events;

/// <summary>
/// Domain event raised when a workflow transitions to a new state.
/// </summary>
public class WorkflowStateChangedEvent : IDomainEvent
{
    public long WorkflowInstanceId { get; }
    public long PreviousStateId { get; }
    public long NewStateId { get; }
    public DateTime TransitionTime { get; }
    public string TransitionedByUserId { get; }
    public string Reason { get; }
    public DateTime OccurredOn { get; }
    
    public WorkflowStateChangedEvent(
        long workflowInstanceId,
        long previousStateId,
        long newStateId,
        DateTime transitionTime,
        string transitionedByUserId,
        string reason = null)
    {
        WorkflowInstanceId = workflowInstanceId;
        PreviousStateId = previousStateId;
        NewStateId = newStateId;
        TransitionTime = transitionTime;
        TransitionedByUserId = transitionedByUserId;
        Reason = reason;
        OccurredOn = DateTime.UtcNow;
    }
}