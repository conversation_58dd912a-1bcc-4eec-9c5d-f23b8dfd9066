using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.Common
{
  public abstract class BaseEntity
  {
    [Key]
    public virtual long Id { get; set; }

    [Column("CREATED")]
    public DateTime Created { get; set; }

    [Column("LAST_UPDATED")]
    public DateTime? LastUpdated { get; set; }

    [Column("CREATED_BY")]
    public long CreatedBy { get; set; }

    [Column("LAST_UPDATED_BY")]
    public long? LastUpdatedBy { get; set; }

    [Column("VERSION_ID")]
    public int? VersionId { get; set; }
  }
}
