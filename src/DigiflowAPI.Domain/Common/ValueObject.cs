namespace DigiflowAPI.Domain.Common;

/// <summary>
/// Base class for value objects in the domain model.
/// Value objects are immutable and are compared by their values rather than identity.
/// </summary>
public abstract class ValueObject
{
    /// <summary>
    /// Gets the atomic values that make up this value object.
    /// </summary>
    protected abstract IEnumerable<object> GetAtomicValues();
    
    /// <summary>
    /// Determines whether two value objects are equal.
    /// </summary>
    public override bool Equals(object obj)
    {
        if (obj == null || obj.GetType() != GetType())
        {
            return false;
        }
        
        var other = (ValueObject)obj;
        var thisValues = GetAtomicValues().GetEnumerator();
        var otherValues = other.GetAtomicValues().GetEnumerator();
        
        while (thisValues.MoveNext() && otherValues.MoveNext())
        {
            if (ReferenceEquals(thisValues.Current, null) ^ ReferenceEquals(otherValues.Current, null))
            {
                return false;
            }
            
            if (thisValues.Current != null && !thisValues.Current.Equals(otherValues.Current))
            {
                return false;
            }
        }
        
        return !thisValues.MoveNext() && !otherValues.MoveNext();
    }
    
    /// <summary>
    /// Gets the hash code for this value object.
    /// </summary>
    public override int GetHashCode()
    {
        return GetAtomicValues()
            .Select(x => x != null ? x.GetHashCode() : 0)
            .Aggregate((x, y) => x ^ y);
    }
    
    /// <summary>
    /// Equality operator.
    /// </summary>
    public static bool operator ==(ValueObject left, ValueObject right)
    {
        if (ReferenceEquals(left, null) ^ ReferenceEquals(right, null))
        {
            return false;
        }
        
        return ReferenceEquals(left, null) || left.Equals(right);
    }
    
    /// <summary>
    /// Inequality operator.
    /// </summary>
    public static bool operator !=(ValueObject left, ValueObject right)
    {
        return !(left == right);
    }
}