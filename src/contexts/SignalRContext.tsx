import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react'
import * as signalR from '@microsoft/signalr'
import { toast } from 'react-hot-toast'

export enum NotificationType {
  SUCCESS = 'success',
  ERROR = 'error',
  WARNING = 'warning',
  SYSTEM = 'system',
}

export interface Notification {
  type: NotificationType
  message: string
  targetUser: string
}

interface SignalRContextType {
  connectionState: signalR.HubConnectionState
  sendNotification: (notification: Notification) => Promise<void>
  disconnect: () => Promise<void>
  reconnect: () => Promise<void>
}

const SignalRContext = createContext<SignalRContextType>({
  connectionState: signalR.HubConnectionState.Disconnected,
  sendNotification: async () => {
    throw new Error('SignalR context not initialized')
  },
  disconnect: async () => {
    throw new Error('SignalR context not initialized')
  },
  reconnect: async () => {
    throw new Error('SignalR context not initialized')
  },
})

const handleNotification = (type: string, message: string) => {
  switch (type.toLowerCase()) {
    case NotificationType.SUCCESS:
      toast.success(message)
      break
    case NotificationType.ERROR:
      toast.error(message)
      break
    case NotificationType.WARNING:
      toast(message, { icon: '⚠️' })
      break
    case NotificationType.SYSTEM:
      toast(message, { icon: '🔔' })
      break
    default:
      toast(message)
  }
}

interface NotificationProviderProps {
  children: React.ReactNode
  hubUrl: string
  fallbackToWS?: boolean
  retryIntervals?: number[]
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children, hubUrl, fallbackToWS = true, retryIntervals }) => {
  const [connectionState, setConnectionState] = useState<signalR.HubConnectionState>(signalR.HubConnectionState.Disconnected)
  console.log('connectionState', connectionState)
  const connectionRef = useRef<signalR.HubConnection | null>(null)
  const isConnectingRef = useRef(false)

  const createConnection = useCallback(() => {
    console.log('Creating new SignalR connection to:', hubUrl)
    const connection = new signalR.HubConnectionBuilder()
      .withUrl(hubUrl, {
        skipNegotiation: true,
        transport: signalR.HttpTransportType.WebSockets,
        withCredentials: true,
      })
      .withAutomaticReconnect(retryIntervals || [0, 2000, 5000, 10000, 20000])
      .configureLogging(signalR.LogLevel.Debug)
      .build()

    // Set up handlers
    connection.onreconnecting((error) => {
      console.log('Reconnecting:', error?.message)
      setConnectionState(signalR.HubConnectionState.Reconnecting)
    })

    connection.onreconnected((connectionId) => {
      console.log('Reconnected with ID:', connectionId)
      setConnectionState(signalR.HubConnectionState.Connected)
    })

    connection.onclose((error) => {
      console.log('Connection closed:', error?.message)
      setConnectionState(signalR.HubConnectionState.Disconnected)
    })

    connection.on('ReceiveNotification', (type: string, message: string) => {
      console.log('Received notification:', { type, message })
      handleNotification(type, message)
    })

    return connection
  }, [hubUrl, retryIntervals])

  const connect = useCallback(async () => {
    if (isConnectingRef.current) {
      console.log('Connection attempt already in progress')
      return
    }

    if (connectionRef.current?.state === signalR.HubConnectionState.Connected) {
      console.log('Already connected')
      setConnectionState(signalR.HubConnectionState.Connected)
      return
    }

    isConnectingRef.current = true
    setConnectionState(signalR.HubConnectionState.Connecting)

    try {
      const connection = createConnection()

      console.log('Starting connection...')
      await connection.start()
      console.log('Connection established successfully')

      connectionRef.current = connection
      setConnectionState(signalR.HubConnectionState.Connected)
    } catch (error) {
      console.error('Connection failed:', error)
      setConnectionState(signalR.HubConnectionState.Disconnected)
      connectionRef.current = null

      if (fallbackToWS && hubUrl.startsWith('https:')) {
        try {
          const wsUrl = hubUrl.replace('https:', 'http:')
          console.log('Attempting WS fallback:', wsUrl)

          const fallbackConnection = createConnection()
          await fallbackConnection.start()

          console.log('WS fallback connection established')
          connectionRef.current = fallbackConnection
          setConnectionState(signalR.HubConnectionState.Connected)
        } catch (fallbackError) {
          console.error('WS fallback failed:', fallbackError)
          throw fallbackError
        }
      }
    } finally {
      isConnectingRef.current = false
    }
  }, [createConnection, fallbackToWS, hubUrl])

  useEffect(() => {
    console.log('Setting up connection...')
    connect()

    return () => {
      console.log('Cleaning up connection...')
      if (connectionRef.current) {
        connectionRef.current.stop()
        connectionRef.current = null
        setConnectionState(signalR.HubConnectionState.Disconnected)
      }
    }
  }, [connect])

  // Debug logging for state changes
  useEffect(() => {
    console.log('Connection state changed to:', connectionState)
  }, [connectionState])

  const sendNotification = async (notification: Notification) => {
    if (!connectionRef.current) {
      throw new Error('No connection available')
    }

    if (connectionRef.current.state !== signalR.HubConnectionState.Connected) {
      throw new Error(`Connection is not in Connected state (Current: ${connectionRef.current.state})`)
    }

    try {
      await connectionRef.current.invoke('SendNotification', notification.targetUser, notification.type, notification.message)
    } catch (error) {
      console.error('Error sending notification:', error)
      throw error
    }
  }

  const disconnect = async () => {
    if (connectionRef.current) {
      await connectionRef.current.stop()
      connectionRef.current = null
      setConnectionState(signalR.HubConnectionState.Disconnected)
    }
  }

  const reconnect = async () => {
    await connect()
  }

  return (
    <SignalRContext.Provider
      value={{
        connectionState,
        sendNotification,
        disconnect,
        reconnect,
      }}
    >
      {children}
    </SignalRContext.Provider>
  )
}

export const useNotification = () => {
  const context = useContext(SignalRContext)
  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider')
  }
  return context
}

export default NotificationProvider
