import React, { createContext, useContext, useEffect, useState } from 'react'
import { isInWebView } from '@/api'

interface WebViewContextType {
  isWebView: boolean
  isWebViewReady: boolean
  hasMobileHeaders: boolean
}

const WebViewContext = createContext<WebViewContextType>({
  isWebView: false,
  isWebViewReady: false,
  hasMobileHeaders: false,
})

export const useWebView = () => useContext(WebViewContext)

export const WebViewProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isWebView, setIsWebView] = useState(false)
  const [isWebViewReady, setIsWebViewReady] = useState(false)
  const [hasMobileHeaders, setHasMobileHeaders] = useState(false)

  useEffect(() => {
    const checkWebView = () => {
      const webViewDetected = isInWebView()

      setIsWebView(webViewDetected)
      setIsWebViewReady(true)

      // Apply CSS class for WebView mode or when mobile headers are detected
      const shouldHideHeaders = webViewDetected || hasMobileHeaders
      if (shouldHideHeaders) {
        document.body.classList.add('webview-mode')
        console.log('DigiflowReact: WebView/Mobile mode detected and enabled', {
          webViewDetected,
          hasMobileHeaders,
        })
      } else {
        document.body.classList.remove('webview-mode')
      }

      return webViewDetected
    }

    // Initial check
    checkWebView()

    // Check periodically in case WebView properties are set later
    const intervalId = setInterval(() => {
      if (!isWebView && !isWebViewReady) {
        checkWebView()
      }
    }, 100)

    // Cleanup interval after 5 seconds to avoid continuous checking
    const timeoutId = setTimeout(() => {
      clearInterval(intervalId)
    }, 5000)

    return () => {
      clearInterval(intervalId)
      clearTimeout(timeoutId)
    }
  }, [isWebView, isWebViewReady, hasMobileHeaders])

  // Monitor for mobile headers in requests
  useEffect(() => {
    const checkForMobileHeaders = () => {
      // Check if any requests contain mobile headers by monitoring axios interceptors
      // This is a simplified check - in production you might want to monitor actual network requests
      const mobileHeadersDetected =
        navigator.userAgent.toLowerCase().includes('mobile') || document.querySelector('[data-mobile-request="true"]') !== null

      if (mobileHeadersDetected !== hasMobileHeaders) {
        setHasMobileHeaders(mobileHeadersDetected)
      }
    }

    // Run initial check
    checkForMobileHeaders()

    // Set up periodic check
    const headerCheckInterval = setInterval(checkForMobileHeaders, 1000)

    return () => clearInterval(headerCheckInterval)
  }, [hasMobileHeaders])

  return <WebViewContext.Provider value={{ isWebView, isWebViewReady, hasMobileHeaders }}>{children}</WebViewContext.Provider>
}
