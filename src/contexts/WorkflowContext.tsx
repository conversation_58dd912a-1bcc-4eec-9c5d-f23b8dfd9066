import { createContext, useContext, useState, useCallback, ReactNode, useEffect, useRef } from 'react'
import toast from 'react-hot-toast'
import { IOption, IOrganizationSchemaParams } from '@/types'
import { useTranslation } from 'react-i18next'
import api from '@/api'
import { UserActionHistoryActionType } from '@/types/UserActionType'
import { useAppContext } from 'wface'
import { useUserStore } from '@/stores/userStore'

interface WorkflowContextType {
  comments: Record<string, string>
  setComments: React.Dispatch<React.SetStateAction<Record<string, string>>>
  setData: React.Dispatch<React.SetStateAction<any>>
  setUpdateEntity: React.Dispatch<React.SetStateAction<any>>
  isSendBackVisible: boolean
  setIsSendBackVisible: React.Dispatch<React.SetStateAction<any>>
  isDaemonWorking: boolean
  setIsDaemonWorking: React.Dispatch<React.SetStateAction<any>>
  data: any
  updateEntity: any
  wfInstanceId: number
  definitionId: number
  setDefinitionId: React.Dispatch<React.SetStateAction<number>>
  handleWorkflowAction: (action: string, data?: any) => void
  fetchInitialData: () => void
  refetchInitialData: () => void
  fetchPostInitialStateData: () => void
  initialData: any
  // selectedUser removed from context - now using Zustand
  setSelectedUser: (user: IOption | null) => void // wrapper for Zustand
  activeAction: string
  setActiveAction: React.Dispatch<React.SetStateAction<string>>
  actionType: UserActionHistoryActionType | undefined
  setActionType: React.Dispatch<React.SetStateAction<UserActionHistoryActionType | undefined>>
  orgTreeInitialData: IOrganizationSchemaParams
  setOrgTreeInitialData: React.Dispatch<React.SetStateAction<IOrganizationSchemaParams>>
  tabVisibility: Record<string, boolean>
  setTabVisibility: React.Dispatch<React.SetStateAction<Record<string, boolean>>>
  actionPermissions: Record<string, boolean | number | any[]>
  setActionPermissions: React.Dispatch<React.SetStateAction<Record<string, boolean | number>>>
  canSeeWorkflow: boolean
  isSuspended: boolean
  authMessage: string
  refInstanceId: number | null
  copyInstanceId: number | null
  workflowName: string
  schemas: any // Add this to store the validation schemas
}

const WorkflowContext = createContext<WorkflowContextType | undefined>(undefined)

export const useWorkflow = () => {
  const context = useContext(WorkflowContext)
  if (!context) throw new Error('useWorkflow must be used within a WorkflowProvider')
  return context
}

interface WorkflowProviderProps {
  children: ReactNode
  workflowName: string
  wfInstanceId: number
  refInstanceId: number | null
  copyInstanceId: number | null
  schemas: any
}

export const WorkflowProvider: React.FC<WorkflowProviderProps> = ({
  children,
  workflowName,
  wfInstanceId,
  refInstanceId,
  copyInstanceId,
  schemas,
}) => {
  const { t } = useTranslation(['errors'])
  const [comments, setComments] = useState<Record<string, string>>({
    create: '.',
    approve: '.',
    forward: '',
    sendToComment: '',
    suspend: '',
    abort: '',
    rollback: '',
    finalize: '',
    sendRequestToComment: '',
    fileUpload: '.',
  })
  const [updateEntity, setUpdateEntity] = useState<any>(null)
  const [data, setData] = useState<any>({})
  const [initialData, setInitialData] = useState<any>(null)
  const [actionType, setActionType] = useState<UserActionHistoryActionType>()
  const [definitionId, setDefinitionId] = useState<any>(null)
  const [isDaemonWorking, setIsDaemonWorking] = useState(false)
  const [isSendBackVisible, setIsSendBackVisible] = useState<boolean>(false)
  const [tabVisibility, setTabVisibility] = useState<Record<string, boolean>>({})
  const [actionPermissions, setActionPermissions] = useState<Record<string, boolean | number>>({})
  const [canSeeWorkflow, setCanSeeWorkflow] = useState<boolean>(true)
  const [isSuspended, setIsSuspended] = useState<boolean>(false)
  const [authMessage, setAuthMessage] = useState<string>('')
  const [activeAction, setActiveAction] = useState<string>('approvereject')
  const { openScreenById } = useAppContext()

  // Use Zustand store
  const { selectedUser, setSelectedUser: setZustandSelectedUser } = useUserStore()

  // Create wrapper function for compatibility
  const setSelectedUser = useCallback(
    (user: IOption | null) => {
      setZustandSelectedUser(user)
    },
    [setZustandSelectedUser]
  )

  useEffect(() => {
    if (tabVisibility.NewRequestTabVisible) setActiveAction(tabVisibility.NewRequestTabVisible ? 'newrequest' : activeAction)
  }, [tabVisibility.NewRequestTabVisible])

  const [orgTreeInitialData, setOrgTreeInitialData] = useState<IOrganizationSchemaParams>({
    departments: [],
    selectedDepartment: undefined,
    divisions: [],
    selectedDivision: undefined,
    units: [],
    selectedUnit: undefined,
    teams: [],
    selectedTeam: undefined,
    subTeams: [[]],
    selectedSubTeams: [],
    users: [],
  })
  const hasFetchedInitialData = useRef(false)

  const parseWorkflowAuthorization = (header: string) => {
    try {
      const parsedHeader = JSON.parse(header)
      setTabVisibility(parsedHeader.TabVisibility || {})
      setActionPermissions(parsedHeader.ActionPermissions || {})
      setCanSeeWorkflow(parsedHeader.CanSeeWorkflow != undefined ? parsedHeader.CanSeeWorkflow : true)
      setIsSuspended(parsedHeader.IsWorkflowSuspended != undefined ? parsedHeader.IsWorkflowSuspended : true)
      setAuthMessage(parsedHeader.Message || '')
    } catch (error) {
      console.error('Error parsing X-Workflow-Authorization header:', error)
    }
  }

  const formatDataByAction = (action: string, data: any, comment: string, workflowName: string, definitionId: number | null) => {
    const baseData = {
      InstanceId: wfInstanceId,
      WorkflowName: workflowName,
      LoginUserId: parseInt(localStorage?.getItem('UserId')?.toString() || '0'),
      Comment: comment,
    }

    if (action != 'create' && comment.trim() == '') {
      toast.error(t('commentError'))
      return null
    }

    switch (action) {
      case 'create':
        const formattedData = {
          EntityJson: data,
          WorkFlowDefinitionId: definitionId,
          WorkflowName: workflowName,
          LoginId: parseInt(localStorage?.getItem('UserId')?.toString() || '0'),
          DetailJson: data.DetailJson || null,
          Detail2Json: data.Detail2Json || null,
          Detail3Json: data.Detail3Json || null,
          Detail4Json: data.Detail4Json || null,
        }
        return formattedData

      case 'approve':
        return {
          ...baseData,
          IsWfContextSave: true,
          ActionType: Number(data.conditionalApproval),
          UpdateEntity: updateEntity,
        }
      case 'forward':
        if (!data.ForwardLoginId) {
          toast.error(t('forwardLoginIdRequired'))
          return null
        }
        return { ...baseData, ForwardUserId: data.ForwardLoginId }
      case 'sendToComment':
        if (!data.SendToCommentLoginId) {
          toast.error(t('sendToCommentLoginIdRequired'))
          return null
        }
        return { ...baseData, SendToCommentUserId: data.SendToCommentLoginId }
      case 'suspend':
        if (!data.suspendUntil) {
          toast.error(t('suspendDateRequired'))
          return null
        }
        if (new Date(data.suspendUntil) <= new Date()) {
          toast.error(t('invalidSuspendDate'))
          return null
        }
        return { ...baseData, SuspendDate: data.suspendUntil }
      case 'fileUpload':
        return { ...baseData, Files: data.files }
      case 'finalize':
      case 'cancel':
      case 'resume':
      case 'reject':
      case 'send-back':
      case 'sendRequestToComment':
        return baseData
      default:
        toast.error(t('invalidAction'))
        return null
    }
  }

  const handleWorkflowAction = useCallback(
    async (action: string, data: any) => {
      const url = `/workflows/${action}`
      const activeComment = action === 'fileUpload' ? data.comment : comments[activeAction] || ''
      const formattedData = formatDataByAction(action, data, activeComment, workflowName, definitionId)

      if (!formattedData) {
        return
      }

      try {
        const response = await api.post(url, formattedData)

        if (response.data.successMessage) {
          toast.success(response.data.successMessage.replaceAll('"', ''), {
            duration: 5000,
          })

          // Clear comments after successful action
          setComments((prevComments) =>
            Object.keys(prevComments).reduce(
              (acc, key) => {
                acc[key] = '.'
                return acc
              },
              {} as Record<string, string>
            )
          )

          setData(null)

          // Handle navigation based on action type
          if (action !== 'fileUpload' && action !== 'sendRequestToComment' && action !== 'suspend' && action !== 'resume') {
            //TODO: wface güncellendiğinde openScreenByParams methodu kullanılarak yönlendirme yapılacak
            openScreenById('inbox')
          } else {
            await refetchInitialData() // wait for updated tab info
            setIsDaemonWorking(true)
            // Dispatch custom event to signal full screen refresh
            window.dispatchEvent(new CustomEvent('refreshWorkflowScreen'))
          }
        } else if (response.data.errorMessage) {
          toast.error(response.data.errorMessage, {
            duration: 5000,
          })
          throw new Error(response.data.errorMessage)
        }

        return response.data
      } catch (error) {
        console.error('Error in workflow action:', error)
        throw error
      }
    },
    [comments, activeAction, definitionId, workflowName, t]
  )

  const refetchInitialData = async () => {
    if (wfInstanceId === 0) {
      try {
        const response = await api.get(
          `/workflows`,
          {
            params: {
              workflowName,
              ...(copyInstanceId && { copyInstanceId }),
              ...(refInstanceId && { refInstanceId }),
            },
          },
          definitionId?.toString()
        )
        if (response.data.errorMessage !== '') {
          toast.error(response.data.errorMessage, {
            duration: 5000,
          })
        } else {
          const { enableControlsDto, loadEntityToControlsDto, loadDataBindingDto, newWorkflowLoadingDto } = response.data.detail
          const combinedData = {
            ...enableControlsDto,
            ...loadEntityToControlsDto,
            ...loadDataBindingDto,
            ...newWorkflowLoadingDto,
          }
          setInitialData(combinedData)

          const workflowAuthorizationHeader = response.headers['X-Workflow-Authorization'] || response.headers['x-workflow-authorization']
          if (workflowAuthorizationHeader) {
            parseWorkflowAuthorization(workflowAuthorizationHeader)
          } else {
            console.error('Workflow authorization information not found in the response headers')
          }
        }

        hasFetchedInitialData.current = true
      } catch (error) {
        console.error('Error fetching initial data:', error)
      }
    }
  }

  const fetchInitialData = useCallback(async () => {
    if (wfInstanceId === 0 && !hasFetchedInitialData.current) {
      try {
        const response = await api.get(
          `/workflows`,
          {
            params: {
              workflowName,
              ...(copyInstanceId && { copyInstanceId }),
              ...(refInstanceId && { refInstanceId }),
            },
          },
          definitionId?.toString()
        )

        if (response.data.errorMessage !== '') {
          toast.error(response.data.errorMessage, {
            duration: 5000,
          })
        } else {
          const { enableControlsDto, loadEntityToControlsDto, loadDataBindingDto, newWorkflowLoadingDto } = response.data.detail
          const combinedData = {
            ...enableControlsDto,
            ...loadEntityToControlsDto,
            ...loadDataBindingDto,
            ...newWorkflowLoadingDto,
          }
          setInitialData(combinedData)

          const workflowAuthorizationHeader = response.headers['X-Workflow-Authorization'] || response.headers['x-workflow-authorization']

          if (workflowAuthorizationHeader) {
            parseWorkflowAuthorization(workflowAuthorizationHeader)
          } else {
            console.error('Workflow authorization information not found in the response headers')
          }
        }

        hasFetchedInitialData.current = true
      } catch (error) {
        console.error('Error fetching initial data:', error)
      }
    }
  }, [workflowName, wfInstanceId, definitionId, location.search])

  const fetchPostInitialStateData = useCallback(async () => {
    if (wfInstanceId !== 0) {
      try {
        const response = await api.get(`/workflows?workflowName=${workflowName}&wfInstanceId=${wfInstanceId}`, {}, definitionId?.toString())
        if (response.data.errorMessage !== '') {
          toast.error(response.data.errorMessage, {
            duration: 5000,
          })
        } else {
          const { enableControlsDto, loadEntityToControlsDto, loadDataBindingDto, newWorkflowLoadingDto } = response.data.detail
          const combinedData = {
            ...enableControlsDto,
            ...loadEntityToControlsDto,
            ...loadDataBindingDto,
            ...newWorkflowLoadingDto,
          }
          setInitialData(combinedData)

          const workflowAuthorizationHeader = response.headers['X-Workflow-Authorization'] || response.headers['x-workflow-authorization']

          if (workflowAuthorizationHeader) {
            parseWorkflowAuthorization(workflowAuthorizationHeader)
          } else {
            console.error('Workflow authorization information not found in the response headers')
          }
        }
      } catch (error) {
        console.error('Error fetching post initial state data:', error)
      }
    }
  }, [workflowName, wfInstanceId, location.search])

  useEffect(() => {
    fetchInitialData()
  }, [fetchInitialData, location.search])

  useEffect(() => {
    if (selectedUser) {
      if (wfInstanceId) {
        fetchPostInitialStateData()
      } else {
        fetchInitialData()
      }
    }
  }, [selectedUser, wfInstanceId, fetchInitialData, fetchPostInitialStateData, location.search])

  return (
    <WorkflowContext.Provider
      value={{
        comments,
        setComments,
        setData,
        data,
        setUpdateEntity,
        updateEntity,
        initialData,
        definitionId,
        activeAction,
        setActiveAction,
        setDefinitionId,
        handleWorkflowAction,
        fetchInitialData,
        isDaemonWorking,
        setIsDaemonWorking,
        refetchInitialData,
        fetchPostInitialStateData,
        setSelectedUser,
        orgTreeInitialData,
        setOrgTreeInitialData,
        wfInstanceId,
        refInstanceId,
        copyInstanceId,
        setActionType,
        actionType,
        isSendBackVisible,
        setIsSendBackVisible,
        tabVisibility,
        setTabVisibility,
        actionPermissions,
        setActionPermissions,
        canSeeWorkflow,
        isSuspended,
        authMessage,
        workflowName,
        schemas,
      }}
    >
      {children}
    </WorkflowContext.Provider>
  )
}

export { WorkflowContext }
export default WorkflowProvider
