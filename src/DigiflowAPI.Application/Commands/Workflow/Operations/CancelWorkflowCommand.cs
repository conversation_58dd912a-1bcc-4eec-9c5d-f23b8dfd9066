﻿using DigiflowAPI.Application.DTOs.Workflow.Operation;
using DigiflowAPI.Application.DTOs.Workflow.Responses;
using MediatR;

namespace DigiflowAPI.Application.Commands.Workflow.Operations
{
    public class CancelWorkflowCommand<TRequest, TResponse>(CancelWorkflowRequestDto workflowRequest) : IRequest<WorkflowResponseDto<TResponse>> where TResponse : class
    {
        public CancelWorkflowRequestDto WorkflowRequest { get; } = workflowRequest;
    }
}
