﻿using DigiflowAPI.Application.DTOs.Workflow.Operation;
using DigiflowAPI.Application.DTOs.Workflow.Responses;
using MediatR;

namespace DigiflowAPI.Application.Commands.Workflow.Operations
{
    public class SendToCommentWorkflowCommand<TRequest, TResponse>(SendToCommentWorkflowRequestDto workflowRequest) : IRequest<WorkflowResponseDto<TResponse>> where TResponse : class
    {
        public SendToCommentWorkflowRequestDto WorkflowRequest { get; } = workflowRequest;
    }
}
