﻿using DigiflowAPI.Application.DTOs.Workflow.Operation;
using DigiflowAPI.Application.DTOs.Workflow.Responses;
using MediatR;

namespace DigiflowAPI.Application.Commands.Workflow.Operations
{
    public class SuspendWorkflowCommand<TRequest, TResponse>(SuspendWorkflowRequestDto workflowRequest) : IRequest<WorkflowResponseDto<TResponse>> where TResponse : class
    {
        public SuspendWorkflowRequestDto WorkflowRequest { get; } = workflowRequest;
    }
}
