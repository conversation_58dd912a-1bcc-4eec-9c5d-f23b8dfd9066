﻿using DigiflowAPI.Application.DTOs.Workflow.Operation;
using DigiflowAPI.Application.DTOs.Workflow.Responses;
using MediatR;

namespace DigiflowAPI.Application.Commands.Workflow.Operations
{
    public class ForwardWorkflowCommand<TRequest, TResponse>(ForwardWorkflowRequestDto workflowRequest) : IRequest<WorkflowResponseDto<TResponse>> where TResponse : class
    {
        public ForwardWorkflowRequestDto WorkflowRequest { get; } = workflowRequest;
    }
}
