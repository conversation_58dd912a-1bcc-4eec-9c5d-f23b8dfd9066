﻿using DigiflowAPI.Application.DTOs.Workflow.Operation;
using DigiflowAPI.Application.DTOs.Workflow.Responses;
using MediatR;

namespace DigiflowAPI.Application.Commands.Workflow.Operations
{
    public class ApproveWorkflowCommand<TRequest, TResponse>(ApprovalWorkflowRequestDto workflowRequest) : IRequest<WorkflowResponseDto<TResponse>> where TResponse : class
    {
        public ApprovalWorkflowRequestDto WorkflowRequest { get; } = workflowRequest;
    }
}
