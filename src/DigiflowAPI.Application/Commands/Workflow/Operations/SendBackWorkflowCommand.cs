﻿using DigiflowAPI.Application.DTOs.Workflow.Operation;
using DigiflowAPI.Application.DTOs.Workflow.Responses;
using MediatR;

namespace DigiflowAPI.Application.Commands.Workflow.Operations
{
    public class SendBackWorkflowCommand<TRequest, TResponse>(SendBackWorkflowRequestDto workflowRequest) : IRequest<WorkflowResponseDto<TResponse>> where TResponse : class
    {
        public SendBackWorkflowRequestDto WorkflowRequest { get; } = workflowRequest;
    }
}
