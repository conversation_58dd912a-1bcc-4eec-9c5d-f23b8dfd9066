﻿using DigiflowAPI.Application.DTOs.Workflow.Operation;
using DigiflowAPI.Application.DTOs.Workflow.Responses;
using MediatR;

namespace DigiflowAPI.Application.Commands.Workflow.Operations
{
    public class FileUploadWorkflowCommand<TRequest, TResponse>(FileUploadWorkflowRequestDto workflowRequest) : IRequest<WorkflowResponseDto<TResponse>> where TResponse : class
    {
        public FileUploadWorkflowRequestDto WorkflowRequest { get; } = workflowRequest;
    }
}
