﻿using DigiflowAPI.Application.DTOs.Workflow.Operation;
using DigiflowAPI.Application.DTOs.Workflow.Responses;
using MediatR;

namespace DigiflowAPI.Application.Commands.Workflow.Operations
{
    public class SendRequestToCommentWorkflowCommand<TRequest, TResponse>(SendRequestToCommentWorkflowRequestDto workflowRequest) : IRequest<WorkflowResponseDto<TResponse>> where TResponse : class
    {
        public SendRequestToCommentWorkflowRequestDto WorkflowRequest { get; } = workflowRequest;
    }
}
