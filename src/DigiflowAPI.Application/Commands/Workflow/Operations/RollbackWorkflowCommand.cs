﻿using DigiflowAPI.Application.DTOs.Workflow.Operation;
using DigiflowAPI.Application.DTOs.Workflow.Responses;
using MediatR;

namespace DigiflowAPI.Application.Commands.Workflow.Operations
{
    public class RollbackWorkflowCommand<TRequest, TResponse>(RollbackWorkflowRequestDto workflowRequest) : IRequest<WorkflowResponseDto<TResponse>> where TResponse : class
    {
        public RollbackWorkflowRequestDto WorkflowRequest { get; } = workflowRequest;
    }
}
