﻿using DigiflowAPI.Application.DTOs.Workflow.Operation;
using DigiflowAPI.Application.DTOs.Workflow.Responses;
using MediatR;

namespace DigiflowAPI.Application.Commands.Workflow.Operations
{
    public class CreateWorkflowCommand<TRequest, TResponse>(CreateWorkflowRequestDto workflowRequest) : IRequest<WorkflowResponseDto<TResponse>> where TResponse : class
    {
        public CreateWorkflowRequestDto WorkflowRequest { get; } = workflowRequest;
    }
}
