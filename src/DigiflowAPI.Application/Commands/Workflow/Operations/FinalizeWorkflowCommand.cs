﻿using DigiflowAPI.Application.DTOs.Workflow.Operation;
using DigiflowAPI.Application.DTOs.Workflow.Responses;
using MediatR;

namespace DigiflowAPI.Application.Commands.Workflow.Operations
{
    public class FinalizeWorkflowCommand<TRequest, TResponse>(FinalizeWorkflowRequestDto workflowRequest) : IRequest<WorkflowResponseDto<TResponse>> where TResponse : class
    {
        public FinalizeWorkflowRequestDto WorkflowRequest { get; } = workflowRequest;
    }
}
