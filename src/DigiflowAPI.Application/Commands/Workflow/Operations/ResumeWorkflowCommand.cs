﻿using DigiflowAPI.Application.DTOs.Workflow.Operation;
using DigiflowAPI.Application.DTOs.Workflow.Responses;
using MediatR;

namespace DigiflowAPI.Application.Commands.Workflow.Operations
{
    public class ResumeWorkflowCommand<TRequest, TResponse>(ResumeWorkflowRequestDto workflowRequest) : IRequest<WorkflowResponseDto<TResponse>> where TResponse : class
    {
        public ResumeWorkflowRequestDto WorkflowRequest { get; } = workflowRequest;
    }
}
