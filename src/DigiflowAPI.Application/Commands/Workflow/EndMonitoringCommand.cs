﻿using DigiflowAPI.Application.DTOs.Workflow.Monitoring;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiflowAPI.Application.Commands.Workflow
{
    public class EndMonitoringCommand : IRequest<string>
    {
        public EndMonitoringRequestDto[] Monitorings { get; }

        public EndMonitoringCommand(EndMonitoringRequestDto[] monitorings)
        {
            Monitorings = monitorings;
        }
    }

}
