﻿using DigiflowAPI.Application.DTOs.Workflow.History;
using MediatR;
using System.Collections.Generic;

namespace DigiflowAPI.Application.Commands.Workflow
{
    public class UpdateHistoryCommentCommand : IRequest<WorkflowHistoryDto>
    {
        public UpdateHistoryCommentCommand(long wfHistoryId, string comment, List<string> uploadedFiles)
        {
            WfHistoryId = wfHistoryId;
            Comment = comment;
            UploadedFiles = uploadedFiles;
        }

        public long WfHistoryId { get; }
        public string Comment { get; }
        public List<string> UploadedFiles { get; }
    }
}
