﻿using DigiflowAPI.Application.DTOs.Organization;
using DigiflowAPI.Domain.DTOs.Organization;
using MediatR;

namespace DigiflowAPI.Application.Commands.Organization
{
    public class GetSchemaCommand : IRequest<SchemaDto>
    {
        public int? SelectedDepartment { get; set; }
        public int? SelectedDivision { get; set; }
        public int? SelectedUnit { get; set; }
        public int? SelectedTeam { get; set; }
        public IEnumerable<int>? SelectedSubTeams { get; set; }
        public int LastSelectedId { get; set; }
        public bool ShowText { get; set; } = false;
        public long? UserId { get; set; }
    }
}