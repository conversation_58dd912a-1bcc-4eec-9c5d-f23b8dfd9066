﻿using MediatR;
using DigiflowAPI.Application.DTOs.Inbox;
using DigiflowAPI.Domain.Entities.History;
using DigiflowAPI.Application.DTOs;

namespace DigiflowAPI.Application.Commands.History
{
    public class GetWorkflowsOfAdminHistoryCommand : IRequest<IEnumerable<VwWorkflowHistory>>
    {
        public GetWorkflowsOfAdminRequestDto Request { get; set; }

        public GetWorkflowsOfAdminHistoryCommand(GetWorkflowsOfAdminRequestDto request)
        {
            Request = request;
        }
    }
}