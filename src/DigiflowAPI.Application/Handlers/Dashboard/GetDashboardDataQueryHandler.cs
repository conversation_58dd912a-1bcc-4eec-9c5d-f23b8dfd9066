using MediatR;
using DigiflowAPI.Application.Models.Common;
using System.Net.Http.Headers;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using DigiflowAPI.Application.Interfaces.Services;
using Microsoft.Extensions.Configuration;

namespace DigiflowAPI.Application.Handlers.Dashboard
{
    // Query
    public class GetDashboardDataQuery : IRequest<DashboardResponse>
    {
        public int UserId { get; set; }
    }

    // Handler
    public class GetDashboardDataQueryHandler : IRequestHandler<GetDashboardDataQuery, DashboardResponse>
    {
        private readonly IUserService _userService; // To get user info
        private readonly HttpClient _httpClient; // For other API calls if needed, or refactor to services
        private readonly IConfiguration _configuration;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public GetDashboardDataQueryHandler(IUserService userService, HttpClient httpClient, IConfiguration configuration, IHttpContextAccessor httpContextAccessor)
        {
            _userService = userService;
            _httpClient = httpClient;
            _configuration = configuration;
            _httpContextAccessor = httpContextAccessor;

            var webApiBaseUrl = _configuration["WebApiSettings:BaseUrl"];
            if (!string.IsNullOrEmpty(webApiBaseUrl) && _httpClient.BaseAddress == null)
            {
                _httpClient.BaseAddress = new Uri(webApiBaseUrl);
            }
        }

        public async Task<DashboardResponse> Handle(GetDashboardDataQuery request, CancellationToken cancellationToken)
        {
            var token = ExtractJwtToken();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            }

            // Fetch UserInfo using IUserService
            var domainUserInfo = await _userService.GetUserInfo((long)request.UserId);
            var appUserInfo = new UserInfo(); // Application DTO
            if (domainUserInfo != null)
            {
                appUserInfo.Id = (int)domainUserInfo.FLoginId.GetValueOrDefault(); // Ensure FLoginId is the correct mapping
                appUserInfo.Name = domainUserInfo.NameSurname; // Assuming FullName exists
                appUserInfo.Email = domainUserInfo.EMail;
                // appUserInfo.Department = await _userService.GetDepartmentById(domainUserInfo.DepartmentId); // This needs adjustment
                // appUserInfo.Title = domainUserInfo.Title; // Assuming Title exists
            }
            else
            {
                // Fallback or error handling if user not found
                appUserInfo.Id = request.UserId;
                appUserInfo.Name = "Unknown";
            }

            // Other data fetching tasks (OrgInfo, Slides, Inbox, History)
            // These were previously private methods in HomeService.cs
            // They might need to be refactored into their own services or handlers if complex,
            // or called directly if they are simple HTTP GET requests.

            var tasks = new List<Task>
            {
                Task.FromResult(appUserInfo), // UserInfo is already fetched
                GetOrganizationInfoAsync(request.UserId, cancellationToken),
                GetSlidesAsync(cancellationToken),
                GetInboxItemsAsync(request.UserId, cancellationToken),
                GetHistoryItemsAsync(request.UserId, cancellationToken)
            };

            await Task.WhenAll(tasks.Skip(1)); // Skip the already completed UserInfo task for WhenAll

            return new DashboardResponse
            {
                UserInfo = appUserInfo, // Use the already fetched and mapped UserInfo
                OrgInfo = ((Task<OrgTreeInfo>)tasks[1]).Result,
                Slides = ((Task<List<SlideItem>>)tasks[2]).Result, // Ensure type matches
                InboxItems = ((Task<List<InboxItem>>)tasks[3]).Result, // Ensure type matches
                HistoryItems = ((Task<List<HistoryItem>>)tasks[4]).Result // Ensure type matches
            };
        }

        // Helper methods (similar to those in HomeService, but now within the handler or a dedicated service)
        // These methods now use _httpClient and _configuration from the handler

        private async Task<OrgTreeInfo> GetOrganizationInfoAsync(int userId, CancellationToken cancellationToken)
        {
            try
            {
                // TODO: Determine the correct endpoint or service for organization info
                // The original HomeService used "api/organizations/departments"
                var response = await _httpClient.GetAsync("api/organizations/departments", cancellationToken);
                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync(cancellationToken);
                    var orgData = JsonSerializer.Deserialize<JsonElement>(json);
                    return new OrgTreeInfo
                    {
                        DepartmentName = orgData.TryGetProperty("departmentName", out var deptProp) ? deptProp.GetString() ?? "" : "",
                        ManagerName = orgData.TryGetProperty("managerName", out var mgrProp) ? mgrProp.GetString() ?? "" : "",
                        TeamSize = orgData.TryGetProperty("teamSize", out var sizeProp) ? sizeProp.GetInt32() : 0,
                        Level = orgData.TryGetProperty("level", out var levelProp) ? levelProp.GetInt32() : 0
                    };
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error fetching organization info: {ex.Message}");
            }
            return new OrgTreeInfo { DepartmentName = "", ManagerName = "", TeamSize = 0, Level = 0 };
        }

        private async Task<List<SlideItem>> GetSlidesAsync(CancellationToken cancellationToken)
        {
            try
            {
                var response = await _httpClient.GetAsync("slides/active", cancellationToken);
                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync(cancellationToken);
                    // Assuming SlideItem DTO in Application.Models.Common is compatible
                    var slidesData = JsonSerializer.Deserialize<List<SlideItem>>(json);
                    return slidesData ?? new List<SlideItem>();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error fetching slides: {ex.Message}");
            }
            return new List<SlideItem>();
        }

        private async Task<List<InboxItem>> GetInboxItemsAsync(int userId, CancellationToken cancellationToken)
        {
            try
            {
                var response = await _httpClient.GetAsync($"api/inboxes/{userId}?limit=3", cancellationToken);
                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync(cancellationToken);
                    var inboxData = JsonSerializer.Deserialize<JsonElement[]>(json);
                    return inboxData?.Take(3).Select(item => new InboxItem
                    {
                        Id = item.TryGetProperty("id", out var idProp) ? idProp.GetInt32() : 0,
                        Title = item.TryGetProperty("title", out var titleProp) ? titleProp.GetString() ?? "" : "",
                        Description = item.TryGetProperty("description", out var descProp) ? descProp.GetString() ?? "" : "",
                        CreatedDate = item.TryGetProperty("createdDate", out var dateProp) && DateTime.TryParse(dateProp.GetString(), out var date) ? date : DateTime.Now,
                        IsRead = item.TryGetProperty("isRead", out var readProp) && readProp.GetBoolean(),
                        Priority = item.TryGetProperty("priority", out var priProp) ? priProp.GetString() ?? "Normal" : "Normal",
                        SenderName = item.TryGetProperty("senderName", out var senderProp) ? senderProp.GetString() ?? "" : ""
                    }).ToList() ?? new List<InboxItem>();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error fetching inbox items: {ex.Message}");
            }
            return new List<InboxItem>();
        }

        private async Task<List<HistoryItem>> GetHistoryItemsAsync(int userId, CancellationToken cancellationToken)
        {
            try
            {
                var response = await _httpClient.GetAsync($"api/histories?userId={userId}&limit=3", cancellationToken);
                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync(cancellationToken);
                    var historyData = JsonSerializer.Deserialize<JsonElement[]>(json);
                    return historyData?.Take(3).Select(item => new HistoryItem
                    {
                        Id = item.TryGetProperty("id", out var idProp) ? idProp.GetInt32() : 0,
                        Action = item.TryGetProperty("action", out var actionProp) ? actionProp.GetString() ?? "" : "",
                        Description = item.TryGetProperty("description", out var descProp) ? descProp.GetString() ?? "" : "",
                        Timestamp = item.TryGetProperty("timestamp", out var timestampProp) && DateTime.TryParse(timestampProp.GetString(), out var timestamp) ? timestamp : DateTime.Now,
                        Module = item.TryGetProperty("module", out var moduleProp) ? moduleProp.GetString() ?? "" : "",
                        Status = item.TryGetProperty("status", out var statusProp) ? statusProp.GetString() ?? "" : ""
                    }).ToList() ?? new List<HistoryItem>();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error fetching history items: {ex.Message}");
            }
            return new List<HistoryItem>();
        }

        private string ExtractJwtToken()
        {
            var authHeader = _httpContextAccessor.HttpContext?.Request.Headers["Authorization"].FirstOrDefault();
            if (authHeader != null && authHeader.StartsWith("Bearer "))
            {
                return authHeader.Substring("Bearer ".Length).Trim();
            }
            return string.Empty;
        }
    }
}
