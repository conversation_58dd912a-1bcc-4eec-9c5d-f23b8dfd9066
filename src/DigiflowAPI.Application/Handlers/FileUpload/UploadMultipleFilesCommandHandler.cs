﻿using DigiflowAPI.Application.Commands.FileUpload;
using DigiflowAPI.Domain.Entities;
using DigiflowAPI.Application.Interfaces.Services;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace DigiflowAPI.Application.Handlers.FileUpload
{
    public class UploadMultipleFilesCommandHandler : IRequestHandler<UploadMultipleFilesCommand, IEnumerable<string>>
    {
        private readonly ISharePointService _sharePointService;

        public UploadMultipleFilesCommandHandler(ISharePointService sharePointService)
        {
            _sharePointService = sharePointService;
        }

        public async Task<IEnumerable<string>> Handle(UploadMultipleFilesCommand request, CancellationToken cancellationToken)
        {
            var uploadTasks = new List<Task<string>>();

            foreach (var file in request.Files)
            {
                var fileName = file.FileName.Split(".");
                var uploadedFile = new UploadedFile
                {
                    FileName = fileName[0] + "_" + Guid.NewGuid() + fileName[1],
                    Content = await GetFileBytes(file)
                };

                uploadTasks.Add(_sharePointService.UploadFileToSharePointAsync(uploadedFile, request.PathKey));
            }

            return await Task.WhenAll(uploadTasks);
        }

        private async Task<byte[]> GetFileBytes(IFormFile file)
        {
            using var memoryStream = new MemoryStream();
            await file.CopyToAsync(memoryStream);
            return memoryStream.ToArray();
        }
    }

}
