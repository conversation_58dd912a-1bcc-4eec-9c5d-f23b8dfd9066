﻿using DigiflowAPI.Application.Queries.FileUploads;
using DigiflowAPI.Domain.Entities;
using MediatR;
using DigiflowAPI.Application.Interfaces.Services;

namespace DigiflowAPI.Application.Handlers.FileUpload
{
    public class GetUploadedFileQueryHandler(ISharePointService sharePointService) : IRequestHandler<GetUploadedFileQuery, UploadedFile>
    {
        public async Task<UploadedFile> Handle(GetUploadedFileQuery request, CancellationToken cancellationToken)
        {
            return await sharePointService.GetFileFromSharePointAsync(request.PathKey, request.FileName);
        }
    }
}
