﻿using DigiflowAPI.Application.Commands.FileUpload;
using DigiflowAPI.Domain.Entities;
using MediatR;
using Microsoft.AspNetCore.Http;
using DigiflowAPI.Application.Interfaces.Services;

namespace DigiflowAPI.Application.Handlers.FileUpload
{
    public class UploadFileCommandHandler(ISharePointService sharePointService) : IRequestHandler<UploadFileCommand, string>
    {
        public async Task<string> Handle(UploadFileCommand request, CancellationToken cancellationToken)
        {
            var uploadedFile = new UploadedFile
            {
                FileName = request.File.FileName,
                Content = await GetFileBytes(request.File)
            };

            return await sharePointService.UploadFileToSharePointAsync(uploadedFile, request.PathKey);
        }

        private async Task<byte[]> GetFileBytes(IFormFile file)
        {
            using var memoryStream = new MemoryStream();
            await file.CopyToAsync(memoryStream);
            return memoryStream.ToArray();
        }
    }
}
