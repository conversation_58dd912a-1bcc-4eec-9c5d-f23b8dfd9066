﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiflowAPI.Domain.DTOs.Email
{
    public class EmailParametersDto
    {
        public string CommentUpdatedUserNameSurName { get; set; } = null;
        public string OldDescription { get; set; } = null;
        public string Description { get; set; } = null;
        public string WfDefName { get; set; } = null;
        public string WfHistoryInstanceId { get; set; } = null;
        public string ToListMailActionUser { get; set; } = null; // Optional, for debug mode
        public string CreateUserNameSurName { get; set; } = null;
        public string AssinedUserNameSurname { get; set; } = null;
        public string LastActionUserNameSurName { get; set; } = null;
        public string GetFinalLoginNameSurName { get; set; } = null;
        public string WorkFlowAdminList { get; set; } = null;
        public string TaskScreenLink { get; set; } = null;
        public string ActionOwner { get; set; } = null;
        public string ActionDescription { get; set; } = null;
        public string ActionDate { get; set; } = null;
        public string Action { get; set; } = null;
        public string ActionTo { get; set; } = null;
        public string AssignedUserNameSurname { get; set; } = null;
        public string StateName { get; set; } = null;
        public string NotificationUserNameSurName { get; set; } = null;
        public string NotificationNote { get; set; } = null;
    }
}
