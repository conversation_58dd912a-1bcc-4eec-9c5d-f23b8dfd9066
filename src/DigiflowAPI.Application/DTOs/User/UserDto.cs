﻿using DigiflowAPI.Domain.DTOs.User;

namespace DigiflowAPI.Application.DTOs.User
{
    public class UserDto
    {
        public long? LoginId { get; set; }
        public string? NameSurname { get; set; }
        public string? Username { get; set; }
        public string? EMail { get; set; }
        public decimal Sicil { get; set; }
        public string? DeptName { get; set; }
        public string? ButceManager { get; set; }
        public string? CariKod { get; set; }
        public decimal? DeptId { get; set; }
        public string IsDeleted { get; set; }
        public string? PersonelTipi { get; set; }
        public DPHRUsersDto? DpHrUsers { get; set; }
    }
}