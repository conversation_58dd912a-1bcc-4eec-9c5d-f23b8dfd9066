using System.ComponentModel.DataAnnotations;

namespace DigiflowAPI.Application.DTOs.User
{
    /// <summary>
    /// Minimal user profile DTO containing only the fields used by the frontend
    /// </summary>
    public class UserProfileDto
    {
        public long LoginId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string NameSurname { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100)]
        [RegularExpression(@"^[a-zA-Z0-9_]+$", ErrorMessage = "Username can only contain letters, numbers and underscores")]
        public string Username { get; set; } = string.Empty;
        
        [EmailAddress]
        [StringLength(200)]
        public string? Email { get; set; }
        
        [StringLength(20)]
        public string? Sicil { get; set; }
        
        [StringLength(200)]
        public string? DeptName { get; set; }
        
        public long? DeptId { get; set; }
        
        public string? ButceManager { get; set; }
        
        public string? CariKod { get; set; }
        
        public string? PersonelTipi { get; set; }
        
        public bool IsDeleted { get; set; }
    }

    /// <summary>
    /// Minimal DTO for user selection dropdowns
    /// </summary>
    public class UserSelectDto
    {
        public long LoginId { get; set; }
        
        [Required]
        public string DisplayName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Current user DTO with authentication info
    /// </summary>
    public class CurrentUserDto
    {
        public long LoginId { get; set; }
        public string Username { get; set; } = string.Empty;
        public string NameSurname { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? DeptName { get; set; }
        public string AuthenticationType { get; set; } = "JWT";
        public bool IsSystemAdmin { get; set; }
    }
}