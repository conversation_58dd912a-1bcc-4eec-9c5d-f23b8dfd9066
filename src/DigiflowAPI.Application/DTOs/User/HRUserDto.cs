﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiflowAPI.Application.DTOs.User
{
    public class DPHRUsersDto
    {
        public int Sicil { get; set; }
        public string? Username { get; set; }
        public string? NameSurname { get; set; }
        public long? DeptId { get; set; }
        public string? Email { get; set; }
        public string? IsManager { get; set; }
        public string? HesapNo { get; set; }
        public char Deleted { get; set; }
        public string? DeptName { get; set; }
        public DateTime? DogumTarihi { get; set; }
        public string? ButceManager { get; set; }
        public DateTime? GirisTarihi { get; set; }
        public int? KalanIzin { get; set; }
        public string? LastGraduated { get; set; }
        public string? Address { get; set; }
        public long? CellPhone { get; set; }
        public string? MaritalStatus { get; set; }
        public char? Sex { get; set; }
        public string? BloodType { get; set; }
        public long? TcKimlikNo { get; set; }
        public long? VergiKimlikNo { get; set; }
        public long? SskNo { get; set; }
        public long? DegisimOnculeriPuani { get; set; }
        public DateTime? IzinHakedisTarihi { get; set; }
        public long? Id { get; set; }
        public string? Floor { get; set; }
        public int? Corbuss { get; set; }
        public string? Presentative { get; set; }
        public int? Telephone { get; set; }
        public int? DeptFlag { get; set; }
        public string? CariKod { get; set; }
        public int? Firma { get; set; }
        public string? BedenOlcu { get; set; }
        public string? GsmProfil { get; set; }
        public string? Lokasyon { get; set; }
        public string? DogumYeri { get; set; }
        public string? AnneAdi { get; set; }
        public string? BabaAdi { get; set; }
        public string? Milliyet { get; set; }
        public string? Din { get; set; }
        public string? AdresSemt { get; set; }
        public string? AdresIl { get; set; }
        public string? EmailOzel { get; set; }
        public string? OkulFakulte { get; set; }
        public string? OkulBolum { get; set; }
        public string? OkulGirisAy { get; set; }
        public string? OkulGirisYıl { get; set; }
        public string? OkulCikisAy { get; set; }
        public string? OkulCikisYil { get; set; }
        public int? Telephone2 { get; set; }
        public long? FLoginId { get; set; }
        public string? SodexhoKartNo { get; set; }
        public string? Name { get; set; }
        public string? Surname { get; set; }

    }
}
