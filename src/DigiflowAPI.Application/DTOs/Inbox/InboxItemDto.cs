using System.ComponentModel.DataAnnotations;

namespace DigiflowAPI.Application.DTOs.Inbox
{
    /// <summary>
    /// Optimized inbox item DTO containing only fields used by frontend
    /// </summary>
    public class InboxItemDto
    {
        public long EntityRefId { get; set; }
        public long WfInsId { get; set; }
        
        [Required]
        public string FlowName { get; set; } = string.Empty;
        
        public string? FlowDesc { get; set; }
        
        [Required]
        public string StateName { get; set; } = string.Empty;
        
        public string? StateDesc { get; set; }
        
        public string? Route { get; set; }
        
        public string? TaskScreen { get; set; }
        
        public DateTime? WfDate { get; set; }
        
        public DateTime? StartTime { get; set; }
        
        public string? WfOwner { get; set; }
        
        public long? OwnerLoginId { get; set; }
        
        public string? Detail { get; set; }
        
        public decimal? Amount { get; set; }
        
        public string? Currency { get; set; }
        
        public string? WfInstanceLink { get; set; }
        
        public long? WfWorkflowDefId { get; set; }
        
        public string? WfActionStatusTypeCd { get; set; }
        
        public long? WfActionTaskInstanceId { get; set; }
        
        public string? WfActionTaskStatusTCd { get; set; }
        
        public long? WfActionDefId { get; set; }
        
        public string? Atanan { get; set; }
        
        public string? Bolum { get; set; }
        
        public string? BolumEn { get; set; }
    }

    /// <summary>
    /// Response DTO for inbox endpoints
    /// </summary>
    public class InboxResponseDto
    {
        public List<InboxItemDto> Inbox { get; set; } = new();
        public List<InboxItemDto> Delegated { get; set; } = new();
        public List<InboxItemDto> Commented { get; set; } = new();
    }
}