﻿using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Application.DTOs.Inbox
{

    public class InboxDto
    {
        [Column("FLOWNAME")]
        public string? FlowName { get; set; }
        [Column("FLOWDESC")]
        public string? FlowDesc { get; set; }
        [Column("ATANAN")]
        public string? Atanan { get; set; }
        [Column("STATENAME")]
        public string? StateName { get; set; }
        [Column("STATEDESC")]
        public string? StateDesc { get; set; }
        [Column("WFINSID")]
        public long WfInsId { get; set; }
        [Column("WFINSTANCEDEF")]
        public string? WfInstanceDef { get; set; }
        [Column("ROUTE")]
        public string? Route { get; set; }
        [Column("WFOWNER")]
        public string? WfOwner { get; set; }
        [Column("BOLUM")]
        public string? Bolum { get; set; }
        [Column("BOLUM_EN")]
        public string? BolumEn { get; set; }
        [Column("WF_WORKFLOW_DEF_ID")]
        public long? WfWorkflowDefId { get; set; }
        [Column("LASTLOGINID")]
        public long? LastLoginId { get; set; }
        [Column("LASTLOGINEMAIL")]
        public string? LastLoginEmail { get; set; }
        [Column("WFLASTMODIFIEDBY")]
        public string? WfLastModifiedBy { get; set; }
        [Column("WFLASTMODIFIEDBY_NOM")]
        public string? WfLastModifiedByNom { get; set; }
        [Column("DETAIL")]
        public string? Detail { get; set; }
        [Column("AMOUNT")]
        public string? Amount { get; set; }
        [Column("CURRENCY")]
        public string? Currency { get; set; }
        [Column("AMOUNTTTL")]
        public int? Amounttl { get; set; }
        [Column("WFINSTANCELINK")]
        public string? WfInstanceLink { get; set; }
        [Column("MWFINSTANCELINK")]
        public string? MwfInstanceLink { get; set; }
        [Column("WFDATE")]
        public DateTime? WfDate { get; set; }
        [Column("TOPLUONAYDURUM")]
        public bool? TopluOnayDurum { get; set; }
        [Column("WF_WORKFLOW_ENTITY")]
        public string? WfWorkflowEntity { get; set; }
        [Column("WF_WORKFLOW_ENTITY_VALUE")]
        public string? WfWorkflowEntityValue { get; set; }
    }

}
