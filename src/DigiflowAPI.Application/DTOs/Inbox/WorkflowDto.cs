﻿using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Domain.DTOs.Inbox
{

    public class WorkflowDto
    {
        public string? FlowDesc { get; set; }
        public long? Atanan { get; set; }
        public string? FlowName { get; set; }
        public string? StateName { get; set; }
        public long WfInsId { get; set; }
        public string? WfInstanceDef { get; set; }
        public char? WfOwner { get; set; }
        public long? WfWorkflowDefId { get; set; }
        public long? LastLoginId { get; set; }
        public char? WfLastModifiedBy { get; set; }
        public char? WfLastModifiedByNom { get; set; }
        public DateTime? WfDate { get; set; }
        public string? StateDesc { get; set; }
        public string? Bolum { get; set; }
        public string? Detail { get; set; }
        public string? Amount { get; set; }
        public string? Currency { get; set; }
        public int? Amounttl { get; set; }
        public bool? TopluOnayDurum { get; set; }
        public string? WfWorkflowEntity { get; set; }
        public string? WfWorkflowEntityValue { get; set; }
        public string? MwfInstanceLink { get; set; }
        public string? wfInstanceLink { get; set; }
    }

}
