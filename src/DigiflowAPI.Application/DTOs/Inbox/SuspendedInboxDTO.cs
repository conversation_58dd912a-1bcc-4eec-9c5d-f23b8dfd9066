﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiflowAPI.Application.DTOs.Inbox
{
    public class SuspendedInboxDTO
    {
        [Key]
        [Column("WF_INS_ID")]
        public long wfInsId { get; set; }
        [Column("NAME")]
        public string name { get; set; }
        [Column("WF_DATE")]
        public string wfDate { get; set; }
        [Column("WF_OWNER")]
        public string wfOwner { get; set; }
        [Column("TASK_SCREEN")]
        public string taskScreen { get; set; }
        public virtual string wfInstanceLink { get; set; }
        [Column("WF_LAST_MODIFIED_BY")]
        public string wfLastModifiedBy { get; set; }
    }
}
