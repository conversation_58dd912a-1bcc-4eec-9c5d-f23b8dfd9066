﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiflowAPI.Application.DTOs.Inbox
{
    public class GetWorkflowsOfAdminRequestDto
    {
        public string? AssignType { get; set; }
        public long? LoginId { get; set; }
        public string? WorkFlowType { get; set; }
        public string? WorkFlowDefId { get; set; }
        public string? WorkFlowId { get; set; }
        public string? StartDate { get; set; }
        public string? EndDate { get; set; }
        public string? IsSystemAdmin { get; set; }
        public string? LastAction { get; set; } = "0";
        public string? WfLastAction { get; set; } = "0";
    }
}
