namespace DigiflowAPI.Application.DTOs.History
{
    public class WorkflowHistoryDto
    {
        public long WfHistoryId { get; set; }
        public long WfWorkflowInstanceId { get; set; }
        public string Action { get; set; }
        public string ActionEng { get; set; }
        public long? ActionLoginId { get; set; }
        public long? AssignedLoginId { get; set; }
        public string Colors { get; set; }
        public string Comments { get; set; }
        public DateTime Dates { get; set; }
        public string State { get; set; }
        public string StateDesc { get; set; }
        public string Users { get; set; }
        public string WfDefDesc { get; set; }
        public string WfDefName { get; set; }
        public long WfWorkflowDefId { get; set; }
        public string WfWorkflowHistoryTypeCd { get; set; }
    }
}