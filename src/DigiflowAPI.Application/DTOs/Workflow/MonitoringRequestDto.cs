﻿using DigiflowAPI.Application.DTOs.Common;

namespace DigiflowAPI.Domain.Entities.Workflow
{
    public class MonitoringRequestDto : WorkflowBaseDto
    {
        public long RequestId { get; set; }
        public long? FlowDefId { get; set; }
        public long? FlowInstanceId { get; set; }
        public long? FlowTypeId { get; set; }
        public string? FlowTypeName { get; set; }
        public long? PersonelId { get; set; }
        public string? FlowDefIdList { get; set; }
        public long? IsActive { get; set; }
        public long? OwnerLoginId { get; set; }

    }
}