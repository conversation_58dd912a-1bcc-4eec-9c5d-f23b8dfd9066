﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DigiflowAPI.Application.DTOs.Workflow
{
    public class CreateWorkflowRequestDto
    {
        public string EntityJson { get; set; }
        public long WorkFlowDefinitionId { get; set; }
        public long LoginId { get; set; }
        public string GrupKod { get; set; }
        public string <PERSON>orNo { get; set; }
        public string DetailJson { get; set; }
        public string Detail2Json { get; set; }
        public string Detail3Json { get; set; }
        public string Detail4Json { get; set; }
        public CreateWorkflowRequestDto()
        {
            EntityJson = "{}";
        }
    }
}