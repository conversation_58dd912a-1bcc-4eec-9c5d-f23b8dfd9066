﻿namespace DigiflowAPI.Application.DTOs.Workflow.History
{
    public class WorkflowHistoryDto
    {

        public long? WfHistoryId { get; set; }

        public long? WfWorkflowInstanceId { get; set; }

        public string? WfWorkflowHistoryTypeCd { get; set; }

        public string? State { get; set; }

        public string? StateDesc { get; set; }

        public string? Users { get; set; }

        public string? Action { get; set; }

        public string? ActionEng { get; set; }

        public DateTime Dates { get; set; }

        public string? Comments { get; set; }

        public string? Colors { get; set; }

        public int AssignedLoginId { get; set; }

        public int ActionLoginId { get; set; }

        public int WfWorkflowDefId { get; set; }

        public string? WfDefName { get; set; }

        public string? WfDefDesc { get; set; }
    }
}
