﻿using DigiflowAPI.Domain.Enums;
using System.Text.Json;
using System.Text.Json.Nodes;

namespace DigiflowAPI.Application.DTOs.Workflow.Operation
{
    public class ApprovalWorkflowRequestDto
    {
        public string WorkflowName { get; set; }
        public long InstanceId { get; set; }
        public long ActionTaskInstanceId { get; set; }
        public long LoginUserId { get; set; }
        public long AssignedUserId { get; set; }
        public bool IsWfContextSave { get; set; }
        public string? Comment { get; set; }
        public Nullable<WorkflowHistoryActionType> ActionType { get; set; } = null;
        public long? SendTaskUserId { get; set; }
        public UpdateEntityRequestDto? UpdateEntity { get; set; }
    }
}
