﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace DigiflowAPI.Application.DTOs.Workflow.Operation
{
    public class CreateWorkflowRequestDto
    {
        [JsonConverter(typeof(JsonElementConverter))]
        public JsonElement EntityJson { get; set; }
        public long? WorkFlowDefinitionId { get; set; }
        public string WorkflowName { get; set; }
        public long LoginId { get; set; }
        public string? GrupKod { get; set; }
        public string? RaporNo { get; set; }
        [JsonConverter(typeof(JsonElementConverter))]
        public JsonElement? DetailJson { get; set; }
        [JsonConverter(typeof(JsonElementConverter))]
        public JsonElement? Detail2Json { get; set; }
        [JsonConverter(typeof(JsonElementConverter))]
        public JsonElement? Detail3Json { get; set; }
        [JsonConverter(typeof(JsonElementConverter))]
        public JsonElement? Detail4Json { get; set; }
        public JsonElement? UpdateEntity { get; set; }
    }

    public class JsonElementConverter : JsonConverter<JsonElement>
    {
        public override JsonElement Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            using (JsonDocument document = JsonDocument.ParseValue(ref reader))
            {
                return document.RootElement.Clone();
            }
        }

        public override void Write(Utf8JsonWriter writer, JsonElement value, JsonSerializerOptions options)
        {
            value.WriteTo(writer);
        }
    }
}