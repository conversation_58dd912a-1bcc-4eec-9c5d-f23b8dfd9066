﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace DigiflowAPI.Application.DTOs.Workflow.Operation
{
    public class ObjectToInferredTypesConverter : JsonConverter<Dictionary<string, object>>
    {
        public override Dictionary<string, object> Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType != JsonTokenType.StartObject)
            {
                throw new JsonException();
            }
            var dictionary = new Dictionary<string, object>();
            while (reader.Read())
            {
                if (reader.TokenType == JsonTokenType.EndObject)
                {
                    return dictionary;
                }
                if (reader.TokenType != JsonTokenType.PropertyName)
                {
                    throw new JsonException();
                }
                var propertyName = reader.GetString();
                reader.Read();
                dictionary.Add(propertyName, ExtractValue(ref reader));
            }
            throw new JsonException();
        }

        private object ExtractValue(ref Utf8JsonReader reader)
        {
            switch (reader.TokenType)
            {
                case JsonTokenType.True:
                    return true;
                case JsonTokenType.False:
                    return false;
                case JsonTokenType.Number:
                    if (reader.TryGetInt64(out long l))
                        return l;
                    return reader.GetDouble();
                case JsonTokenType.String:
                    if (reader.TryGetDateTime(out DateTime datetime))
                        return datetime;
                    return reader.GetString();
                case JsonTokenType.StartArray:
                    var list = new List<object>();
                    while (reader.Read() && reader.TokenType != JsonTokenType.EndArray)
                    {
                        list.Add(ExtractValue(ref reader));
                    }
                    return list;
                case JsonTokenType.StartObject:
                    return Read(ref reader, typeof(Dictionary<string, object>), new JsonSerializerOptions());
                default:
                    throw new JsonException($"'{reader.TokenType}' is not supported");
            }
        }

        public override void Write(Utf8JsonWriter writer, Dictionary<string, object> value, JsonSerializerOptions options)
        {
            JsonSerializer.Serialize(writer, value, options);
        }
    }

    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum EntityFieldType
    {
        Username = 0,
        LoginId = 1
    }

    public class UpdateEntityRequestDto
    {
        public long Id { get; set; }
        [JsonConverter(typeof(JsonElementConverter))]
        public JsonElement Properties { get; set; }
        public EmailRuleDto? EmailRule { get; set; }
    }

    public class EmailRuleDto
    {
        public long? MailTemplateId { get; set; }
        public string? LogicalGroupName { get; set; }
        public long WorkflowInstanceId { get; set; }
        public Dictionary<string, EntityFieldType>? EntityFields { get; set; }
        public Dictionary<string, string>? CustomParameters { get; set; }
    }

}