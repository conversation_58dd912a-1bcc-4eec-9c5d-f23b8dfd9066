﻿namespace DigiflowAPI.Application.DTOs.Workflow.Responses
{
    public class WorkflowResponseDto<TResponse>
    {
        public TResponse? Detail { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public string SuccessMessage { get; set; } = string.Empty;

        public WorkflowResponseDto() { }

        public WorkflowResponseDto(TResponse detail)
        {
            Detail = detail;
        }

        public static WorkflowResponseDto<TResponse> FromDetail(TResponse detail)
        {
            return new WorkflowResponseDto<TResponse> {
                Detail = detail 
            };
        }
    }
}
