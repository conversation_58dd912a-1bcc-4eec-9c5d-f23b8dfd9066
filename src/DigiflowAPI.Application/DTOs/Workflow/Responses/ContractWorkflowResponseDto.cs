﻿using DigiflowAPI.Application.DTOs.Common;
using DigiflowAPI.Application.DTOs.Organization;
using DigiflowAPI.Domain.DTOs.Organization;
using DigiflowAPI.Domain.Entities.Workflow;

namespace DigiflowAPI.Application.DTOs.Workflow.Responses
{
    public class ContractWorkflowResponseDto : IWorkflowResponse<ContractNewWorkflowLoadingDto, ContractEnableControlsDto, ContractLoadDataBindingDto, ContractLoadEntityToControlsDto>
    {
        public new ContractNewWorkflowLoadingDto? NewWorkflowLoadingDto { get; set; }
        public new ContractEnableControlsDto? EnableControlsDto { get; set; }
        public new ContractLoadDataBindingDto? LoadDataBindingDto { get; set; }
        public new ContractLoadEntityToControlsDto? LoadEntityToControlsDto { get; set; }
    }

    public class ContractNewWorkflowLoadingDto
    {
        public ContractNewWorkflowLoadingDto()
        {
            Firms = new List<SelectOptionDto>();
            ContractCategories = new List<SelectOptionDto>();
            OrganizationHierarchy = new OrganizationSchemaParamsDto();
            WorkflowAdmins = new WorkflowAdmin();
            SharePointPath = string.Empty;
        }
        public IEnumerable<SelectOptionDto> Firms { get; set; }
        public IEnumerable<SelectOptionDto> ContractCategories { get; set; }
        public OrganizationSchemaParamsDto? OrganizationHierarchy { get; set; }
        public WorkflowAdmin WorkflowAdmins { get; set; }
        public string SharePointPath { get; set; }
    }

    public class ContractEnableControlsDto
    {
        public ContractEnabilityControlsDto? EnabilitySettings { get; set; }
        public ContractVisiblityControlsDto? VisibilitySettings { get; set; }
    }

    public class ContractLoadDataBindingDto
    {
        public IEnumerable<SelectOptionDto> Firms { get; set; }
        public IEnumerable<SelectOptionDto> ContractCategories { get; set; }
        public string WorkflowState { get; set; }
        public string SharePointFinalPath { get; set; }
    }

    public class ContractLoadEntityToControlsDto
    {
        public ContractEntity? Entity { get; set; }
    }

    public class ContractEnabilityControlsDto
    {
        public bool CanEditOrgTree { get; set; } = true;
        public bool CanEditParties { get; set; } = true;
        public bool CanEditFirms { get; set; } = true;
        public bool CanEditTaxNo { get; set; } = true;
        public bool CanEditTaxRegion { get; set; } = true;
        public bool CanEditStartDate { get; set; } = true;
        public bool CanEditEndDate { get; set; } = true;
        public bool CanEditOther { get; set; } = true;
        public bool CanEditContractCategory { get; set; } = true;
        public bool CanEditSubject { get; set; } = true;
        public bool CanEditPaymentAmount { get; set; } = true;
        public bool CanEditPaymentType { get; set; } = true;
        public bool CanEditDescription { get; set; } = true;
        public bool CanEditContractTermination { get; set; } = true;
        public bool CanEditCurePeriodDay { get; set; } = true;
        public bool CanEditUrunServis { get; set; } = true;
        public bool CanEditConfidentiality { get; set; } = true;
        public bool CanEditPunishmentClauses { get; set; } = true;
        public bool CanEditContractControlDate { get; set; } = true;
        public bool CanEditContractType { get; set; } = true;
        public bool CanEditPaymentCurrencyType { get; set; } = true;
        public bool CanPressUploadButton { get; set; } = true;
        public bool CanSendBack { get; set; } = false;
    }

    public class ContractVisiblityControlsDto
    {
        public bool CanSeeOrgTree { get; set; } = true;
        public bool CanSeeParties { get; set; } = true;
        public bool CanSeeFirms { get; set; } = true;
        public bool CanSeeTaxNo { get; set; } = true;
        public bool CanSeeTaxRegion { get; set; } = true;
        public bool CanSeeStartDate { get; set; } = true;
        public bool CanSeeEndDate { get; set; } = true;
        public bool CanSeeOther { get; set; } = true;
        public bool CanSeeContractCategory { get; set; } = true;
        public bool CanSeeSubject { get; set; } = true;
        public bool CanSeePaymentAmount { get; set; } = true;
        public bool CanSeePaymentType { get; set; } = true;
        public bool CanSeeDescription { get; set; } = true;
        public bool CanSeeContractTermination { get; set; } = true;
        public bool CanSeeCurePeriodDay { get; set; } = true;
        public bool CanSeeConfidentiality { get; set; } = true;
        public bool CanSeePunishmentClauses { get; set; } = true;
        public bool CanSeeContractControlDate { get; set; } = true;
        public bool CanSeeContractType { get; set; } = true;
        public bool CanSeePaymentCurrencyType { get; set; } = true;
        public bool CanSeeUploadButton { get; set; } = true;
    }
}