﻿using DigiflowAPI.Application.DTOs.Common;
using DigiflowAPI.Application.DTOs.Organization;
using DigiflowAPI.Domain.DTOs.Organization;
using DigiflowAPI.Domain.Entities.Workflow;

namespace DigiflowAPI.Application.DTOs.Workflow.Responses
{
    public class MonitoringRequestWorkflowResponseDto : IWorkflowResponse<MonitoringRequestNewWorkflowLoadingDto, MonitoringRequestEnableControlsDto, MonitoringRequestLoadDataBindingDto, MonitoringRequestLoadEntityToControlsDto>
    {
        public new MonitoringRequestNewWorkflowLoadingDto? NewWorkflowLoadingDto { get; set; }
        public new MonitoringRequestEnableControlsDto? EnableControlsDto { get; set; }
        public new MonitoringRequestLoadDataBindingDto? LoadDataBindingDto { get; set; }
        public new MonitoringRequestLoadEntityToControlsDto? LoadEntityToControlsDto { get; set; }
    }

    public class MonitoringRequestNewWorkflowLoadingDto
    {
        public IEnumerable<SelectOptionDto> Workflows { get; set; }
        public OrganizationSchemaParamsDto? OrganizationHierarchy { get; set; }
        public WorkflowAdmin WorkflowAdmins { get; set; }
        public string WorkflowOwner { get; set; }
    }

    public class MonitoringRequestEnableControlsDto
    {
        public MonitoringRequestEnabilityControlsDto? EnabilitySettings { get; set; }
        public MonitoringRequestVisiblityControlsDto? VisibilitySettings { get; set; }
    }

    public class MonitoringRequestLoadDataBindingDto
    {
        public string WorkflowState { get; set; }
        public IEnumerable<SelectOptionDto> Workflows { get; set; }
    }

    public class MonitoringRequestLoadEntityToControlsDto
    {
        public MonitoringRequestEntity? Entity { get; set; }
    }

    public class MonitoringRequestEnabilityControlsDto
    {
        public bool CanEditFlowType { get; set; } = true;
        public bool CanEditWorkflow { get; set; } = true;
        public bool CanEditWorkflowInstanceId { get; set; } = true;
        public bool CanEditDescription { get; set; } = true;
        public bool CanEditOrgTree { get; set; } = true;
        public bool CanSelectWorkflowOwner { get; set; } = false;
    }

    public class MonitoringRequestVisiblityControlsDto
    {
        public bool CanSeeFlowType { get; set; } = true;
        public bool CanSeeWorkflow { get; set; } = true;
        public bool CanSeeWorkflowInstanceId { get; set; } = true;
        public bool CanSeeDescription { get; set; } = true;
    }
}