﻿namespace DigiflowAPI.Application.DTOs.Workflow.Responses
{
    public interface IBaseWorkflowResponse
    {
        string SuccessMessage { get; set; }
        string ErrorMessage { get; set; }
        WorkflowResponseDto<DelegationWorkflowResponseDto> NewWorkflowLoading { get; set; }
        WorkflowResponseDto<DelegationWorkflowResponseDto> EnableControls { get; set; }
        WorkflowResponseDto<DelegationWorkflowResponseDto> LoadDataBinding { get; set; }
        WorkflowResponseDto<DelegationWorkflowResponseDto> LoadEntityToControls { get; set; }

    }
}
