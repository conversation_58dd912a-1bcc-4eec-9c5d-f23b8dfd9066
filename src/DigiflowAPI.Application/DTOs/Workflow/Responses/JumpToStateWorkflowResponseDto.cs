﻿using DigiflowAPI.Application.DTOs.Organization;
using DigiflowAPI.Domain.Entities.Workflow;
using DigiflowAPI.Domain.Entities.Framework;

namespace DigiflowAPI.Application.DTOs.Workflow.Responses
{
    public class JumpToStateWorkflowResponseDto : IWorkflowResponse<JumpToStateNewWorkflowLoadingDto, JumpToStateEnableControlsDto, JumpToStateLoadDataBindingDto, JumpToStateLoadEntityToControlsDto>
    {
        public new JumpToStateNewWorkflowLoadingDto? NewWorkflowLoadingDto { get; set; }
        public new JumpToStateEnableControlsDto? EnableControlsDto { get; set; }
        public new JumpToStateLoadDataBindingDto? LoadDataBindingDto { get; set; }
        public new JumpToStateLoadEntityToControlsDto? LoadEntityToControlsDto { get; set; }
    }

    public class JumpToStateNewWorkflowLoadingDto
    {
        public RefInstanceDto RefInstance { get; set; }
        public WorkflowAdmin WorkflowAdmins { get; set; }
        public string StarterUser { get; set; }
        public string[] AssignerUsers { get; set; }

    }

    public class JumpToStateEnableControlsDto
    {
        public JumpToStateEnabilityControlsDto? EnabilitySettings { get; set; }
        public JumpToStateVisiblityControlsDto? VisibilitySettings { get; set; }
    }

    public class JumpToStateLoadDataBindingDto
    {
        public string WorkflowName { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
    }

    public class JumpToStateLoadEntityToControlsDto
    {
        public JumpToStateEntity? Entity { get; set; }
        public RefInstanceDto? RefInstance { get; set; }
        public string StarterUser { get; set; }
        public string[] AssignerUsers { get; set; }
    }

    public class JumpToStateEnabilityControlsDto
    {
        public bool canEditJumpToStateComment { get; set; }
        public bool canJumpToState { get; set; }
        public string canJumpToStateMessage { get; set; }
    }

    public class JumpToStateVisiblityControlsDto
    {

    }

    public class JumpToStateSupervisorDto
    {
        public long LoginId { get; set; }
        public required string Fullname { get; set; }
    }

    public class RefInstanceDto(FWfWorkflowInstance instance)
    {
        public long WfWorkflowInstanceId { get; set; } = instance.WfWorkflowInstanceId;
        public string WorkflowName { get; set; } = instance.WfWorkflowDef.Name;
        public string StateName { get; set; } = instance.WfCurrentState?.WfStateDef?.Name??"";
        public long StateInstanceId{ get; set; } = instance.WfCurrentState?.WfStateInstanceId??0;
        public long OwnerLoginId { get; set; } = instance.OwnerLogin.LoginId;
        public string FlowLink { get; set; }
    }

}