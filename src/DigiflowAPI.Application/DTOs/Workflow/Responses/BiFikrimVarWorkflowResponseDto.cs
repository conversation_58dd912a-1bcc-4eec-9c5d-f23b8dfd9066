﻿using DigiflowAPI.Application.DTOs.Common;
using DigiflowAPI.Application.DTOs.Organization;
using DigiflowAPI.Domain.DTOs.Organization;
using DigiflowAPI.Domain.Entities.Workflow;

namespace DigiflowAPI.Application.DTOs.Workflow.Responses
{
    public class BiFikrimVarWorkflowResponseDto : IWorkflowResponse<BiFikrimVarNewWorkflowLoadingDto, BiFikrimVarEnableControlsDto, BiFikrimVarLoadDataBindingDto, BiFikrimVarLoadEntityToControlsDto>
    {
        public new BiFikrimVarNewWorkflowLoadingDto? NewWorkflowLoadingDto { get; set; }
        public new BiFikrimVarEnableControlsDto? EnableControlsDto { get; set; }
        public new BiFikrimVarLoadDataBindingDto? LoadDataBindingDto { get; set; }
        public new BiFikrimVarLoadEntityToControlsDto? LoadEntityToControlsDto { get; set; }
    }

    public class BiFikrimVarNewWorkflowLoadingDto
    {
        public IEnumerable<SelectOptionDto> Locations { get; set; }
        public OrganizationSchemaParamsDto? OrganizationHierarchy { get; set; }
        public WorkflowAdmin WorkflowAdmins { get; set; }
    }

    public class BiFikrimVarEnableControlsDto
    {
        public BiFikrimVarEnabilityControlsDto? EnabilitySettings { get; set; }
        public BiFikrimVarVisiblityControlsDto? VisibilitySettings { get; set; }
    }

    public class BiFikrimVarLoadDataBindingDto
    {
        public string WorkflowState { get; set; }
        public IEnumerable<SelectOptionDto> Locations { get; set; }
        public IEnumerable<SelectOptionDto> Suggesters { get; set; }
        public IEnumerable<BiFikrimVarDetailDocEntity>? Files { get; set; }
        public string Team { get; set; }
    }

    public class BiFikrimVarLoadEntityToControlsDto
    {
        public BiFikrimVarEntity? Entity { get; set; }
    }

    public class BiFikrimVarEnabilityControlsDto
    {
        public bool CanEditSubject { get; set; } = true;
        public bool CanEditSuggestionDetail { get; set; } = true;
        public bool CanEditBenefit { get; set; } = true;
        public bool CanEditLocation { get; set; } = true;
        public bool CanEditSuggester { get; set; } = true;
        public bool CanEditPlanDate { get; set; } = true;
        public bool CanEditExecutionDate { get; set; } = true;
        public bool CanEditDelegationNote { get; set; } = true;
        public bool CanPressUpdateButton { get; set; } = true;
        public bool CanPressUploadButton { get; set; } = true;
        public bool CanEditUpdateComponent { get; set; } = true;
    }

    public class BiFikrimVarVisiblityControlsDto
    {
        public bool CanSeeSubject { get; set; } = true;
        public bool CanSeeSuggestionDetail { get; set; } = true;
        public bool CanSeeBenefit { get; set; } = true;
        public bool CanSeeLocation { get; set; } = true;
        public bool CanSeeSuggester { get; set; } = true;
        public bool CanSeePlanDate { get; set; } = true;
        public bool CanSeeExecutionDate { get; set; } = true;
        public bool CanSeeDelegationNote { get; set; } = true;
        public bool CanSeeUpdateButton { get; set; } = true;
        public bool CanSeeUploadButton { get; set; } = true;
    }

    public class BiFikrimVarSupervisorDto
    {
        public long LoginId { get; set; }
        public required string Fullname { get; set; }
    }
}
