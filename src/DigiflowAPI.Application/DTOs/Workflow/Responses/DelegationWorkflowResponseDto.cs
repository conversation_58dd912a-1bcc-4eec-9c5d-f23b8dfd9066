﻿using DigiflowAPI.Application.DTOs.Organization;
using DigiflowAPI.Domain.DTOs.Organization;
using DigiflowAPI.Domain.Entities.Workflow;

namespace DigiflowAPI.Application.DTOs.Workflow.Responses
{
    public class DelegationWorkflowResponseDto : IWorkflowResponse<DelegationNewWorkflowLoadingDto, DelegationEnableControlsDto, DelegationLoadDataBindingDto, DelegationLoadEntityToControlsDto>
    {
        public new DelegationNewWorkflowLoadingDto? NewWorkflowLoadingDto { get; set; }
        public new DelegationEnableControlsDto? EnableControlsDto { get; set; }
        public new DelegationLoadDataBindingDto? LoadDataBindingDto { get; set; }
        public new DelegationLoadEntityToControlsDto? LoadEntityToControlsDto { get; set; }
    }

    public class DelegationNewWorkflowLoadingDto
    {
        public List<DelegationSupervisorDto> Supervisors { get; set; }
        public OrganizationSchemaParamsDto? OrganizationHierarchy { get; set; }
        public WorkflowAdmin WorkflowAdmins {  get; set; }
    }

    public class DelegationEnableControlsDto
    {
        public DelegationEnabilityControlsDto? EnabilitySettings { get; set; }
        public DelegationVisiblityControlsDto? VisibilitySettings { get; set; }
    }

    public class DelegationLoadDataBindingDto
    {
        public string WorkflowName { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
    }

    public class DelegationLoadEntityToControlsDto
    {
        public DelegationWorkflowEntity? Entity { get; set; }
        public string WorkflowOwnerNameSurname {  get; set; }
    }

    public class DelegationEnabilityControlsDto
    {
        public bool CanEditSupervisor { get; set; } = true;
        public bool CanEditOrgTree { get; set; } = true;
        public bool CanEditWorkflows { get; set; } = true;
        public bool CanEditStartDate { get; set; } = true;
        public bool CanEditEndDate { get; set; } = true;
        public bool CanEditDelegationComment { get; set; } = true;
    }

    public class DelegationVisiblityControlsDto
    {
        public bool CanSeeSupervisor { get; set; } = false;
        public bool CanSeeOrgTree { get; set; } = true;
        public bool CanSeeWorkflows { get; set; } = true;
        public bool CanSeeStartDate { get; set; } = true;
        public bool CanSeeEndDate { get; set; } = true;
        public bool CanSeeDelegationComment { get; set; } = true;
    }

    public class DelegationSupervisorDto
    {
        public long LoginId { get; set; }
        public required string Fullname { get; set; }
    }
}
