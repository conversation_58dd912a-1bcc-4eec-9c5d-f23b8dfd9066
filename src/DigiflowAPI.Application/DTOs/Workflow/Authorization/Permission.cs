﻿
using DigiflowAPI.Application.DTOs.HelperDTOs;

namespace DigiflowAPI.Application.DTOs.Workflow.Authorization
{
    public class Permission
    {
        public Permission() { }
        public Permission(bool canCreate, bool canApproval, bool canReject, bool canForward, bool canSendtoCommend, bool canSendRequestToComment, bool canSuspend, bool canResume, bool canCancel, bool canFinalize, bool canSendTask, bool canConditionalAccept, bool canCorrection, bool canAddToComment, bool canRollBack, bool canSendBack, long forwardLogicalGroupId, long sendCommentLogicalGroupId, IEnumerable<MemberInfoDto> sendTaskLogicalGroupItems, IEnumerable<MemberInfoDto> forwardLogicalGroupUserList, IEnumerable<MemberInfoDto> sendCommentLogicalGroupUserList, long sendTaskLogicalGroupId, bool canFileUpload)
        {
            CanCreate = canCreate;
            CanApproval = canApproval;
            CanReject = canReject;
            CanForward = canForward;
            CanSendtoCommend = canSendtoCommend;
            CanSendRequestToComment = canSendRequestToComment;
            CanSuspend = canSuspend;
            CanResume = canResume;
            CanCancel = canCancel;
            CanFinalize = canFinalize;
            CanSendTask = canSendTask;
            CanConditionalAccept = canConditionalAccept;
            CanCorrection = canCorrection;
            CanAddToComment = canAddToComment;
            CanRollBack = canRollBack;
            CanSendBack = canSendBack;
            ForwardLogicalGroupId = forwardLogicalGroupId;
            SendCommentLogicalGroupId = sendCommentLogicalGroupId;
            SendTaskLogicalGroupItems = sendTaskLogicalGroupItems;
            ForwardLogicalGroupUserList = forwardLogicalGroupUserList;
            SendCommentLogicalGroupUserList = sendCommentLogicalGroupUserList;
            SendTaskLogicalGroupId = sendTaskLogicalGroupId;
            CanFileUpload = canFileUpload;
        }

        public bool CanCreate { get; set; }
        public bool CanApproval { get; set; }
        public bool CanReject { get; set; }
        public bool CanForward { get; set; }
        public bool CanSendtoCommend { get; set; }
        public bool CanSendRequestToComment { get; set; }
        public bool CanSuspend { get; set; }
        public bool CanResume { get; set; }
        public bool CanCancel { get; set; }
        public bool CanFinalize { get; set; }
        public bool CanSendTask { get; set; }
        public bool CanConditionalAccept { get; set; }
        public bool CanCorrection { get; set; }
        public bool CanAddToComment { get; set; }
        public bool CanRollBack { get; set; }
        public bool CanSendBack { get; set; }
        public long ForwardLogicalGroupId { get; set; }
        public long SendCommentLogicalGroupId { get; set; }
        public IEnumerable<MemberInfoDto> SendTaskLogicalGroupItems { get; set; }
        public IEnumerable<MemberInfoDto> ForwardLogicalGroupUserList { get; set; }
        public IEnumerable<MemberInfoDto> SendCommentLogicalGroupUserList { get; set; }

        public long SendTaskLogicalGroupId { get; set; }
        public bool CanFileUpload { get; set; }
        public void SetPermissions(bool permission)
        {
            CanCreate = permission;
            CanApproval = permission;
            CanReject = permission;
            CanForward = permission;
            CanSendtoCommend = permission;
            CanSendRequestToComment = permission;
            CanSuspend = permission;
            CanResume = permission;
            CanCancel = permission;
            CanFinalize = permission;
            CanSendTask = permission;
            CanConditionalAccept = permission;
            CanCorrection = permission;
            CanAddToComment = permission;
            CanRollBack = permission;
            CanSendBack = permission;
            ForwardLogicalGroupId = permission == true ? ForwardLogicalGroupId : 0;
            SendCommentLogicalGroupId = permission == true ? SendCommentLogicalGroupId : 0;
            SendTaskLogicalGroupId = permission == true ? SendTaskLogicalGroupId : 0;
            CanFileUpload = permission;
        }
        public bool CheckPermissionByAction(string action)
        {
            return action.ToLower() switch
            {
                "create" => CanCreate,
                "approve" => CanApproval,
                "reject" => CanReject,
                "rollback" => CanRollBack,
                "forward" => CanForward,
                "sendtocommend" => CanSendtoCommend,
                "sendrequesttocomment" => CanSendRequestToComment,
                "suspend" => CanSuspend,
                "resume" => CanResume,
                "cancel" => CanCancel,
                "finalize" => CanFinalize,
                "sendtask" => CanSendTask,
                "conditionalaccept" => CanConditionalAccept,
                "correction" => CanCorrection,
                "addtocomment" => CanAddToComment,
                "sendback" => CanSendBack,
                "fileupload" => CanFileUpload,
                _ => false
            };
        }
    }
}
