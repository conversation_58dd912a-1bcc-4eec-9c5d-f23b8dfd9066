﻿namespace DigiflowAPI.Application.DTOs.Workflow.Authorization
{
    public class WorkflowAuthorizationResponseDto
    {
        public TabVisibility TabVisibility { get; set; } = new TabVisibility();
        public Permission ActionPermissions { get; set; } = new Permission();
        public bool CanSeeWorkflow { get; set; } = true;
        public bool IsWorkflowSuspended { get; set; } = false;
        public string Message { get; set; } = "";
    }
}