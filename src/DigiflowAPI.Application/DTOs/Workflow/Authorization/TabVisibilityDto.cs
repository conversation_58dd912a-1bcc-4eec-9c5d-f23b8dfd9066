﻿namespace DigiflowAPI.Application.DTOs.Workflow.Authorization
{
    public class TabVisibility
    {
        public bool NewRequestTabVisible { get; set; }
        public bool ApproveRejectTabVisible { get; set; }
        public bool ForwardTabVisible { get; set; }
        public bool SuspendResumeTabVisible { get; set; }
        public bool AbortTabVisible { get; set; }
        public bool AddCommentTabVisible { get; set; }
        public bool RollbackTabVisible { get; set; }
        public bool SendToCommentTabVisible { get; set; }
        public bool FileUploadTabVisible { get; set; }

        public void SetAllTabsVisible()
        {
            NewRequestTabVisible = true;
            ApproveRejectTabVisible = true;
            ForwardTabVisible = true;
            SuspendResumeTabVisible = true;
            AbortTabVisible = true;
            AddCommentTabVisible = true;
            RollbackTabVisible = true;
        }

        public void SetAllTabsInvisible()
        {
            NewRequestTabVisible = false;
            ApproveRejectTabVisible = false;
            ForwardTabVisible = false;
            SuspendResumeTabVisible = false;
            AbortTabVisible = false;
            AddCommentTabVisible = false;
            RollbackTabVisible = false;
        }

        public bool CheckIfAnyTabVisible()
        {
            return NewRequestTabVisible || ApproveRejectTabVisible || ForwardTabVisible || SuspendResumeTabVisible || AbortTabVisible || AddCommentTabVisible || RollbackTabVisible;
        }

        public bool CheckIfAllInvisible()
        {
            return !NewRequestTabVisible && !ApproveRejectTabVisible && !ForwardTabVisible && !SuspendResumeTabVisible && !AbortTabVisible && !AddCommentTabVisible && !RollbackTabVisible;
        }
    }
}
