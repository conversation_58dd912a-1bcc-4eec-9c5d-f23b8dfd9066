﻿using DigiflowAPI.Application.DTOs.Common;
using DigiflowAPI.Domain.Entities.Workflow;

namespace DigiflowAPI.Application.DTOs.Workflow
{
    public class DelegationWorkflowDto : WorkflowBaseDto
    {
        public long DelegationRequestId { get; set; }
        public string? DelegationComment { get; set; }
        public long? OwnerLoginId { get; set; }
        public DateTime? StartTime { get; set; }
        public long? DelegatedLoginId { get; set; }
        public DateTime? EndTime { get; set; }
        public long? WorkflowDefId { get; set; }
        public string? WorkFlowIds { get; set; }
        public string? OwnerLoginName { get; set; }
        public string? ActiveLoginName { get; set; }
    }
}