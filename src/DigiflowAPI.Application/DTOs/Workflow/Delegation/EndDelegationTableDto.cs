﻿using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Application.DTOs.Workflow.Delegation
{
    public class EndDelegationTableDto
    {
        [Column("WF_DELEGATION_ID")]
        public long WfDelegationId { get; set; }
        [Column("NAME")]
        public string? Name { get; set; }
        [Column("DELEGATION_OWNER_REF_ID")]
        public long? DelegationOwnerRefId { get; set; }
        [Column("DELEGATION_REQUEST_ID")]
        public long? DelegationRequestId { get; set; }
        [Column("DELEGATE_REF_ID")]
        public long? DelegateRefId{ get; set; }
        [Column("OWNER_NAMESURNAME")]
        public string? OwnerNameSurname { get; set; }
        [Column("NAME_SURNAME")]
        public string? NameSurname { get; set; }
        [Column("DELEGATION_START_DATE")]
        public DateTime? DelegationStartDate { get; set; }
        [Column("DELEGATION_END_DATE")]
        public DateTime? DelegationEndDate { get; set; }
        [Column("WF_WORKFLOW_DEF_ID")]
        public long? WorkflowDefId{ get; set; }
    }
}
