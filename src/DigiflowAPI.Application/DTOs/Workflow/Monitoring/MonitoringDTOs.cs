﻿using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Application.DTOs.Workflow.Monitoring
{
    public class EndMonitoringRequest
    {
        public EndMonitoringRequestDto[] Monitorings { get; set; }
    }

    public class EndMonitoringRequestDto
    {
        public long MonitoringRequestId { get; set; }
        public long CreatedBy { get; set; }
        public long FlowTypeId { get; set; }
    }

    public class EndMonitoringTableDto
    {
        [Column("WF_WORKFLOW_INSTANCE_ID")]
        public long WfWorkflowInstanceId { get; set; }
        [Column("OWNER_LOGIN_ID")]
        public long? OwnerLoginId { get; set; }
        [Column("FLOW_TYPE_ID")]
        public long? flowTypeId { get; set; }
        [Column("ASSIGNED_OWNER_REF_ID")]
        public long? AssignedOwnerRefId { get; set; }
        [Column("WF_WORKFLOW_DEF_ID")]
        public long? WfWorkflowDefId { get; set; }
        [Column("PERSONEL_ID")]
        public long? PersonelId { get; set; }
        [Column("MONITORING_REQUEST_ID")]
        public long MonitoringRequestId { get; set; }
        [Column("OWNER_NAME_SURNAME")]
        public string OwnerNameSurname { get; set; }
        [Column("NAME_SURNAME")]
        public string NameSurname { get; set; }
        [Column("AKIS_NO")]
        public long AkisNo { get; set; }
        [Column("FLOWTYPE")]
        public string Flowtype { get; set; }
        [Column("FLOWNAME")]
        public string Flowname { get; set; }
        [Column("WFLINK")]
        public string Wflink { get; set; }


    }
}