﻿using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Application.DTOs.Workflow.XML.Definition
{
    public class WorkflowXmlDefinitionDto
    {
        public string FlowName { get; set; }
        public string Page { get; set; }
        public string Value { get; set; }
        public string? Route { get; set; }
        public string? OnWfStartOwner { get; set; }
        public string? OnWfStartAssigner { get; set; }
        public string? OnWfApproveOwner { get; set; }
        public string? OnWfApproveAssigner { get; set; }
        public string? OnWfRejectAssigner { get; set; }
        public string? OnWfRejectOwner { get; set; }
        public string? OnWfWorkflowApproveEndowner { get; set; }
        public string? OnWfWorkflowRejectEndowner { get; set; }
        public string? OnFlowEndInformation { get; set; }
        public string? OnFlowEndInformationToFirstList { get; set; }
        public string? OnFlowEndInformationLogicalGrp { get; set; }
        public string? OnFlowEndInformationToList { get; set; }
        public string? LastState { get; set; }
        public string? OnWfRejectFlowUsers { get; set; }
        public string? SendEndState { get; set; }
        public string? OnLastState { get; set; }
        public string? OnSendBack { get; set; }
        public string? OnWfStartInformationGroup { get; set; }
        public string? OnWfApproveInformationGroup { get; set; }
        public string? OnWfPrivateApproveOwner { get; set; }
        public string? OnFlowPrivateEndInformation { get; set; }
        public string? OnFlowCustomMailingLogicalGrp { get; set; }
        public string? OnFlowCustomMailTemplate { get; set; }
        public string? OnWfRelatedUserInformation { get; set; }
        public string? OnWfPrivateApprove { get; set; }
        public string? TableName { get; set; }
        public string? FieldName { get; set; }
    }
}