﻿using DigiflowAPI.Application.DTOs.Common;

namespace DigiflowAPI.Domain.Entities.Workflow
{
    public class BiFikrimVarDto : WorkflowBaseDto
    {
        public long BiFikrimVarRequestId { get; set; }
        public string? SuggestionDetail { get; set; }
        public string? Benefit { get; set; }
        public string? Location { get; set; }
        public string? SuggestersTeam { get; set; }
        public DateTime? PlanDate { get; set; }
        public DateTime? ExecutionDate { get; set; }
        public string? Subject { get; set; }
        public string? Suggester { get; set; }
        public string? SuggesterFullname { get; set; }
        public string? CreatorFullname { get; set; }
    }
}
