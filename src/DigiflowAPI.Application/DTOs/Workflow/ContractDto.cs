﻿namespace DigiflowAPI.Domain.Entities.Workflow
{
    public class ContractDto
    {
        public long RequestId { get; set; }
        public long? OwnerLoginId { get; set; }
        public string? Parties { get; set; }
        public string? Firms { get; set; }
        public string? TaxNo { get; set; }
        public string? TaxRegion { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Other { get; set; }
        public string? ContractCategory { get; set; }
        public string? Subject { get; set; }
        public decimal? PaymentAmount { get; set; }
        public string? PaymentCurrencyType { get; set; }
        public int? PaymentType { get; set; }
        public string? Description { get; set; }
        public int? CancelByDigiTurk { get; set; }
        public int? CancelTwoSide { get; set; }
        public int? CancelCurePeriod { get; set; }
        public int? CancelOther { get; set; }
        public int? CurePeriodDay { get; set; }
        public string? Confidence { get; set; }
        public string? PunishmentClauses { get; set; }
        public DateTime? ContractControlDate { get; set; }
        public int? ContractType { get; set; }
        public string? ContractSoftFile { get; set; }
        public string? LastComment { get; set; }
        public string? UrunServis { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? LastUpdated { get; set; }
        public long? CreatedBy { get; set; }
        public long? LastUpdatedBy { get; set; }
        public long? VersionID { get; set; }
        public string? ContractSignedSoftFile { get; set; }
    }
}
