﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DigiflowAPI.Application.DTOs.Workflow
{
    public class CreateWorkflowDto
    {
        public IEntity Entity { get; set; }
        public long WorkFlowDefinitionId { get; set; }
        public long LoginId { get; set; }
        public string GrupKod { get; set; }
        public string RaporNo { get; set; }
        public IDetailEntity DetailEntity1 { get; set; }
        public IDetailEntity DetailEntity2 { get; set; }
        public IDetailEntity DetailEntity3 { get; set; }
        public IDetailEntity DetailEntity4 { get; set; }
    }
}