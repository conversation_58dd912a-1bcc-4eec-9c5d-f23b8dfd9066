﻿using DigiflowAPI.Application.DTOs.Common;

namespace DigiflowAPI.Domain.Entities.Workflow
{
    public class JumpToStateDto : WorkflowBaseDto
    {
        public long JumpToStateRequestId { get; set; }
        public string? Description { get; set; }
        public long? FlowInstanceID { get; set; }
        public string? FlowName { get; set; }
        public string? Link { get; set; }
        public long? StateInstanceID { get; set; }
        public string? StateName { get; set; }
    }
}