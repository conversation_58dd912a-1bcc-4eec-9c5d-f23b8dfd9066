﻿using System;

namespace DigiflowAPI.Application.DTOs.Workflow
{
    public class ApprovalWorkflowRequestDto
    {
        public long InstanceId { get; set; }
        public long ActionTaskInstanceId { get; set; }
        public long LoginUserId { get; set; }
        public long? AssignedUserId { get; set; }
        public bool IsWfContextSave { get; set; }
        public string Comment { get; set; }
        public long? SendTaskUserId { get; set; }
        public Nullable<WorkflowHistoryActionType> ActionType { get; set; } = null;
    }
}