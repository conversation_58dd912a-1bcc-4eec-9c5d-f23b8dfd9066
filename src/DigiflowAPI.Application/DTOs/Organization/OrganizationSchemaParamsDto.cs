﻿using DigiflowAPI.Application.DTOs.Common;
using DigiflowAPI.Application.Interfaces.Services;

namespace DigiflowAPI.Application.DTOs.Organization
{
    public class OrganizationSchemaParamsDto : IOrganizationSchemaParams
    {
        public IEnumerable<OrgTreeSelectOptionDto> Departments { get; set; }
        public OrgTreeSelectOptionDto? SelectedDepartment { get; set; }
        public IEnumerable<OrgTreeSelectOptionDto>? Divisions { get; set; }
        public OrgTreeSelectOptionDto? SelectedDivision { get; set; }
        public IEnumerable<OrgTreeSelectOptionDto>? Units { get; set; }
        public OrgTreeSelectOptionDto? SelectedUnit { get; set; }
        public IEnumerable<OrgTreeSelectOptionDto>? Teams { get; set; }
        public OrgTreeSelectOptionDto? SelectedTeam { get; set; }
        public List<OrgTreeSelectOptionDto>? SubTeams { get; set; }
        public List<OrgTreeSelectOptionDto>? SelectedSubTeams { get; set; }
        public IEnumerable<OrgTreeSelectOptionDto>? Users { get; set; }
        public DepsPathSelectDto? DepsPath { get; set; }
    }

    public class OrgTreeSelectOptionDto : SelectOptionDto
    {
        public List<OrgTreeSelectOptionDto>? SubTeams { get; set; }
    }
}