﻿using DigiflowAPI.Application.DTOs.Common;

namespace DigiflowAPI.Application.DTOs.Organization
{
    public class SchemaDto
    {
        public IEnumerable<SelectOptionDto> Departments { get; set; }
        public IEnumerable<SelectOptionDto>? Divisions { get; set; }
        public IEnumerable<SelectOptionDto>? Units { get; set; }
        public IEnumerable<SelectOptionDto>? Teams { get; set; }
        public List<OrgTreeSelectOptionDto>? SubTeams { get; set; }
        public IEnumerable<SelectOptionDto>? Users { get; set; }
        public DepsPathSelectDto? DepsPath { get; set; }
    }
}
