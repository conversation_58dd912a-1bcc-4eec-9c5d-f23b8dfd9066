using System;
using System.ComponentModel.DataAnnotations;

namespace DigiflowAPI.Application.DTOs.Slide
{
  public class SlideDto
  {
    public int Id { get; set; }

    [Required]
    [MaxLength(200)]
    public string Title { get; set; } = string.Empty;

    [MaxLength(500)]
    public string? Description { get; set; }

    [MaxLength(500)]
    public string? ImageUrl { get; set; }

    [MaxLength(500)]
    public string? LinkUrl { get; set; }

    public int Order { get; set; }

    public char IsActive { get; set; } = '1';

    public DateTime CreatedDate { get; set; }

    public long? CreatedBy { get; set; }

    public DateTime? ModifiedDate { get; set; }
    public long? ModifiedBy { get; set; }
  }

  public class UpdateSlideDto
  {
    [Required]
    [MaxLength(200)]
    public string Title { get; set; } = string.Empty;

    [MaxLength(500)]
    public string? Description { get; set; }

    [MaxLength(500)]
    public string? ImageUrl { get; set; }

    [MaxLength(500)]
    public string? LinkUrl { get; set; }

    public int Order { get; set; }

    public bool IsActive { get; set; } = true;
  }
}
