﻿using Permission = DigiflowAPI.Application.DTOs.Workflow.Authorization.Permission;

namespace DigiflowAPI.Application.DTOs
{
    public class WorkflowAuthorizationInfo
    {
        public Permission Permissions { get; set; } = new Permission();
        public bool IsFlowAdmin { get; set; }
        public bool IsActionFlow { get; set; }
        public bool IsOwner { get; set; }
        public bool IsViewUser { get; set; }
        public bool IsReportAdmin { get; set; }
        public bool IsSystemAdmin { get; set; }
        public bool IsAssigned { get; set; }
        public bool IsLastActor { get; set; }
        public bool IsCommentedTo { get; set; }
        public bool IsOwnDelegated { get; set; }
        public string WorkflowStatus { get; set; } = string.Empty;
    }
}
