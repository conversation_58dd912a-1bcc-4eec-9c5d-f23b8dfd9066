# Workflow Type Resolution Guide

## Overview

We've updated the `WorkflowTypeHelper` class to dynamically resolve workflow types, entities, and workflow definition IDs from the database instead of using hardcoded dictionaries. This document provides guidance on how to use the new implementation in your code.

## Database Schema Changes

The `WF_XML_DEFINITION` table has been updated with the following new columns:

- `RESPONSE_TYPE_NAME` - The fully qualified name of the response DTO type
- `ENTITY_TYPE_NAME` - The fully qualified name of the entity type
- `TYPE_ASSEMBLY` - The assembly name containing the types

To apply these changes, run the SQL script located at:
`DigiflowAPI.Application\Scripts\UpdateWfXmlDefinition.sql`

## Using the WorkflowTypeHelper

### Dependency Injection

The `WorkflowTypeHelper` is now registered as a scoped service, so you should use dependency injection to get an instance of it:

```csharp
private readonly WorkflowTypeHelper _workflowTypeHelper;

public MyClass(WorkflowTypeHelper workflowTypeHelper)
{
    _workflowTypeHelper = workflowTypeHelper;
}
```

### Async Methods

Use the async methods wherever possible:

```csharp
// Get workflow DTO type
var responseType = await _workflowTypeHelper.GetWorkflowTypeAsync(workflowName);

// Get workflow entity type
var entityType = await _workflowTypeHelper.GetWorkflowEntityAsync(workflowName);

// Get workflow definition ID
var definitionId = await _workflowTypeHelper.GetWorkflowDefinitionIdAsync(workflowName);

// Get all workflow definitions
var definitions = await _workflowTypeHelper.GetAllWorkflowDefinitionsAsync();
```

### Backward Compatibility

For backward compatibility, the synchronous methods are still available, but they will internally use the async implementation:

```csharp
// These still work but are not recommended for new code
var responseType = _workflowTypeHelper.GetWorkflowType(workflowName);
var entityType = _workflowTypeHelper.GetWorkflowEntity(workflowName);
var definitionId = _workflowTypeHelper.GetWorkflowDefinitionId(workflowName);
```

### Updating Type Information

To update the type information in the database for a workflow:

```csharp
await _workflowTypeHelper.UpdateWorkflowTypeInfoInDatabaseAsync(
    "workflowRoute",
    typeof(MyResponseDto),
    typeof(MyEntity)
);
```

### Initial Setup

When the application starts, the `WorkflowTypeInitializer` will automatically populate the database with type information from the hardcoded dictionaries. This ensures a smooth transition from the old implementation to the new one.

## Benefits

1. **Dynamic Type Resolution** - Types are resolved at runtime from the database, eliminating the need to update code when adding new workflow types.
2. **Better Caching** - Type information is cached in memory for improved performance.
3. **Reflection-Based Fallback** - If a type is not found in the cache, the system will try to resolve it using reflection.
4. **Backward Compatibility** - Existing code that uses the static methods will continue to work.

## Migration Steps

1. Update your code to use dependency injection for `WorkflowTypeHelper`
2. Replace static method calls with their async counterparts
3. Test your code to ensure it works with the new implementation
4. Run the SQL script to update the database schema

For any questions or issues, please contact the development team.
