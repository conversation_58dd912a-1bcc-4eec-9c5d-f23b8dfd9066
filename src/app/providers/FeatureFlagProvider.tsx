import React, { createContext, useContext, useState, useCallback, useEffect, ReactNode } from 'react';

/**
 * Feature flag value types
 */
export type FeatureFlagValue = boolean | string | number | Record<string, unknown>;

/**
 * Feature flag configuration
 */
export interface FeatureFlag {
  key: string;
  value: FeatureFlagValue;
  description?: string;
  enabled: boolean;
  variant?: string;
  metadata?: Record<string, unknown>;
}

/**
 * Feature flags map
 */
export type FeatureFlags = Record<string, FeatureFlag>;

/**
 * Feature flag context interface
 */
interface FeatureFlagContextType {
  flags: FeatureFlags;
  isEnabled: (key: string) => boolean;
  getValue: <T = FeatureFlagValue>(key: string, defaultValue?: T) => T;
  getVariant: (key: string) => string | undefined;
  setFlag: (key: string, flag: Partial<FeatureFlag>) => void;
  setFlags: (flags: FeatureFlags) => void;
  resetFlags: () => void;
  refreshFlags: () => Promise<void>;
  isLoading: boolean;
  error: Error | null;
}

/**
 * Props for FeatureFlagProvider component
 */
interface FeatureFlagProviderProps {
  children: ReactNode;
  defaultFlags?: FeatureFlags;
  fetchFlags?: () => Promise<FeatureFlags>;
  onFlagChange?: (key: string, flag: FeatureFlag) => void;
  refreshInterval?: number;
  storageKey?: string;
  enableStorage?: boolean;
}

const FeatureFlagContext = createContext<FeatureFlagContextType | undefined>(undefined);

/**
 * Default feature flags
 */
const defaultFeatureFlags: FeatureFlags = {
  darkMode: {
    key: 'darkMode',
    value: true,
    enabled: true,
    description: 'Enable dark mode theme',
  },
  newUI: {
    key: 'newUI',
    value: false,
    enabled: false,
    description: 'Enable new UI components',
  },
  analytics: {
    key: 'analytics',
    value: true,
    enabled: true,
    description: 'Enable analytics tracking',
  },
};

/**
 * Feature flag provider that manages feature toggles and A/B testing
 * @component
 */
export const FeatureFlagProvider: React.FC<FeatureFlagProviderProps> = ({
  children,
  defaultFlags = defaultFeatureFlags,
  fetchFlags,
  onFlagChange,
  refreshInterval,
  storageKey = 'feature-flags',
  enableStorage = true,
}) => {
  const [flags, setFlagsState] = useState<FeatureFlags>(() => {
    if (enableStorage && typeof window !== 'undefined') {
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        try {
          return JSON.parse(stored);
        } catch (error) {
          console.error('Failed to parse stored feature flags:', error);
        }
      }
    }
    return defaultFlags;
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  /**
   * Save flags to storage
   */
  const saveToStorage = useCallback((flags: FeatureFlags) => {
    if (enableStorage && typeof window !== 'undefined') {
      try {
        localStorage.setItem(storageKey, JSON.stringify(flags));
      } catch (error) {
        console.error('Failed to save feature flags to storage:', error);
      }
    }
  }, [enableStorage, storageKey]);

  /**
   * Check if a feature flag is enabled
   */
  const isEnabled = useCallback((key: string): boolean => {
    const flag = flags[key];
    return flag?.enabled ?? false;
  }, [flags]);

  /**
   * Get feature flag value with type safety
   */
  const getValue = useCallback(<T = FeatureFlagValue>(
    key: string,
    defaultValue?: T
  ): T => {
    const flag = flags[key];
    if (!flag || !flag.enabled) {
      return defaultValue as T;
    }
    return flag.value as T;
  }, [flags]);

  /**
   * Get feature flag variant for A/B testing
   */
  const getVariant = useCallback((key: string): string | undefined => {
    const flag = flags[key];
    return flag?.variant;
  }, [flags]);

  /**
   * Set or update a single feature flag
   */
  const setFlag = useCallback((key: string, updates: Partial<FeatureFlag>) => {
    setFlagsState(prev => {
      const newFlags = {
        ...prev,
        [key]: {
          ...prev[key],
          key,
          ...updates,
        },
      };
      saveToStorage(newFlags);
      
      const updatedFlag = newFlags[key];
      if (onFlagChange) {
        onFlagChange(key, updatedFlag);
      }
      
      return newFlags;
    });
  }, [saveToStorage, onFlagChange]);

  /**
   * Set multiple feature flags
   */
  const setFlags = useCallback((newFlags: FeatureFlags) => {
    setFlagsState(newFlags);
    saveToStorage(newFlags);
  }, [saveToStorage]);

  /**
   * Reset flags to defaults
   */
  const resetFlags = useCallback(() => {
    setFlagsState(defaultFlags);
    saveToStorage(defaultFlags);
  }, [defaultFlags, saveToStorage]);

  /**
   * Refresh flags from remote source
   */
  const refreshFlags = useCallback(async () => {
    if (!fetchFlags) return;

    try {
      setIsLoading(true);
      setError(null);
      
      const remoteFlags = await fetchFlags();
      
      // Merge remote flags with local overrides if needed
      const mergedFlags = { ...flags, ...remoteFlags };
      
      setFlagsState(mergedFlags);
      saveToStorage(mergedFlags);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch feature flags'));
      console.error('Failed to refresh feature flags:', err);
    } finally {
      setIsLoading(false);
    }
  }, [fetchFlags, flags, saveToStorage]);

  // Initial load of flags
  useEffect(() => {
    refreshFlags();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Set up refresh interval
  useEffect(() => {
    if (!refreshInterval || !fetchFlags) return;

    const interval = setInterval(refreshFlags, refreshInterval);
    return () => clearInterval(interval);
  }, [refreshInterval, refreshFlags, fetchFlags]);

  // Listen for storage changes from other tabs
  useEffect(() => {
    if (!enableStorage || typeof window === 'undefined') return;

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === storageKey && e.newValue) {
        try {
          const newFlags = JSON.parse(e.newValue);
          setFlagsState(newFlags);
        } catch (error) {
          console.error('Failed to parse feature flags from storage event:', error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [enableStorage, storageKey]);

  const contextValue: FeatureFlagContextType = {
    flags,
    isEnabled,
    getValue,
    getVariant,
    setFlag,
    setFlags,
    resetFlags,
    refreshFlags,
    isLoading,
    error,
  };

  return (
    <FeatureFlagContext.Provider value={contextValue}>
      {children}
    </FeatureFlagContext.Provider>
  );
};

/**
 * Hook to access feature flag context
 * @returns Feature flag context
 * @throws Error if used outside of FeatureFlagProvider
 */
export const useFeatureFlags = (): FeatureFlagContextType => {
  const context = useContext(FeatureFlagContext);
  if (!context) {
    throw new Error('useFeatureFlags must be used within FeatureFlagProvider');
  }
  return context;
};

/**
 * Hook to check if a specific feature is enabled
 * @param key - Feature flag key
 * @returns Boolean indicating if feature is enabled
 */
export const useFeature = (key: string): boolean => {
  const { isEnabled } = useFeatureFlags();
  return isEnabled(key);
};

/**
 * Hook to get feature flag value
 * @param key - Feature flag key
 * @param defaultValue - Default value if flag is not found or disabled
 * @returns Feature flag value
 */
export const useFeatureValue = <T = FeatureFlagValue>(
  key: string,
  defaultValue?: T
): T => {
  const { getValue } = useFeatureFlags();
  return getValue(key, defaultValue);
};

/**
 * Hook to get feature variant for A/B testing
 * @param key - Feature flag key
 * @returns Feature variant or undefined
 */
export const useFeatureVariant = (key: string): string | undefined => {
  const { getVariant } = useFeatureFlags();
  return getVariant(key);
};

/**
 * Component for conditional rendering based on feature flag
 */
interface FeatureProps {
  flag: string;
  children: ReactNode;
  fallback?: ReactNode;
}

export const Feature: React.FC<FeatureProps> = ({ flag, children, fallback = null }) => {
  const isEnabled = useFeature(flag);
  return <>{isEnabled ? children : fallback}</>;
};