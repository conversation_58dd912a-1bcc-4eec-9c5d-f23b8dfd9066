﻿using System;
using System.IO;
using System.Reflection;
using System.Web.Http;

namespace DigiflowAPI.CompatibilityApi
{
    public class WebApiApplication : System.Web.HttpApplication
    {
        protected void Application_Start()
        {
            GlobalConfiguration.Configure(WebApiConfig.Register);
            CopyNHibernateConfig();
            InitializeNHibernate();

        }

        private void CopyNHibernateConfig()
        {
            try
            {
                string configSourcePath = @"\\dtl1iis3\Deployment\hibernate.cfg.local.xml";
                string configDestinationPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "hibernate.cfg.xml");

                if (File.Exists(configSourcePath))
                {
                    File.Copy(configSourcePath, configDestinationPath, overwrite: true);
                    System.Diagnostics.Debug.WriteLine("hibernate.cfg.xml file copied successfully.");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Source hibernate.cfg.xml file not found.");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error copying hibernate.cfg.xml: {ex.Message}");
            }
        }

        private void InitializeNHibernate()
        {
            try
            {
                var sessionFactory = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FSessionHelper.GetDTWorkflowSession();
                System.Diagnostics.Debug.WriteLine("NHibernate initialized successfully.");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing NHibernate: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
            }
        }
    }
}
