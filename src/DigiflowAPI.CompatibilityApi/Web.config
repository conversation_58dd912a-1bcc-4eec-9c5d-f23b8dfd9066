﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="hibernate-configuration" type="NHibernate.Cfg.ConfigurationSection<PERSON><PERSON><PERSON>, NHibernate" />
    <section name="bccMailList" type="Digiturk.Workflow.Common.WorkflowMailerSection, Digiturk.Workflow.Common" />
    <section name="debugMailList" type="Digiturk.Workflow.Common.WorkflowMailerSection, Digiturk.Workflow.Common" />
  </configSections>
  <!-- Other configurations -->
  <runtime>
    <gcServer enabled="true" />
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="3a9cab8f8d22bfb7" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.0.39271" newVersion="3.1.0.39271" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Oracle.DataAccess" culture="neutral" publicKeyToken="89b483f429c47342" />
        <bindingRedirect oldVersion="0.0.0.0-4.122.19.1" newVersion="4.122.19.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Web.Infrastructure" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="2.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.3.0.0" newVersion="5.3.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.5.0" newVersion="4.0.5.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.6" newVersion="9.0.0.6" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.5.0" newVersion="4.0.5.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.4.0" newVersion="4.2.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.6.0" newVersion="4.1.6.0" />
      </dependentAssembly>
    </assemblyBinding>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="NHibernate" publicKeyToken="aa95f207798dfdb4" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.1.1.4000" newVersion="2.1.1.4000" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.web>
    <compilation debug="true" />
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
      </providers>
    </membership>
    <authentication mode="Windows" />
    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
      </providers>
    </roleManager>
  </system.web>
  <system.webServer>
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
  </system.webServer>
  <bccMailList>
    <mailAddresses>
      <add address="<EMAIL>" displayName="İş Akışı Test1" />
      <add address="<EMAIL>" displayName="İş Akışı Test2" />
      <add address="<EMAIL>" displayName="İş Akışı Test3" />
      <add address="<EMAIL>" displayName="İş Akışı Test4" />
      <add address="<EMAIL>" displayName="İş Akışı Test5" />
      <add address="<EMAIL>" displayName="İş Akışı Test6" />
    </mailAddresses>
  </bccMailList>
  <debugMailList>
    <mailAddresses>
      <add address="<EMAIL>" displayName="İş Akışı Test1" />
      <add address="<EMAIL>" displayName="İş Akışı Test2" />
      <add address="<EMAIL>" displayName="İş Akışı Test3" />
      <add address="<EMAIL>" displayName="İş Akışı Test4" />
      <add address="<EMAIL>" displayName="İş Akışı Test5" />
      <add address="<EMAIL>" displayName="İş Akışı Test6" />
    </mailAddresses>
  </debugMailList>
  <hibernate-configuration xmlns="urn:nhibernate-configuration-2.2">
    <session-factory name="NHibernate.Test">
      <property name="format_sql">true</property>
      <mapping assembly="Digiturk.Workflow.Entities" />
      <mapping assembly="Digiturk.Common.Entities" />
      <event type="pre-update">
        <listener class="Digiturk.Workflow.Repository.AuditEventListener, Digiturk.Workflow.Repository" />
      </event>
      <event type="pre-insert">
        <listener class="Digiturk.Workflow.Repository.AuditEventListener, Digiturk.Workflow.Repository" />
      </event>
      <!--<event type="delete">
      <listener class="Digiturk.Workflow.Repository.SoftDeleteEventListener, Digiturk.Workflow.Repository"/>
    </event>-->
    </session-factory>
  </hibernate-configuration>
  <connectionStrings>
    <add name="connStrESSB_TEST" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16.digiturk.local)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=SUBSET15)));User Id=ESSB_USR;Password=******" providerName="System.Data.OracleClient" />
    <add name="connStrESSB_TEST_M8" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=MaintenanceTestDB.digiturk.local)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=CORP)));User Id=ESSB_USR;Password=******" providerName="System.Data.OracleClient" />
    <add name="connStrESSB_LIVE" connectionString="Data Source=(DESCRIPTION =(ADDRESS = (PROTOCOL = TCP)(HOST = DPSMS2)(PORT = 1522))(CONNECT_DATA = (SID = DBSLIVE)));User Id=ESSB_USR;Password=******" providerName="System.Data.OracleClient" />
    <add name="DBSLIVE" connectionString="Data Source=(DESCRIPTION =(ADDRESS = (PROTOCOL = TCP)(HOST = DPSMS2)(PORT = 1522))(CONNECT_DATA = (SID = DBSLIVE)));User Id={0};Password=***" providerName="System.Data.OracleClient" />
    <add name="ITTPTEST" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms9.digiturk.local)(PORT=1521))(CONNECT_DATA=(SID=ITTPTEST)));User Id={0};Password=***" providerName="System.Data.OracleClient" />
    <add name="DBSTEST" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=DBSTEST)));User Id={0};Password=***" providerName="System.Data.OracleClient" />
    <add name="DBSLIVE_M8" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dbslive-scan.digiturk.local)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=DBSLIVE)));User Id={0};Password=***" providerName="System.Data.OracleClient" />
    <add name="SUBSET15_M8" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=MaintenanceTestDB.digiturk.local)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=CORP)));User Id={0};Password=***" providerName="System.Data.OracleClient" />
    <add name="SUBSET15" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16.digiturk.local)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=SUBSET15)));User Id={0};Password=***" providerName="System.Data.OracleClient" />
    <add name="SAPERION" connectionString="Data Source=DTL1SAPERION01;Initial Catalog=SAPERION;Persist Security Info=True;User ID=********;Password=********;Pooling=False;" providerName="System.Data.SqlClient" />
    <add name="DefaultConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=DT_APPLICATION_USR;Password=DT_APPLICATION_USR;Pooling=true;Self Tuning=false" />
    <add name="ReportConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=***********;Password=***********;Pooling=true;Self Tuning=false" />
    <add name="***********" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=***********;Password=***********;Pooling=true" />
    <add name="FrameworkConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=FRAMEWORK;Password=FRAMEWORK;Pooling=true;Self Tuning=false" />
    <add name="NetsisConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1527))(CONNECT_DATA=(SID=FUSION)));User Id=NETSISORTAK;Password=****;Pooling=true;Statement Cache Size=10;Self Tuning=false" />
    <add name="DBSConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16.digiturk.local)(PORT=1521))(CONNECT_DATA=(SID=KURUMSAL.DIGITURK.LOCAL)));User Id=*******;Password=*******;Self Tuning=false" />
    <add name="DBSConnection_m8" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=MaintenanceTestDB.digiturk.local)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=CORP)));User Id=*******;Password=*******;Self Tuning=false" />
    <add name="RAPOR" connectionString="Data Source=(DESCRIPTION =(ADDRESS = (PROTOCOL = TCP)(HOST = DPSMS2)(PORT = 1522))(CONNECT_DATA = (SID = DBSLIVE)));User Id={0};Password=***" providerName="System.Data.OracleClient" />
  </connectionStrings>
  <appSettings>
    <add key="ReadConfigFilesFromXML" value="false" />
    <add key="Odeme_SatinAlmaBaskanLimit" value="500000" />
    <add key="RaporMaxTarihTanim" value="ay" />
    <add key="RaporMaxTarihDeger" value="6" />
    <add key="IsSIDControls" value="True" />
    <add key="debugMode" value="true" />
    <add key="PageTitle" value="Digiturk Is Akislari" />
    <add key="hibernate.use_reflection_optimizer" value="false" />
    <add key="ServiceName" value="ApplicationServer1" />
    <add key="SingleInstance" value="true" />
    <add key="AuthenticateUserOnAnyServiceCall" value="true" />
    <add key="DefinitionConfigration" value="C:\TFS\DigiFlowPM\Digiturk.Workflow.DigiFlow_v3\WFPages\Definiton.xml" />
    <add key="LogicalGroupDefinition" value="C:\TFS\DigiFlowPM\Digiturk.Workflow.DigiFlow_v3\WFPages\LogicalGroups.xml" />
    <add key="StateDefinition" value="C:\TFS\DigiFlowPM\Digiturk.Workflow.DigiFlow_v3\WFPages\StateDefinition.xml" />
    <add key="Workflow.Mail.IsMailDebugMode" value="True" />
    <add key="NotificationTemplateID" value="1000" />
    <add key="IsYYSActive" value="true" />
    <add key="EducationWebServiceUserName" value="WEBSERVICE" />
    <add key="EducationWebServicePassword" value="w17211s" />
    <add key="DomainName" value="http://localhost:4462/" />
    <add key="ExMonth" value="0" />
    <add key="F2FDeptId" value="464" />
    <add key="AssemblyLookupFolders" value="C:\\TFS\\DigiFlowPM\\Digiturk.Workflow.DigiFlow_v3\\bin" />
    <add key="DynamicInboxWorker" value="C:\\TFS\\DigiFlowPM\\Digiturk.Workflow.DigiFlow_v3\\Inbox" />
    <add key="DynamicHistoryWorker" value="C:\\TFS\\DigiFlowPM\\Digiturk.Workflow.DigiFlow_v3\\History" />
    <add key="DefaultEndPoint" value="NetTcpBinding_AppService" />
    <add key="Workflow.Mail.EmbeddedImagesPath" value="\\dtl1iis3\Deployment\MailImages" />
    <add key="Workflow.Mail.Params" value="ad9bBOUpHG1st9IlCOvZA9DCTJKj7XTlewXqZpa4xWo/m0f/ZXwzFpTy9cdYK53Hx2MQqWxlyxSVT5lg5waY6LC3p5i77oc4pHAEGgnKFbAuL48SNlMELo9dIiUOo2RmdTprZ/SAkyKF03+gmRGRexw3+qCFnr/iVOx/58S075o=" />
    <add key="Workflow.Mail.Server" value="************" />
    <add key="Workflow.Mail.FromAddress" value="<EMAIL>" />
    <add key="Workflow.Mail.LinkDomain" value="http://digiflowtest.digiturk.com.tr" />
    <add key="noreply_workflow.Mail.Server" value="************" />
    <add key="noreply_workflow.Mail.FromAddress" value="<EMAIL>" />
    <add key="noreply_workflow.Mail.FromAddressUserName" value="noreply_workflow" />
    <add key="noreply_workflow.Mail.FromAddressPsw" value="flow123456+" />
    <add key="TicariCCAddress" value="<EMAIL>" />
    <add key="UzaktanCalismaLimit" value="14" />
    <add key="UzaktanCalismaSirket" value="1" />
    <add key="UzaktanCalismaResmiTarih" value="19.07.2021" />
    <add key="yayinKurulumProcessType" value="57" />
    <add key="yayinProcessType" value="58" />
    <add key="yayinKurulumProcessTypeTest" value="52" />
    <add key="yayinProcessTypeTest" value="53" />
    <add key="UzaktanCalismaSirket" value="1" />
    <add key="KreaSakaryaSirket" value="1" />
    <add key="HrServicesEndPointDefaul" value="http://dtl1iis4:3331/PersonelBilgisi.asmx" />
    <add key="HrServicesEndPointMedia" value="http://dtl1iis4:3335/PersonelBilgisi.asmx" />
    <add key="AvansEndPointDefaul" value="http://dtl1sp1:20000/Service.asmx" />
    <add key="ApplicationName" value="" />
    <add key="Web.Services.IsProxyUsing" value="True" />
    <add key="Web.Services.IsCredentialUsing" value="True" />
    <add key="Web.Services.UserName" value="Digiflow_sa" />
    <add key="Web.Services.Password" value="Digif16up+-" />
    <add key="Web.Services.Domain" value="DIGITURK" />
    <add key="Web.Services.ProxyServicesIp" value="http://************:8080" />
    <add key="aspnet:MaxHttpCollectionKeys" value="100000" />
    <add key="ESSBDurum" value="E" />
    <add key="DBSTEST_*******" value="*******" />
    <add key="SUBSET15_*******" value="*******" />
    <add key="ITTPTEST_*******" value="*******" />
    <add key="DBSLIVE_*******" value="MARACANA" />
    <add key="SUBSET15_RAPOR" value="RAPOR" />
    <add key="SUBSET15_RAPOR_APPSTR" value="Rota" />
    <add key="DBSLIVE_*******_APPSTR" value="Digiflow" />
    <add key="DBSLIVE_*******_UNIQUE" value="4!fedL0w" />
    <add key="DBSTEST_*******_APPSTR" value="Digiflow" />
    <add key="DBSTEST_*******_UNIQUE" value="4!fedL0w" />
    <add key="ITTPTEST_*******_APPSTR" value="Digiflow" />
    <add key="ITTPTEST_*******_UNIQUE" value="4!fedL0w" />
    <add key="SUBSET15_*******_APPSTR" value="Digiflow" />
    <add key="SUBSET15_*******_UNIQUE" value="4!fedL0w" />
    <add key="SUBSET15_RAPOR_APPSTR" value="Rota" />
    <add key="SUBSET15_RAPOR_UNIQUE" value="8up!5f0rrt" />
    <add key="DBSLIVE_RAPOR_APPSTR" value="Rota" />
    <add key="DBSLIVE_RAPOR_UNIQUE" value="8up!5f0rrt" />
    <add key="DBS_REQUEST_ROW_SIZE" value="100" />
    <add key="AydinlatmaMetniServisi" value="http://sdp-lcl-tst.digiturk.net/Maintenance_Test/virtual/basic/PersonalDataProtectionBS.svc?wsdl" />
    <add key="SosyalMedyaServisi2" value="http://test-sdp-lcl.digiturk.net/virtual/basic/PersonalDataProtectionBS.svc?wsdl" />
    <add key="SosyalMedyaServisi" value="http://sdp-lcl.digiturk.net/virtual/basic/PersonalDataProtectionBS.svc?wsdl" />
    <add key="OrganisationalSaleRecordBSUrl" value="http://test-sdp-lcl.digiturk.net/virtual/basic/OrganisationalSaleRecordBS.svc" />
    <add key="OrganisationalSaleRecordBSUrlUsername" value="SYSIQ" />
    <add key="OrganisationalSaleRecordBSUrlPassword" value="SYSIQ111" />
    <add key="OrganisationalSaleRecordBSUrlCompany" value="DIGITURK" />
    <add key="OrganisationalSaleRecordBSUrlApplication" value="DIGIPORT" />
    <add key="OrganisationalSaleRecordBSUrlChannelName" value="DEFAULT" />
    <add key="TicariSatisliYayinAcmaToAddress" value="<EMAIL>" />
    <add key="TicariSatisliYayinAcmaccAddress" value="<EMAIL>" />
    <add key="UzaktanCalismaMuvafakatnameTarih" value="30.09.2021" />
    <add key="UzaktanCalismaKontrolCalissin" value="H" />
    <add key="SosyalMedyaDocumentId" value="2141928" />
    <add key="IsAvansKontrolCalissin" value="E" />
    <add key="PerformansAdminPath" value="http://pysadmin/" />
    <add key="UzaktanCalismaEndDate" value="03.10.2022" />
    <add key="AdminGrupUser" value="DTKBAYRAKTAR,DTZKUCUK,DIGIFLOW_SA,SPSMOSS_SA,DTBGUNAY,DTMKASAPOGLU,DTUMKORKMAZ,DTYAELMAS" />
    <add key="imgEmptyFile" value="http://dtl4sptst1:889/users/updateYourImage.png" />
    <add key="imgFolder" value="http://digiflowtest:889/users/" />
    <add key="imgTest" value="C:\Users\<USER>\Desktop\SelfServiceSLN\SelfServiceHR\images\users\" />
    <add key="imgFolderUpload" value="\\dtl4sptst1\users\" />
    <add key="ClientSettingsProvider.ServiceUri" value="" />
  </appSettings>
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701;612;618" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008,40000,40008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
    </compilers>
  </system.codedom>
</configuration>