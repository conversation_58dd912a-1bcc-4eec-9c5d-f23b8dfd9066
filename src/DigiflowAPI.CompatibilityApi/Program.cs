﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http.SelfHost;

namespace DigiflowAPI.CompatibilityApi
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var config = new HttpSelfHostConfiguration("http://localhost:5000");
            config.MessageHandlers.Add(new LoggingHandler());

            WebApiConfig.Register(config);

            using (var server = new HttpSelfHostServer(config))
            {
                server.OpenAsync().Wait();
                Console.WriteLine("Server is running on http://localhost:5000");
                Console.WriteLine("Press any key to exit.");

                // This will allow us to see DLL loading messages
                AppDomain.CurrentDomain.AssemblyLoad += (sender, eventArgs) =>
                {
                    Console.WriteLine($"Loaded: {eventArgs.LoadedAssembly.FullName}");
                };

                Console.ReadKey();
            }
        }
    }
}
