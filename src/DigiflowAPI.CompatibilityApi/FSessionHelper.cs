﻿using NHibernate;
using Oracle.DataAccess.Client;
using System;
using System.Configuration;

namespace CompatibiliyAPI
{
    public static class FSessionHelper
    {
        private static ISessionFactory s_Factory;
        private static object s_SessionLock = new object();

        /// <summary>
        /// Entity Kaydetmek için Nhibernate Session Bilgisini döndürür
        /// </summary>
        /// <returns></returns>
        public static ISession GetDTWorkflowSession()
        {
            //var cfg = new NHibernate.Cfg.Configuration();
            //cfg.SetProperty("dialect", "NHibernate.Dialect.Oracle10gDialect");
            //cfg.SetProperty("connection.driver_class", "NHibernate.Driver.OracleDataClientDriver");
            ////cfg.SetProperty("connection.connection_string", "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SID=FLOWDEV)));User Id=***********;Password=***********");
            //cfg.SetProperty("connection.connection_string", ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
            //cfg.SetProperty("show_sql", "true");
            //cfg.SetProperty("query.substitutions", "true 1, false 0, yes 'Y', no 'N'");
            //cfg.SetProperty("proxyfactory.factory_class", "NHibernate.ByteCode.LinFu.ProxyFactoryFactory, NHibernate.ByteCode.LinFu");
            //cfg.AddAssembly("Digiturk.Workflow.Digiflow.Entities");
            //ISessionFactory factory = cfg.BuildSessionFactory();
            //ISession session = factory.OpenSession();

            ISessionFactory factory = GetSessionFactory();
            ISession session = factory.OpenSession();
            return session;
        }

        public static ISessionFactory GetSessionFactory()
        {
            if (ReferenceEquals(s_Factory, null))
            {
                lock (s_SessionLock)
                {
                    if (ReferenceEquals(s_Factory, null))
                    {
                        try
                        {
                            var cfg = new NHibernate.Cfg.Configuration();

                            // Log configuration settings
                            LogConfigurationSettings(cfg);

                            cfg.SetProperty("dialect", "NHibernate.Dialect.Oracle10gDialect");
                            cfg.SetProperty("connection.driver_class", "NHibernate.Driver.OracleDataClientDriver");
                            cfg.SetProperty("connection.connection_string", ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
                            cfg.SetProperty("show_sql", "true");
                            cfg.SetProperty("query.substitutions", "true 1, false 0, yes 'Y', no 'N'");
                            cfg.SetProperty("proxyfactory.factory_class", "NHibernate.ByteCode.LinFu.ProxyFactoryFactory, NHibernate.ByteCode.LinFu");

                            // Log before adding assembly
                            Console.WriteLine("Adding assembly: Digiturk.Workflow.Digiflow.Entities");
                            cfg.AddAssembly("Digiturk.Workflow.Entities");
                            cfg.AddAssembly("Digiturk.Common.Entities");
                            cfg.AddAssembly("Digiturk.Workflow.Digiflow.Entities");

                            string connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString;

                            TestOracleConnection(connectionString);

                            // Log before building SessionFactory
                            Console.WriteLine("Building SessionFactory...");
                            s_Factory = cfg.BuildSessionFactory();
                            Console.WriteLine("SessionFactory built successfully.");
                        }
                        catch (Exception ex)
                        {
                            // Log the full exception details
                            LogException("Error creating SessionFactory", ex);
                            throw; // Re-throw the exception after logging
                        }
                    }
                }
            }
            return s_Factory;
        }
        private static void TestOracleConnection(string connectionString)
        {
            using (var connection = new OracleConnection(connectionString))
            {
                try
                {
                    Console.WriteLine("Opening Oracle connection for testing...");
                    connection.Open();
                    Console.WriteLine("Oracle connection test successful.");
                }
                catch (OracleException oex)
                {
                    throw new Exception("Failed to open Oracle connection for testing.", oex);
                }
            }
        }


        private static void LogConfigurationSettings(NHibernate.Cfg.Configuration cfg)
        {
            Console.WriteLine("NHibernate Configuration Settings:");
            Console.WriteLine($"Dialect: {cfg.GetProperty("dialect")}");
            Console.WriteLine($"Driver Class: {cfg.GetProperty("connection.driver_class")}");
            Console.WriteLine($"Connection String: {cfg.GetProperty("connection.connection_string")}");
            Console.WriteLine($"Show SQL: {cfg.GetProperty("show_sql")}");
            Console.WriteLine($"Query Substitutions: {cfg.GetProperty("query.substitutions")}");
            Console.WriteLine($"Proxy Factory: {cfg.GetProperty("proxyfactory.factory_class")}");
        }

        private static void LogException(string message, Exception ex)
        {
            Console.WriteLine($"ERROR: {message}");
            Console.WriteLine($"Exception Type: {ex.GetType().FullName}");
            Console.WriteLine($"Exception Message: {ex.Message}");
            Console.WriteLine($"Stack Trace: {ex.StackTrace}");

            if (ex.InnerException != null)
            {
                Console.WriteLine("Inner Exception:");
                LogException("Inner Exception Details", ex.InnerException);
            }
        }

    }
}