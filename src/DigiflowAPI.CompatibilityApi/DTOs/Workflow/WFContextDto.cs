﻿using Digiturk.Workflow.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DigiflowAPI.CompatibilityApi.DTOs.Workflow
{
    public class WFContextDto
    {
        public long WfWorkflowInstanceId { get; set; }
        public DateTime? EndTime { get; set; }
        public long? Priority { get; set; }
        public long? WfCurrentStateId { get; set; }
        public long? EntityRefId { get; set; }
        public string Description { get; set; }
        public long? WfWorkflowDefId { get; set; }
        public long? OwnerLoginId { get; set; }
        public int? ParentActionId { get; set; }
        public DateTime? StartTime { get; set; }
        public string WfWorkflowStatusTypeCd { get; set; }
        public bool? IsSubflow { get; set; }
        public bool? UseInstanceAssignment { get; set; }
        public Dictionary<string, object> Parameters { get; set; }
        public IList<FWfInstanceParameter> WfWorkflowInstanceFWfInstanceParameterList { get; set; } = new List<FWfInstanceParameter>();
        public IList<FWfStateInstance> WfWorkflowInstanceFWfStateInstanceList { get; set; } = new List<FWfStateInstance>();
    }
}