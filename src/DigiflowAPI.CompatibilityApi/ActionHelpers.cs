﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.DataAccessLayer;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.Framework;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Engine;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using FSessionHelper = CompatibiliyAPI.FSessionHelper;
using Digiturk.Workflow.Digiflow.Entities.Enums;
namespace CompatibiliyAPI
{
    /// <summary>
    /// Akışlarla ilgili actionların kullanıldığı class dır.
    /// </summary>
    public class ActionHelpers
    {
        public static void ClearContext(WFContext wfContext)
        {
            //var cList = wfContext.Parameters.ToList();
            //for (int i = 0; i < wfContext.Parameters.Count; i++)
            //{
            //    wfContext.Parameters.AddOrChangeItem(cList[i].Key, "");
            //}
            //wfContext.Save();
        }

        /// <summary>
        /// Entity Object üzerindeki RequestId yi geri get edicez.
        /// </summary>
        /// <param name="EntityObject"></param>
        /// <returns></returns>
        public static long GetRequestId(object EntityObject)
        {
            if (EntityObject is GSMRequest)
                return ((GSMRequest)EntityObject).RequestID;
            if (EntityObject is EmployeeRequest)
                return ((EmployeeRequest)EntityObject).RequestId;
            if (EntityObject is DelegationRequest)
                return ((DelegationRequest)EntityObject).RequestId;
            if (EntityObject is RotaRequest)
                return ((RotaRequest)EntityObject).RequestId;
            if (EntityObject is MacroRequest)
                return ((MacroRequest)EntityObject).RequestId;
            else
                return 0;
        }

        /// <summary>
        /// Sadece bir Entity save etmek için kullanılır.
        /// </summary>
        /// <param name="Entity"></param>
        public static void EntitySave(IEntity Entity)
        {
            using (ISession session = FSessionHelper.GetDTWorkflowSession())
            {
                session.SaveOrUpdate(Entity);
                session.Flush();
                session.Close();
                session.Dispose();
                //session = null;
            }
        }

        /// <summary>
        /// Entity Delete Etmek için kullanılır.
        /// </summary>
        /// <param name="Entity"></param>
        public static void EntityDelete(IEntity Entity)
        {
            using (ISession session = FSessionHelper.GetDTWorkflowSession())
            {
                session.Delete(Entity);
                session.Flush();
                session.Close();
                session.Dispose();
            }
        }

        /// <summary>
        /// Sadece bir Entity Update etmek için kullanılır.
        /// </summary>
        /// <param name="Entity"></param>
        public static void EntityUpdate(IEntity Entity)
        {
            using (ISession session = FSessionHelper.GetDTWorkflowSession())
            {
                session.Update(Entity);
                session.Flush();
                session.Close();
                session.Dispose();
            }
        }

        /// <summary>
        /// Master ve Detail bir Entity Save Etmek için kullanılır
        /// </summary>
        /// <param name="Entity"></param>
        /// <param name="Detail"></param>
        public static void EntitySave(IEntity Entity, List<IDetailEntity> Detail)
        {
            using (ISession session = FSessionHelper.GetDTWorkflowSession())
            {
                session.SaveOrUpdate(Entity);
                IList<IDetailEntity> DetailEntity;
                DetailEntity = ((IList<IDetailEntity>)Detail);
                for (int i = 0; i < DetailEntity.Count; i++)
                {
                    if (Entity is IEntity && DetailEntity[i] is IDetailEntity)
                    {
                        ((IDetailEntity)DetailEntity[i]).RelatedRequestID = ((IEntity)Entity).RequestId;
                    }
                    session.SaveOrUpdate(DetailEntity[i]);
                }
                session.Close();
                session.Dispose();
            }
        }

        /// <summary>
        /// Master ve Detail bir Entity Save Etmek için kullanılır - fazladan detay entity için -satınalmada örneğin
        /// </summary>
        /// <param name="Entity"></param>
        /// <param name="Detail"></param>
        public static void EntitySave(IEntity Entity, List<IDetailEntity> Detail, List<IDetailEntity> Detail2, List<IDetailEntity> Detail3)
        {
            using (ISession session = FSessionHelper.GetDTWorkflowSession())
            {
                session.SaveOrUpdate(Entity);
                IList<IDetailEntity> DetailEntity;
                DetailEntity = ((IList<IDetailEntity>)Detail);
                for (int i = 0; i < DetailEntity.Count; i++)
                {
                    if (Entity is IEntity && DetailEntity[i] is IDetailEntity)
                    {
                        ((IDetailEntity)DetailEntity[i]).RelatedRequestID = ((IEntity)Entity).RequestId;
                    }
                    session.SaveOrUpdate(DetailEntity[i]);
                }

                IList<IDetailEntity> DetailEntity2;
                DetailEntity2 = ((IList<IDetailEntity>)Detail2);
                for (int i = 0; i < DetailEntity2.Count; i++)
                {
                    if (Entity is IEntity && DetailEntity2[i] is IDetailEntity)
                    {
                        ((IDetailEntity)DetailEntity2[i]).RelatedRequestID = ((IEntity)Entity).RequestId;
                    }
                    session.SaveOrUpdate(DetailEntity2[i]);
                }

                IList<IDetailEntity> DetailEntity3;
                DetailEntity3 = ((IList<IDetailEntity>)Detail3);
                for (int i = 0; i < DetailEntity3.Count; i++)
                {
                    if (Entity is IEntity && DetailEntity3[i] is IDetailEntity)
                    {
                        ((IDetailEntity)DetailEntity3[i]).RelatedRequestID = ((IEntity)Entity).RequestId;
                    }
                    session.SaveOrUpdate(DetailEntity3[i]);
                }

                session.Close();
                session.Dispose();
            }
        }

        /// <summary>
        /// Bir detay Entitsinin kaydedilmesi işlemini yapar
        /// </summary>
        /// <param name="EntityList"></param>
        public static void EntitySave(List<IEntity> EntityList)
        {
            using (ISession session = FSessionHelper.GetDTWorkflowSession())
            {
                for (int i = 0; i < EntityList.Count; i++)
                {
                    session.SaveOrUpdate(EntityList[i]);
                }
                session.Close();
                session.Dispose();
            }
        }

        /// <summary>
        /// Bir detay Entitsinin kaydedilmesi işlemini yapar - z - detay olarak not edilmiş metod IEntity tipinde IDetailEntity yeni bir metod oluşturuldu - 31.10.2014
        /// </summary>
        /// <param name="EntityList"></param>
        public static void EntitySave(List<IDetailEntity> EntityList, long RequestId)
        {
            using (ISession session = FSessionHelper.GetDTWorkflowSession())
            {
                IList<IDetailEntity> DetailEntity;
                DetailEntity = ((IList<IDetailEntity>)EntityList);
                for (int i = 0; i < DetailEntity.Count; i++)
                {
                    ((IDetailEntity)DetailEntity[i]).RelatedRequestID = RequestId;
                    session.SaveOrUpdate(DetailEntity[i]);
                }
                session.Close();
                session.Dispose();
            }
        }

        /// <summary>
        /// Bir detay Entitysinin kaydedilmesi işlemini yapar
        /// </summary>
        /// <param name="EntityList"></param>
        public static void EntitySave(IDetailEntity Entity, long RequestId)
        {
            using (ISession session = FSessionHelper.GetDTWorkflowSession())
            {
                IDetailEntity DetailEntity = Entity;
                DetailEntity.RelatedRequestID = RequestId;
                session.SaveOrUpdate(DetailEntity);
                session.Close();
                session.Dispose();
            }
        }

        /// <summary>
        /// Bir detay Entitsinin kaydedilmesi işlemini yapar - z - detay olarak not edilmiş metod IEntity tipinde IDetailEntity yeni bir metod oluşturuldu - 31.10.2014
        /// </summary>
        /// <param name="EntityList"></param>
        public static void EntityUpdate(List<IDetailEntity> EntityList, long RequestId)
        {
            using (ISession session = FSessionHelper.GetDTWorkflowSession())
            {
                IList<IDetailEntity> DetailEntity;
                DetailEntity = ((IList<IDetailEntity>)EntityList);
                for (int i = 0; i < DetailEntity.Count; i++)
                {
                    ((IDetailEntity)DetailEntity[i]).RelatedRequestID = RequestId;

                    if (session.Contains(DetailEntity[i]) == true)
                    {
                        session.Update(DetailEntity[i]);
                    }
                    else
                    {
                        session.SaveOrUpdate(DetailEntity[i]);
                    }
                }
                session.Close();
                session.Dispose();
            }
        }

        /// <summary>
        ///  Bu Method ile Yeni bir Akış Oluşturulur(1 detay tablolu)
        /// </summary>
        /// <param name="Entity"> Akışı Başlatılacak Entity</param>
        /// <param name="WorkFlowDefinition"> Akışın DefinitionID si </param>
        /// <param name="LoginId"> Akışı Başlatan Kullanıcının LoginId si</param>
        public static void CreateWorkFlow(IEntity Entity, List<IDetailEntity> Detail, long WorkFlowDefinitionId, long LoginId)
        {
            using (ISession session = FSessionHelper.GetDTWorkflowSession())
            {
                session.SaveOrUpdate(Entity);
                IList<IDetailEntity> DetailEntity;
                DetailEntity = ((IList<IDetailEntity>)Detail);
                for (int i = 0; i < DetailEntity.Count; i++)
                {
                    if (Entity is IEntity && DetailEntity[i] is IDetailEntity)
                    {
                        ((IDetailEntity)DetailEntity[i]).RelatedRequestID = ((IEntity)Entity).RequestId;
                    }
                    session.SaveOrUpdate(DetailEntity[i]);
                }
                session.Close();
                session.Dispose();
                long ret = WorkflowInstanceWorker.Start(WorkFlowDefinitionId, Entity.RequestId, 1, null, LoginId);
                WorkFlowInformationHelper.GrantViewPermissionToUser(ret, LoginId);
                DetailEntity = null;
                Entity = null;
            }
        }

        /// <summary>
        ///  Bu Method ile Yeni bir Akış Oluşturulur(3 detay tablolu)
        /// </summary>
        /// <param name="Entity"> Akışı Başlatılacak Entity</param>
        /// <param name="WorkFlowDefinition"> Akışın DefinitionID si </param>
        /// <param name="LoginId"> Akışı Başlatan Kullanıcının LoginId si</param>
        public static void CreateWorkFlow(IEntity Entity, List<IDetailEntity> Detail, List<IDetailEntity> Detail2, List<IDetailEntity> Detail3, long WorkFlowDefinitionId, long LoginId)
        {
            using (ISession session = FSessionHelper.GetDTWorkflowSession())
            {
                session.SaveOrUpdate(Entity);
                IList<IDetailEntity> DetailEntity;
                DetailEntity = ((IList<IDetailEntity>)Detail);
                for (int i = 0; i < DetailEntity.Count; i++)
                {
                    if (Entity is IEntity && DetailEntity[i] is IDetailEntity)
                    {
                        ((IDetailEntity)DetailEntity[i]).RelatedRequestID = ((IEntity)Entity).RequestId;
                    }
                    session.SaveOrUpdate(DetailEntity[i]);
                }

                IList<IDetailEntity> DetailEntity2;
                DetailEntity2 = ((IList<IDetailEntity>)Detail2);
                for (int i = 0; i < DetailEntity2.Count; i++)
                {
                    if (Entity is IEntity && DetailEntity2[i] is IDetailEntity)
                    {
                        ((IDetailEntity)DetailEntity2[i]).RelatedRequestID = ((IEntity)Entity).RequestId;
                    }
                    session.SaveOrUpdate(DetailEntity2[i]);
                }

                IList<IDetailEntity> DetailEntity3;
                DetailEntity3 = ((IList<IDetailEntity>)Detail3);
                for (int i = 0; i < DetailEntity3.Count; i++)
                {
                    if (Entity is IEntity && DetailEntity3[i] is IDetailEntity)
                    {
                        ((IDetailEntity)DetailEntity3[i]).RelatedRequestID = ((IEntity)Entity).RequestId;
                    }
                    session.SaveOrUpdate(DetailEntity3[i]);
                }

                session.Close();
                session.Dispose();
                long ret = WorkflowInstanceWorker.Start(WorkFlowDefinitionId, Entity.RequestId, 1, null, LoginId);
                WorkFlowInformationHelper.GrantViewPermissionToUser(ret, LoginId);
                DetailEntity = null;
                DetailEntity2 = null;
                DetailEntity3 = null;
                Entity = null;
            }
        }

        /// <summary>
        ///  Bu Method ile Yeni bir Akış Oluşturulur(2 detay tablolu)
        /// </summary>
        /// <param name="Entity"> Akışı Başlatılacak Entity</param>
        /// <param name="WorkFlowDefinition"> Akışın DefinitionID si </param>
        /// <param name="LoginId"> Akışı Başlatan Kullanıcının LoginId si</param>
        public static void CreateWorkFlow(IEntity Entity, List<IDetailEntity> Detail, List<IDetailEntity> Detail2, long WorkFlowDefinitionId, long LoginId)
        {
            using (ISession session = FSessionHelper.GetDTWorkflowSession())
            {
                session.SaveOrUpdate(Entity);
                IList<IDetailEntity> DetailEntity;
                DetailEntity = ((IList<IDetailEntity>)Detail);
                for (int i = 0; i < DetailEntity.Count; i++)
                {
                    if (Entity is IEntity && DetailEntity[i] is IDetailEntity)
                    {
                        ((IDetailEntity)DetailEntity[i]).RelatedRequestID = ((IEntity)Entity).RequestId;
                    }
                    session.SaveOrUpdate(DetailEntity[i]);
                }

                IList<IDetailEntity> DetailEntity2;
                DetailEntity2 = ((IList<IDetailEntity>)Detail2);
                for (int i = 0; i < DetailEntity2.Count; i++)
                {
                    if (Entity is IEntity && DetailEntity2[i] is IDetailEntity)
                    {
                        ((IDetailEntity)DetailEntity2[i]).RelatedRequestID = ((IEntity)Entity).RequestId;
                    }
                    session.SaveOrUpdate(DetailEntity2[i]);
                }

                session.Close();
                session.Dispose();
                long ret = WorkflowInstanceWorker.Start(WorkFlowDefinitionId, Entity.RequestId, 1, null, LoginId);
                WorkFlowInformationHelper.GrantViewPermissionToUser(ret, LoginId);
                DetailEntity = null;
                DetailEntity2 = null;
                Entity = null;
            }
        }


        /// <summary>
        ///  Bu Method ile Yeni bir Akış Oluşturulur(4 detay tablolu)
        /// </summary>
        /// <param name="Entity"> Akışı Başlatılacak Entity</param>
        /// <param name="WorkFlowDefinition"> Akışın DefinitionID si </param>
        /// <param name="LoginId"> Akışı Başlatan Kullanıcının LoginId si</param>
        public static void CreateWorkFlow(IEntity Entity, List<IDetailEntity> Detail, List<IDetailEntity> Detail2, List<IDetailEntity> Detail3, List<IDetailEntity> Detail4, long WorkFlowDefinitionId, long LoginId)
        {
            using (ISession session = FSessionHelper.GetDTWorkflowSession())
            {
                session.SaveOrUpdate(Entity);
                IList<IDetailEntity> DetailEntity;
                DetailEntity = ((IList<IDetailEntity>)Detail);
                for (int i = 0; i < DetailEntity.Count; i++)
                {
                    if (Entity is IEntity && DetailEntity[i] is IDetailEntity)
                    {
                        ((IDetailEntity)DetailEntity[i]).RelatedRequestID = ((IEntity)Entity).RequestId;
                    }
                    session.SaveOrUpdate(DetailEntity[i]);
                }

                IList<IDetailEntity> DetailEntity2;
                DetailEntity2 = ((IList<IDetailEntity>)Detail2);
                for (int i = 0; i < DetailEntity2.Count; i++)
                {
                    if (Entity is IEntity && DetailEntity2[i] is IDetailEntity)
                    {
                        ((IDetailEntity)DetailEntity2[i]).RelatedRequestID = ((IEntity)Entity).RequestId;
                    }
                    session.SaveOrUpdate(DetailEntity2[i]);
                }

                IList<IDetailEntity> DetailEntity3;
                DetailEntity3 = ((IList<IDetailEntity>)Detail3);
                for (int i = 0; i < DetailEntity3.Count; i++)
                {
                    if (Entity is IEntity && DetailEntity3[i] is IDetailEntity)
                    {
                        ((IDetailEntity)DetailEntity3[i]).RelatedRequestID = ((IEntity)Entity).RequestId;
                    }
                    session.SaveOrUpdate(DetailEntity3[i]);
                }

                IList<IDetailEntity> DetailEntity4;
                DetailEntity4 = ((IList<IDetailEntity>)Detail4);
                for (int i = 0; i < DetailEntity4.Count; i++)
                {
                    if (Entity is IEntity && DetailEntity4[i] is IDetailEntity)
                    {
                        ((IDetailEntity)DetailEntity4[i]).RelatedRequestID = ((IEntity)Entity).RequestId;
                    }
                    session.SaveOrUpdate(DetailEntity4[i]);
                }

                session.Close();
                session.Dispose();
                long ret = WorkflowInstanceWorker.Start(WorkFlowDefinitionId, Entity.RequestId, 1, null, LoginId);
                WorkFlowInformationHelper.GrantViewPermissionToUser(ret, LoginId);
                DetailEntity = null;
                DetailEntity2 = null;
                DetailEntity3 = null;
                Entity = null;
            }
        }


        /// <summary>
        ///  Bu Method ile Yeni bir Akış Oluşturulur
        /// </summary>
        /// <param name="Entity"> Akışı Başlatılacak Entity</param>
        /// <param name="WorkFlowDefinition"> Akışın DefinitionID si </param>
        /// <param name="LoginId"> Akışı Başlatan Kullanıcının LoginId si</param>
        public static void CreateWorkFlow(object Entity, long WorkFlowDefinitionId, long LoginId)
        {
            // Entity Kaydet
            using (ISession session = FSessionHelper.GetDTWorkflowSession())
            {
                session.SaveOrUpdate(Entity);
                session.Close();
                session.Dispose();
                // iş akışını başlat
                long ret = WorkflowInstanceWorker.Start(WorkFlowDefinitionId, ActionHelpers.GetRequestId(Entity), 1, null, LoginId);
                // başlatan kullanıcıya görüntüleme yetkisi ver
                WorkFlowInformationHelper.GrantViewPermissionToUser(ret, LoginId);
            }
        }

        /// <summary>
        ///  Bu Method ile Yeni bir Akış Oluşturulur
        /// </summary>
        /// <param name="Entity"> Akışı Başlatılacak Entity</param>
        /// <param name="WorkFlowDefinition"> Akışın DefinitionID si </param>
        /// <param name="LoginId"> Akışı Başlatan Kullanıcının LoginId si</param>
        public static void CreateWorkFlow(IEntity Entity, long WorkFlowDefinitionId, long LoginId)
        {
            // Entity Kaydet
            using (ISession session = FSessionHelper.GetDTWorkflowSession())
            {
                session.SaveOrUpdate(Entity);
                session.Close();
                session.Dispose();
                // iş akışını başlat
                long ret = WorkflowInstanceWorker.Start(WorkFlowDefinitionId, Entity.RequestId, 1, null, LoginId);
                // başlatan kullanıcıya görüntüleme yetkisi ver
                WorkFlowInformationHelper.GrantViewPermissionToUser(ret, LoginId);
            }
        }

        public static long CreateWorkFlowForServices(IEntity Entity, long WorkFlowDefinitionId, long LoginId)
        {
            // Entity Kaydet
            using (ISession session = FSessionHelper.GetDTWorkflowSession())
            {
                session.SaveOrUpdate(Entity);
                session.Close();
                session.Dispose();
                // iş akışını başlat
                long ret = WorkflowInstanceWorker.Start(WorkFlowDefinitionId, Entity.RequestId, 1, null, LoginId);
                // başlatan kullanıcıya görüntüleme yetkisi ver
                WorkFlowInformationHelper.GrantViewPermissionToUser(ret, LoginId);

                return ret;
            }
        }
        /// <summary>
        ///  Bu Method ile Yeni bir Akış Oluşturulur- Netsis yaşlandırma detay tablosu için
        /// </summary>
        /// <param name="Entity"> Akışı Başlatılacak Entity</param>
        /// <param name="WorkFlowDefinition"> Akışın DefinitionID si </param>
        /// <param name="LoginId"> Akışı Başlatan Kullanıcının LoginId si</param>
        public static void CreateWorkFlow(IEntity Entity, long WorkFlowDefinitionId, long LoginId, string grupKod, string raporNo)
        {
            using (ISession session = FSessionHelper.GetDTWorkflowSession())
            {
                session.SaveOrUpdate(Entity);
                session.Close();
                session.Dispose();

                //netsis yaşlandırma detay entity kaydedildi
                Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.NetsisReportInformationHelper.setNetsisYaslandirmaDetailEntity(Entity.RequestId.ToString(), grupKod, raporNo, LoginId.ToString());

                long ret = WorkflowInstanceWorker.Start(WorkFlowDefinitionId, Entity.RequestId, 1, null, LoginId);
                WorkFlowInformationHelper.GrantViewPermissionToUser(ret, LoginId);

                Entity = null;
            }
        }

        /// <summary>
        ///  Bu Method ile Form bulunduğu adımda onaylanır.
        /// </summary>
        /// <param name="InstanceId"></param>
        /// <param name="fWfActionTaskInstance"></param>
        /// <param name="LoginUser"></param>
        /// <param name="Context"></param>
        /// <param name="assignedUser"></param>
        public static void ApprovalWorkFlow(long InstanceId, FWfActionTaskInstance fWfActionTaskInstance, FLogin LoginUser, WFContext Context, FLogin assignedUser)
        {
            using (UnitOfWork.Start(true))
            {
                ActionTaskWorker.TakeOn(fWfActionTaskInstance.WfActionInstanceId, LoginUser.LoginId);
                long WorkflowInstanceId = fWfActionTaskInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId;
                long AssignToLoginId = CheckingWorker.GetAssignToLoginId(WorkflowInstanceId);
                if (AssignToLoginId == 0)
                {
                    AssignToLoginId = fWfActionTaskInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId;
                }
                long NewAssignLoginId = CheckingWorker.GetManager(AssignToLoginId);
                Context.Parameters.AddOrChangeItem("NewAssignLoginId", NewAssignLoginId);
                if (!Context.Parameters.ContainsKey("WfInstanceId")) Context.Parameters.AddOrChangeItem("WfInstanceId", InstanceId);
                if (!Context.Parameters.ContainsKey("Onay")) Context.Parameters.AddOrChangeItem("Onay", "Yes");
                if (!Context.Parameters.ContainsKey("LastUpdatedBy")) Context.Parameters.AddOrChangeItem("LastUpdatedBy", FormInformationHelper.LastUpdatedBy(LoginUser, assignedUser));
                if (!Context.Parameters.ContainsKey("AssignmentLoginType")) Context.Parameters.AddOrChangeItem("AssignmentLoginType", "LOGIN");
                if (!Context.Parameters.ContainsKey("AssignmentType")) Context.Parameters.AddOrChangeItem("AssignmentType", "TASKINBOX");
                ActionTaskWorker.Send(fWfActionTaskInstance.WfActionInstanceId, LoginUser.LoginId);
                SetWFView(InstanceId, LoginUser.LoginId);
                UnitOfWork.Commit();

                //Onay anındaki yorumu gönder - toplu onayda bu çalıştırılıyor
                long YorumGrup = ConvertionHelper.ConvertValue<int>(LogicalGroupHelper.LogicalGroupIDBul("Satinalma_YorumGrup"));
                DataTable DtYorum = LogicalGroupHelper.GetLoginPersonelList(YorumGrup);
                Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmailAllAcceptLoginList(LoginUser.LoginId, WorkflowInstanceId, DtYorum, 999, Context);
                DtYorum = null;

            }

            //Context.Save();
            //WorkflowHistoryWorker.Execute(fWfActionTaskInstance, LoginUser.LoginId, assignedUser.LoginId, WorkflowHistoryActionType.ACCEPTED,"" );
        }

        /// <summary>
        /// Bu Method ile Form bulunduğu adımda Onay Tipi Belirlenerek Onaylanır.
        /// </summary>
        /// <param name="ActionType"> Onay Tipi</param>
        /// <param name="InstanceId"> Formun Action InstanceId si</param>
        /// <param name="fWfActionTaskInstance"> Action Task Instance Id si</param>
        /// <param name="LoginUser">İşlemi yapan kullanıcının ID sş</param>
        /// <param name="Context">işlem  yapılırken kullanılacak WfContext parametre Listesi </param>
        /// <param name="assignedUser"> İşlem Yapılırken kullanılacak kullanıcının LoginId si</param>
        /// <param name="IsWfContextSave">Context in Save edilip Edilmemesi </param>
        /// <param name="Comment"> Açıklama alanı </param>
        public static void ApprovalWorkFlow(WorkflowHistoryActionType ActionType, long InstanceId, FWfActionTaskInstance fWfActionTaskInstance, FLogin LoginUser, WFContext Context, FLogin assignedUser, bool IsWfContextSave, string Comment)
        {
            ////WorkFlowTraceWorker.OracleLog(LoginUser.DomainUserName, "WorkFlowHelper", "BeforeSelfApproveFunction");
            using (UnitOfWork.Start(true))
            {
                ActionTaskWorker.TakeOn(fWfActionTaskInstance.WfActionInstanceId, LoginUser.LoginId);
                ////WorkFlowTraceWorker.OracleLog(LoginUser.DomainUserName, "WorkFlowHelper", "TakeOn");
                long WorkflowInstanceId = fWfActionTaskInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId;
                long AssignToLoginId = CheckingWorker.GetAssignToLoginId(WorkflowInstanceId);
                ////WorkFlowTraceWorker.OracleLog(LoginUser.DomainUserName, "WorkFlowHelper", "GetAssignToLoginID");
                if (AssignToLoginId == 0)
                {
                    AssignToLoginId = fWfActionTaskInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId;
                }

                #region Action History Type Kararı

                if (ActionType == WorkflowHistoryActionType.ACCEPTED)
                {
                    Context.Parameters.AddOrChangeItem("ActionType", "Onaylanmıştır.");
                }
                else if (ActionType == WorkflowHistoryActionType.CORRECTION)
                {
                    Context.Parameters.AddOrChangeItem("ActionType", "düzeltme olarak onaylanmıştır.");
                }
                else if (ActionType == WorkflowHistoryActionType.CNDACCCEPT)
                {
                    Context.Parameters.AddOrChangeItem("ActionType", "şartlı olarak onaylanmıştır.");
                }
                else
                {
                    Context.Parameters.AddOrChangeItem("ActionType", "Onaylanmıştır.");
                }

                #endregion Action History Type Kararı

                ////WorkFlowTraceWorker.OracleLog(LoginUser.DomainUserName, "WorkFlowHelper", "HistoryTypeParameterSet");
                long NewAssignLoginId = CheckingWorker.GetManager(AssignToLoginId);
                ////WorkFlowTraceWorker.OracleLog(LoginUser.DomainUserName, "WorkFlowHelper", "GetManager");
                Context.Parameters.AddOrChangeItem("NewAssignLoginId", NewAssignLoginId);
                Context.Parameters.AddOrChangeItem("Onay", "Yes");
                Context.Parameters.AddOrChangeItem("WfInstanceId", InstanceId);
                Context.Parameters.AddOrChangeItem("LastUpdatedBy", FormInformationHelper.LastUpdatedBy(LoginUser, assignedUser));
                if (!Context.Parameters.ContainsKey("AssignmentLoginType")) Context.Parameters.AddOrChangeItem("AssignmentLoginType", "LOGIN");
                if (!Context.Parameters.ContainsKey("AssignmentType")) Context.Parameters.AddOrChangeItem("AssignmentType", "TASKINBOX");
                if (IsWfContextSave) Context.Save();
                ////WorkFlowTraceWorker.OracleLog(LoginUser.DomainUserName, "WorkFlowHelper", "SaveContext");
                WorkflowHistoryWorker.Execute(fWfActionTaskInstance, LoginUser.LoginId, AssignToLoginId, ActionType, Comment);
                ////WorkFlowTraceWorker.OracleLog(LoginUser.DomainUserName, "WorkFlowHelper", "HistoryLoggins");
                ActionTaskWorker.Send(fWfActionTaskInstance.WfActionInstanceId, LoginUser.LoginId);
                ////WorkFlowTraceWorker.OracleLog(LoginUser.DomainUserName, "WorkFlowHelper", "ActionTaskWorkerSend");
                SetWFView(InstanceId, LoginUser.LoginId);
                ////WorkFlowTraceWorker.OracleLog(LoginUser.DomainUserName, "WorkFlowHelper", "SetWFView");
                UnitOfWork.Commit();
                ////WorkFlowTraceWorker.OracleLog(LoginUser.DomainUserName, "WorkFlowHelper", "AfterCommit");
            }
            ////WorkFlowTraceWorker.OracleLog(LoginUser.DomainUserName, "WorkFlowHelper", "EndWorkFlowFunction");
        }

        /// <summary>
        ///  Bu Method ile Form bulunduğu adımda onaylanır. Context Save edilebilir.
        /// </summary>
        /// <param name="InstanceId"></param>
        /// <param name="fWfActionTaskInstance"></param>
        /// <param name="LoginUser"></param>
        /// <param name="Context"></param>
        /// <param name="assignedUser"></param>
        /// <param name="IsWfContextSave"></param>
        public static void ApprovalWorkFlow(long InstanceId, FWfActionTaskInstance fWfActionTaskInstance, FLogin LoginUser, WFContext Context, FLogin assignedUser, bool IsWfContextSave, string Comment)
        {
            using (UnitOfWork.Start(true))
            {
                ActionTaskWorker.TakeOn(fWfActionTaskInstance.WfActionInstanceId, LoginUser.LoginId);
                long WorkflowInstanceId = fWfActionTaskInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId;
                long AssignToLoginId = CheckingWorker.GetAssignToLoginId(WorkflowInstanceId);
                if (AssignToLoginId == 0)
                {
                    AssignToLoginId = fWfActionTaskInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId;
                }
                if (AssignToLoginId != assignedUser.LoginId)
                {
                    AssignToLoginId = assignedUser.LoginId;
                }
                long NewAssignLoginId = CheckingWorker.GetManager(AssignToLoginId);
                Context.Parameters.AddOrChangeItem("NewAssignLoginId", NewAssignLoginId);
                Context.Parameters.AddOrChangeItem("Onay", "Yes");
                Context.Parameters.AddOrChangeItem("WfInstanceId", InstanceId);
                Context.Parameters.AddOrChangeItem("LastUpdatedBy", FormInformationHelper.LastUpdatedBy(LoginUser, assignedUser));
                if (!Context.Parameters.ContainsKey("AssignmentLoginType")) Context.Parameters.AddOrChangeItem("AssignmentLoginType", "LOGIN");
                if (!Context.Parameters.ContainsKey("AssignmentType")) Context.Parameters.AddOrChangeItem("AssignmentType", "TASKINBOX");
                if (IsWfContextSave) Context.Save();
                WorkflowHistoryWorker.Execute(fWfActionTaskInstance, LoginUser.LoginId, AssignToLoginId, WorkflowHistoryActionType.ACCEPTED, Comment);
                ActionTaskWorker.Send(fWfActionTaskInstance.WfActionInstanceId, LoginUser.LoginId);
                SetWFView(InstanceId, LoginUser.LoginId);
                UnitOfWork.Commit();
            }
        }

        public static void SendActionTaskInstance(long WfInstanceId, long LoginId, string Comment)
        {
          
            using (UnitOfWork.Start(true))
            {
                FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(WfInstanceId);
                FWfStateInstance CurrentStateIns = WfIns.WfCurrentState;
                FWfActionTaskInstance fWfActionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(WfIns.WfCurrentState.WfStateInstanceId);
                FLogin LoginUser = WFRepository<FLogin>.GetEntity(LoginId);
                WFContext Context = null;
                FLogin assignedUser = null;
                bool IsWfContextSave = false;
                ActionTaskWorker.TakeOn(fWfActionTaskInstance.WfActionInstanceId, LoginUser.LoginId);
                long WorkflowInstanceId = fWfActionTaskInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId;
                long AssignToLoginId = CheckingWorker.GetAssignToLoginId(WorkflowInstanceId);
                if (AssignToLoginId == 0)
                {
                    AssignToLoginId = fWfActionTaskInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId;
                }
                if (AssignToLoginId != assignedUser.LoginId)
                {
                    AssignToLoginId = assignedUser.LoginId;
                }
                // Buraya Bir Çözüm Üretmemiz Lazım. Bu Method Akışı Sadece ilerletir Onay yada Red durumuna karışmaz. Context i değiştirmez.
                WorkflowHistoryWorker.Execute(fWfActionTaskInstance, LoginUser.LoginId, AssignToLoginId, WorkflowHistoryActionType.ACCEPTED, Comment);
                ActionTaskWorker.Send(fWfActionTaskInstance.WfActionInstanceId, LoginUser.LoginId);
                SetWFView(WfInstanceId, LoginUser.LoginId);
                UnitOfWork.Commit();
            }
        }

        /// <summary>
        ///  Bu Method ile Form bulunduğu adımda reddedilir.
        /// </summary>X
        /// <param name="InstanceId"></param>
        /// <param name="fWfActionTaskInstance"></param>
        /// <param name="LoginUser"></param>
        /// <param name="Context"></param>
        /// <param name="assignedUser"></param>
        public static void RejectWorkFlow(long InstanceId, FWfActionTaskInstance fWfActionTaskInstance, FLogin LoginUser, WFContext Context, FLogin assignedUser, WorkflowHistoryActionType Enums, string Comment)
        {
            using (UnitOfWork.Start(true))
            {
                ActionTaskWorker.TakeOn(fWfActionTaskInstance.WfActionInstanceId, LoginUser.LoginId);
                WFContext wfContext = new WFContext(fWfActionTaskInstance.WfStateInstance.WfWorkflowInstance);
                Context.Parameters.AddOrChangeItem("WfInstanceId", InstanceId);
                Context.Parameters.AddOrChangeItem("Onay", "No");
                Context.Parameters.AddOrChangeItem("LastUpdatedBy", FormInformationHelper.LastUpdatedBy(LoginUser, assignedUser));
                Context.Parameters.AddOrChangeItem("Description", Comment);
                Context.Save();
                ActionTaskWorker.Send(fWfActionTaskInstance.WfActionInstanceId, LoginUser.LoginId);
                WorkflowHistoryWorker.Execute(fWfActionTaskInstance, LoginUser.LoginId, assignedUser.LoginId, Enums, Comment);
                SetWFView(InstanceId, LoginUser.LoginId);
                UnitOfWork.Commit();
            }
        }

        /// <summary>
        ///  Bu Method ile Form bulunduğu adımda bir başka kullanıcıya yönlendirilir.
        /// </summary>
        /// <param name="fWfActionTaskInstance"></param>
        /// <param name="LoginUser"></param>
        /// <param name="Context"></param>
        /// <param name="SelectedItemId"></param>
        public static void ForwardWorkFlow(long InstanceId, FWfActionTaskInstance fWfActionTaskInstance, FLogin LoginUser, long SelectedItemId)
        {
            if (SelectedItemId == null || SelectedItemId == 0)
            {
                throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Lütfen Yönlendireceğiniz kullanıcıyı seçiniz");
            }
            long[] m_LoginList = { SelectedItemId };
            long[] m_LoginGrList = { };
            ActionTaskWorker.Forward(fWfActionTaskInstance.WfActionInstanceId, m_LoginGrList, m_LoginList, LoginUser.LoginId);
            Digiturk.Workflow.Digiflow.AssignmentBase.AssignmentBase.Assign(SelectedItemId, "LOGIN", InstanceId, "WFVIEW");
            SetWFView(InstanceId, LoginUser.LoginId);
        }

        /// <summary>
        ///  Bu Method ile Form bulunduğu adımda bir başka kullanıcıya yorumlaması için gönderilir.
        /// </summary>
        /// <param name="fWfActionTaskInstance"></param>
        /// <param name="LoginUser"></param>
        /// <param name="Context"></param>
        /// <param name="SelectedItemId"></param>
        public static void SendtoCommendWorkFlow(long InstanceId, FWfActionTaskInstance fWfActionTaskInstance, long SelectedItemId, long LoginId)
        {
            if (SelectedItemId == null || SelectedItemId == 0)
            {
                throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Lütfen Yönlendireceğiniz kullanıcıyı seçiniz");
            }
            long[] m_LoginList = { SelectedItemId };
            long[] m_LoginGrList = { };
            ActionTaskWorker.SendToComment(fWfActionTaskInstance.WfActionInstanceId, m_LoginGrList, m_LoginList);
            Digiturk.Workflow.Digiflow.AssignmentBase.AssignmentBase.Assign(SelectedItemId, "LOGIN", InstanceId, "WFVIEW");

            SetWFView(InstanceId, LoginId);

            ////delege de view yetkisi yorumda26092024
            //List<long> delegeList = CheckingWorker.GetDelegeAssignLoginList(fWfActionTaskInstance.WfActionDef.WfStateDef.WfWorkflowDef.WfWorkflowDefId, LoginId);

            //if (delegeList.Count > 0)
            //{
            //    for (int i = 0; i < delegeList.Count; i++)
            //    {
            //        SetWFView(InstanceId, ConvertionHelper.ConvertValue<long>(delegeList[i].ToString()));                    
            //    }
            //}
          
        }

        /// <summary>
        ///  Bu Method ile Form bulunduğu adımda kendisine yorumlaması için gönderilen cevabı döner.
        /// </summary>
        /// <param name="wfIns"></param>
        /// <param name="fWfActionTaskInstance"></param>
        /// <param name="LoginUser"></param>
        /// <param name="CurrentPageMode"></param>
        /// <param name="Commend"></param>
        public static void SendRequestToCommentWorkFlow(FWfWorkflowInstance wfIns, FWfActionTaskInstance fWfActionTaskInstance, FLogin LoginUser, PageMode CurrentPageMode, string Commend)
        {
            FWfActionTaskComment taskCmt = new FWfActionTaskComment();
            taskCmt.Login = WFRepository<FLogin>.GetEntity(LoginUser.LoginId);
            taskCmt.CreatedDate = DateTime.Now;
            taskCmt.WfActionTaskInstance = fWfActionTaskInstance;
            taskCmt.Note = Commend;
            WFRepository<FWfActionTaskComment>.SaveEntity(taskCmt);
            if (CurrentPageMode == PageMode.Approve || CurrentPageMode == PageMode.Modify || CurrentPageMode == PageMode.Forwarded)
            {
                #region Versionlama İşlemi

                ISession session = FSessionHelper.GetDTWorkflowSession();
                //WFContext wfCtx = new WFContext(wfIns);
                WFContext wfCtx = new WFContext(WFRepository<FWfWorkflowInstance>.GetEntity(wfIns.WfWorkflowInstanceId));
                Type type = wfCtx.Entity.GetType();
                var newIns = Activator.CreateInstance(type);
                foreach (var prop in type.GetProperties())
                {
                    if (prop.Name == "RequestID")
                    {
                        continue;
                    }
                    prop.SetValue(newIns, type.GetProperty(prop.Name).GetValue(wfCtx.Entity, null), null);
                }
                type.GetProperty("VersionID").SetValue(newIns, fWfActionTaskInstance.WfActionInstanceId, null);
                session.SaveOrUpdate(newIns);

                #endregion Versionlama İşlemi

                var viewList = Digiturk.Workflow.Digiflow.DataAccessLayer.InboxWorker.GetViewList(LoginUser.LoginId.ToString());
                if (viewList.Count == 0 || viewList.IndexOf(wfIns.ToString()) < 0)
                {
                    Digiturk.Workflow.Digiflow.AssignmentBase.AssignmentBase.Assign(LoginUser.LoginId, "LOGIN", wfIns.WfWorkflowInstanceId, "WFVIEW");
                }
                viewList = null;
                session.Close();
                session.Dispose();
                /// Bunu neden yapıyor ....
                if (type.GetProperty("RequestID") != null)
                {
                    wfIns.EntityRefId = Convert.ToInt64(type.GetProperty("RequestID").GetValue(newIns, null));
                }
            }
            if (CurrentPageMode == PageMode.Modify)
            {
                WfDataHelpers.DelegateTaskToLogin(taskCmt);
            }
            SetWFView(wfIns.WfWorkflowInstanceId, LoginUser.LoginId);
            taskCmt = null;
        }

        /// <summary>
        ///  Bu Method ile Form bulunduğu adımda Bekletmeye alınır.
        /// </summary>
        /// <param name="WfIns"></param>
        /// <param name="SuspendDate"></param>
        /// <param name="taskIns"></param>
        /// <param name="LoginObject"></param>
        /// <param name="PageModes"></param>
        /// <param name="Commend"></param>
        public static void SuspendRequest(FWfWorkflowInstance WfIns, DateTime SuspendDate, FWfActionTaskInstance taskIns, FLogin LoginObject, string Commend)
        {
            using (UnitOfWork.Start())
            {
                ActionTaskWorker.TakeOn(taskIns.WfActionInstanceId, LoginObject.LoginId);
                ActionTaskWorker.Suspend(taskIns.WfActionInstanceId, SuspendDate, LoginObject.LoginId);
                Digiturk.Workflow.DigiFlow.Framework.NotificationHelper.SetNotification(WfIns.WfWorkflowInstanceId, SuspendDate);
                SetWFView(WfIns.WfWorkflowInstanceId, LoginObject.LoginId);
            }
        }

        /// <summary>
        ///  Bu Method ile Form bulunduğu adımdan devam ettirilir.
        /// </summary>
        /// <param name="WfIns"></param>
        /// <param name="taskIns"></param>
        /// <param name="LoginObject"></param>
        /// <param name="PageModes"></param>
        /// <param name="Commend"></param>
        public static void ResumeRequest(FWfWorkflowInstance WfIns, FWfActionTaskInstance taskIns, FLogin LoginObject, string Commend)
        {
            //ActionHelpers.SendRequestToCommentWorkFlow(WfIns,taskIns, LoginObject, PageModes, Commend);
            ActionTaskWorker.Resume(taskIns.WfActionInstanceId, LoginObject.LoginId);
            Digiturk.Workflow.DigiFlow.Framework.NotificationHelper.SetNotification(WfIns.WfWorkflowInstanceId);
            SetWFView(WfIns.WfWorkflowInstanceId, LoginObject.LoginId);
        }

        /// <summary>
        ///  Bu Method ile Form bulunduğu adımda İptal Edilir.
        /// </summary>
        /// <param name="wfIns"></param>
        /// <param name="LoginObject"></param>
        /// <param name="PageModes"></param>
        /// <param name="Commend"></param>
        public static void CancelWorkflow(FWfWorkflowInstance wfIns, FLogin LoginObject, string Commend)
        {
            using (UnitOfWork.Start())
            {
                DataTable DtbCancelJumpToStateWorkFlow = GetJumToStateWorkFlows(wfIns.WfWorkflowInstanceId);
                //FWfActionTaskInstance taskIns =  WfDataHelpers.GetLastActionTaskInstance(wfIns.WfCurrentState.WfStateInstanceFWfActionInstanceList.ToList().OrderBy(t => t.WfActionInstanceId));
                //ActionHelpers.SendRequestToCommentWorkFlow(wfIns, taskIns, LoginObject, PageModes, Commend);
                //WorkflowInstanceWorker.Cancel(wfIns.WfWorkflowInstanceId);
                if (wfIns.WfWorkflowStatusType.WfWorkflowStatusTypeCd != FWfWorkflowStatusTypeValues.Started)
                {
                    FWfWorkflowInstance Ins = WFRepository<FWfWorkflowInstance>.GetEntity(wfIns.WfWorkflowInstanceId);
                    FWfWorkflowStatusType NStatus = WFRepository<FWfWorkflowStatusType>.GetEntity(FWfWorkflowStatusTypeValues.Canceled);
                    Ins.WfWorkflowStatusType = NStatus;
                    WFRepository<FWfWorkflowInstance>.SaveEntity(Ins);
                    Ins = null;
                    NStatus = null;
                }
                else
                {
                    WorkflowInstanceWorker.Cancel(wfIns.WfWorkflowInstanceId);
                }
                for (int i = 0; i < DtbCancelJumpToStateWorkFlow.Rows.Count; i++)
                {
                    long WorkFlowInstanceId = Digiturk.Workflow.Digiflow.CoreHelpers.ConvertionHelper.ConvertValue<long>(DtbCancelJumpToStateWorkFlow.Rows[i]["JumpToStateInstanceId"]);
                    FWfWorkflowInstance WfInstance = WFRepository<FWfWorkflowInstance>.GetEntity(WorkFlowInstanceId);
                    FWfStateInstance WfStateInstance = WFRepository<FWfStateInstance>.GetEntity(WfInstance.WfCurrentState.WfStateInstanceId);
                    FWfActionTaskInstance CurrentActionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(WfStateInstance.WfCurrentActionInstanceId);
                    CancelWorkflow(WorkFlowInstanceId, LoginObject.LoginId);
                    WorkflowHistoryWorker.Execute(CurrentActionTaskInstance, LoginObject.LoginId, LoginObject.LoginId, WorkflowHistoryActionType.CANCEL, "Akış İptal Edildiği İçin Akış Atlatma Talebi de İptal Edilmiştir");
                    WfInstance = null;
                    WfStateInstance = null;
                    CurrentActionTaskInstance = null;
                }

                SetWFView(wfIns.WfWorkflowInstanceId, LoginObject.LoginId);
                DtbCancelJumpToStateWorkFlow = null;
            }
        }

        /// <summary>
        ///  Bu Method ile Form bulunduğu adımda İptal Edilir.
        /// </summary>
        /// <param name="wfIns"></param>
        /// <param name="LoginObject"></param>
        /// <param name="PageModes"></param>
        /// <param name="Commend"></param>
        public static void CancelWorkflow(long WfWorkflowInstanceId, long UserId)
        {
            using (UnitOfWork.Start())
            {
                FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(WfWorkflowInstanceId);
                long WfWorkflowDefId = WfIns.WfWorkflowDef.WfWorkflowDefId;
                long WfStateDefId = WfIns.WfCurrentState.WfStateDef.WfStateDefId;
                Digiturk.Workflow.Digiflow.Authorization.AuthoInfo AuthoInfoObj = new Digiturk.Workflow.Digiflow.Authorization.AuthoInfo(WfWorkflowDefId, WfStateDefId, UserId);
                if (AuthoInfoObj.CanCancel)
                {
                    WorkflowInstanceWorker.Cancel(WfWorkflowInstanceId);
                    SetWFView(WfWorkflowInstanceId, UserId);
                }
                else
                {
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Bu Akışı bu aşamada iptal edemezsiniz");
                }
                WfIns = null;
                AuthoInfoObj = null;
            }
        }

        /// <summary>
        ///  Bu Method ile Form bulunduğu adımda Finalize Edilir.
        /// </summary>
        /// <param name="wfIns"></param>
        /// <param name="LoginObject"></param>
        /// <param name="PageModes"></param>
        /// <param name="Commend"></param>
        private void FinalizeWorkflow(FWfWorkflowInstance wfIns, FLogin LoginObject, PageMode PageModes, string Commend)
        {
            FWfActionTaskInstance taskIns = WfDataHelpers.GetLastActionTaskInstance(wfIns.WfCurrentState.WfStateInstanceFWfActionInstanceList.ToList().OrderBy(t => t.WfActionInstanceId));
            ActionHelpers.SendRequestToCommentWorkFlow(wfIns, taskIns, LoginObject, PageModes, Commend);
            WorkflowInstanceWorker.Finalize(wfIns.WfWorkflowInstanceId);
            taskIns = null;
        }

        /// <summary>
        /// Bu Method ile Akış bulunduğu konumdan bir adım geriye döner.
        /// </summary>
        /// <param name="wfIns"></param>
        /// <param name="wfContext"></param>
        /// <param name="LoginObject"></param>
        /// <param name="PageModes"></param>
        /// <param name="Commend"></param>
        public static void RollbackWorkflow(FWfWorkflowInstance wfIns, WFContext wfContext, FLogin LoginObject, PageMode PageModes, string Commend)
        {
            //using (UnitOfWork.Start())
            //{
            //    #region  PreviusStateInstance bulunur.
            //        var prevStateIns = wfIns.WfWorkflowInstanceFWfStateInstanceList.OrderByDescending(t => t.WfStateInstanceId).ToList()[1];
            //    #endregion

            //            #region  WorkFlowTipi belirlenir.
            //                WorkflowType wfType = WorkflowType.None;
            //                if (wfContext.Entity is GSMRequest){wfType = WorkflowType.GSMRequest;}
            //                else if (wfContext.Entity is EmployeeRequest){wfType = WorkflowType.EmployeeRequest;}
            //                else if (wfContext.Entity is DelegationRequest){wfType = WorkflowType.DelegationRequest;}
            //                else if (wfContext.Entity is JumpToStateRequest){wfType = WorkflowType.JumpToStateRequest;}
            //                else if (wfContext.Entity is EducationRequest){wfType = WorkflowType.EducationRequest;}
            //            #endregion

            //            #region  Entity Get edilir ve RelatedLog relatedLogin ve createdBy bulunur
            //        string relatedLogin = string.Empty;
            //        FLogin createdBy = null;

            //        switch (wfType)
            //        {
            //            case WorkflowType.EmployeeRequest:
            //                var item0 = WFRepository<Digiturk.Workflow.Digiflow.Entities.EmployeeRequest>.GetEntity(prevStateIns.WfWorkflowInstance.EntityRefId);
            //                createdBy = WFRepository<FLogin>.GetEntity(item0.CreatedBy);
            //                break;
            //            case WorkflowType.OutsourceRequest:
            //                var item1 = WFRepository<Digiturk.Workflow.Digiflow.Entities.EmployeeRequest>.GetEntity(prevStateIns.WfWorkflowInstance.EntityRefId);
            //                createdBy = WFRepository<FLogin>.GetEntity(item1.CreatedBy);
            //                break;
            //            case WorkflowType.TraineeRequest:
            //                var item2 = WFRepository<Digiturk.Workflow.Digiflow.Entities.EmployeeRequest>.GetEntity(prevStateIns.WfWorkflowInstance.EntityRefId);
            //                createdBy = WFRepository<FLogin>.GetEntity(item2.CreatedBy);
            //                break;
            //            case WorkflowType.DelegationRequest:
            //                var item3 = WFRepository<Digiturk.Workflow.Digiflow.Entities.DelegationRequest>.GetEntity(prevStateIns.WfWorkflowInstance.EntityRefId);
            //                createdBy = WFRepository<FLogin>.GetEntity(item3.CreatedBy);
            //                break;
            //            case WorkflowType.JumpToStateRequest:
            //                var item4 = WFRepository<Digiturk.Workflow.Digiflow.Entities.JumpToStateRequest>.GetEntity(prevStateIns.WfWorkflowInstance.EntityRefId);
            //                createdBy = WFRepository<FLogin>.GetEntity(item4.CreatedBy);
            //                break;
            //            case WorkflowType.EducationRequest:
            //                var item5 = WFRepository<Digiturk.Workflow.Digiflow.Entities.EducationRequest>.GetEntity(prevStateIns.WfWorkflowInstance.EntityRefId);
            //                createdBy = WFRepository<FLogin>.GetEntity(item5.CreatedBy);
            //                break;
            //            case WorkflowType.GSMRequest:
            //                var item6 = WFRepository<Digiturk.Workflow.Digiflow.Entities.GSMRequest>.GetEntity(prevStateIns.WfWorkflowInstance.EntityRefId);
            //                relatedLogin = item6.RelatedLoginAdıSoyadi;
            //                createdBy = WFRepository<FLogin>.GetEntity(item6.CreatedBy);
            //                break;
            //        }
            //    #endregion

            //            #region  AddTaskCommend atılır
            //        //AddTaskComment(WFRepository<FWfActionTaskInstance>.GetEntity(targetStateInstance.WfWorkflowInstance.WfCurrentState.WfCurrentActionInstanceId.Value), login, comment, pageMode);
            //    #endregion

            //            #region  currAction Get edilir.
            //        FWfActionTaskInstance ActionTaskInstance = new FWfActionTaskInstance();
            //        FWfActionInstance lastActionTaskInstance = null;
            //        var currAction = WFRepository<FWfActionInstance>.GetEntity(prevStateIns.WfWorkflowInstance.WfCurrentState.WfCurrentActionInstanceId.Value);
            //    #endregion

            //            #region  Assigment List Getirilir ve Geriye Doğru Assigment işlemi yapılır
            //                List<FLogin> toList = new List<FLogin>();
            //                //wfContext.Parameters.AddOrChangeItem("ActionOwner", WorkFlowDataHelper.GetCommentsLoginString(WorkFlowDataHelper.GetLastComment(currAction.WfStateInstance.WfWorkflowInstance)));
            //                //wfContext.Parameters.AddOrChangeItem("WfInstanceId", currAction.WfActionInstanceId);
            //                IList<FWfAssignment> oldactionAssignments = AssignmentHelper.GetAssignmentList("TASKINBOX", prevStateIns.WfWorkflowInstance.WfCurrentState.WfCurrentActionInstanceId.Value, null);
            //                var actList = WFRepository<FWfActionTaskInstance>.GetEntityList(DetachedCriteria.For<FWfActionTaskInstance>().Add(Expression.Eq("WfStateInstance.WfStateInstanceId", prevStateIns.WfStateInstanceId)).AddOrder(new Order("WfActionInstanceId", false)));
            //                if (actList.Count > 0)
            //                {
            //                        #region Sonraki Kullanıcı Seçilir
            //                                var oldassignments2 = AssignmentHelper.GetAssignmentList(FWfAssignmentTypeValues.TaskInbox.ToString(), prevStateIns.WfWorkflowInstance.WfCurrentState.WfCurrentActionInstanceId.Value, 1487);
            //                                if (oldassignments2.Count > 0)
            //                                {
            //                                    foreach (var oa in oldassignments2)
            //                                    {
            //                                        var assignment = oa;
            //                                        var loginEntity = WFRepository<FLogin>.GetEntity(assignment.AssignedOwnerRefId);
            //                                        // rollback sonrası atanan kişiye mail gönder
            //                                        toList.Add(loginEntity);
            //                                        wfContext.Parameters.AddOrChangeItem("ActionDate", DateTime.Now.ToString("dd.MM.yyyy hh:mm:ss"));
            //                                        wfContext.Parameters.AddOrChangeItem("ActionDescription", Commend);
            //                                        wfContext.Parameters.AddOrChangeItem("Action", "geri alındı");
            //                                        wfContext.Parameters.AddOrChangeItem("ActionTo", WfDataHelpers.GetLoginNameSurname(oldassignments2.First().AssignedOwnerRefId));
            //                                        wfContext.Parameters.AddOrChangeItem("RelatedLogin", relatedLogin);
            //                                        wfContext.Parameters.AddOrChangeItem("WorkflowName", wfContext.Parameters["WFName"]);
            //                                        wfContext.Parameters.AddOrChangeItem("ActionOwner", WfDataHelpers.GetLoginNameSurname(LoginObject));
            //                                        MailHelper.SendMail(10, currAction, wfContext, toList, new List<FLogin>());
            //                                        toList.Clear();
            //                                    }
            //                                }
            //                                toList.Clear();
            //                                lastActionTaskInstance = actList.First();
            //                                wfContext.LoadInstanceParametersFromHistory(lastActionTaskInstance.WfActionInstanceId);
            //                                wfContext.Save();
            //                                IList<FWfAssignment> actionAssignments = AssignmentHelper.GetAssignmentList("TASKINBOX", lastActionTaskInstance.WfActionInstanceId, null);
            //                                //Set dynamic assignment parameters
            //                                if (actionAssignments.Count > 0)
            //                                {
            //                                    long assignToID = actionAssignments.First().AssignedOwnerRefId;
            //                                    wfContext.Parameters.AddOrChangeItem("AssignToID", assignToID.ToString());
            //                                    wfContext.Save();
            //                                }
            //                        #endregion

            //                        #region Rollback Entity Kaydedilir
            //                            Type xtype = wfContext.Entity.GetType();
            //                            EntityBase asdadsasdsad = new EntityBase();
            //                            ISession session =  FSessionHelper.GetDTWorkflowSession();
            //                            var xyz = session.CreateCriteria(xtype).Add(Expression.Eq("VersionID", lastActionTaskInstance.WfActionInstanceId)).List();
            //                            foreach (var item in xyz)
            //                            {
            //                                prevStateIns.WfWorkflowInstance.EntityRefId = Convert.ToInt64(xtype.GetProperty("RequestID").GetValue(item, null));
            //                                break;
            //                            }
            //                        #endregion

            //                        #region  Jump to target state to rollback workflow
            //                EventJumpToStateWorker jumpToStateWorker = new EventJumpToStateWorker();
            //                jumpToStateWorker.Execute(prevStateIns.WfStateDef.WfStateDefId, prevStateIns.WfWorkflowInstance.WfCurrentState);
            //                if (oldactionAssignments.Count > 0)
            //                {
            //                    var loginList = WFRepository<FLogin>.GetEntityList(DetachedCriteria.For<FLogin>().Add(Expression.Eq("LoginId", oldactionAssignments.First().AssignedOwnerRefId)));
            //                    wfContext.Parameters.AddOrChangeItem("ActionTo", WfDataHelpers.GetLoginNameSurname(loginList.First().LoginId));
            //                    wfContext.Parameters.AddOrChangeItem("Action", "geri alındı");
            //                    wfContext.Parameters.AddOrChangeItem("WorkflowName", currAction.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.Name);
            //                    wfContext.Parameters.AddOrChangeItem("ActionOwner", WfDataHelpers.GetCommentsLoginString(WfDataHelpers.GetLastComment(currAction.WfStateInstance.WfWorkflowInstance)));
            //                    wfContext.Parameters.AddOrChangeItem("RelatedLogin", relatedLogin);
            //                    foreach (var asg in oldactionAssignments)
            //                    {
            //                        foreach (var lg in loginList)
            //                        {
            //                            toList.Add(lg);
            //                        }
            //                    }
            //                    MailHelper.SendMail(10, currAction, wfContext, toList, new List<FLogin>());
            //                    toList.Clear();
            //                    toList.Add(currAction.WfStateInstance.WfWorkflowInstance.OwnerLogin);
            //                    wfContext.Parameters.AddOrChangeItem("Action", "geri alındı");
            //                    wfContext.Parameters.AddOrChangeItem("ActionTo", WfDataHelpers.GetLoginNameSurname(currAction.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId));
            //                    wfContext.Parameters.AddOrChangeItem("RelatedLogin", "İlgili kişi: " + WfDataHelpers.GetLoginNameSurname(loginList.First().LoginId));
            //                    MailHelper.SendMail(10, currAction, wfContext, toList, new List<FLogin>());
            //                }
            //            #endregion

            //                        #region Rollback sonrası Atanan kişiye mail gönderilir
            //                        if (actionAssignments.Count > 0)
            //                        {
            //                            toList.Add(WFRepository<FLogin>.GetEntity(actionAssignments.First().AssignedOwnerRefId));
            //                            wfContext.Parameters.AddOrChangeItem("Action", "geri alındı");
            //                            wfContext.Parameters.AddOrChangeItem("ActionTo", WfDataHelpers.GetLoginNameSurname(actionAssignments.First().AssignedOwnerRefId));
            //                            wfContext.Parameters.AddOrChangeItem("RelatedLogin", relatedLogin);
            //                            wfContext.Parameters.AddOrChangeItem("ActionOwner", WfDataHelpers.GetCommentsLoginString(WfDataHelpers.GetLastComment(currAction.WfStateInstance.WfWorkflowInstance)));
            //                            wfContext.Parameters.AddOrChangeItem("ActionDescription", Commend);
            //                            MailHelper.SendMail(10, currAction, wfContext, toList, new List<FLogin>());
            //                            toList.Clear();

            //                        }
            //            #endregion

            //                        #region Oluşturan Kişiye Mail Gönderilir
            //                if (createdBy != null)
            //                {
            //                    toList.Add(createdBy);
            //                    wfContext.Parameters.AddOrChangeItem("Action", "geri alındı");
            //                    wfContext.Parameters.AddOrChangeItem("ActionTo", WfDataHelpers.GetLoginNameSurname(createdBy.LoginId));
            //                    wfContext.Parameters.AddOrChangeItem("RelatedLogin", relatedLogin);
            //                    wfContext.Parameters.AddOrChangeItem("ActionOwner", WfDataHelpers.GetCommentsLoginString(WfDataHelpers.GetLastComment(currAction.WfStateInstance.WfWorkflowInstance)));
            //                    wfContext.Parameters.AddOrChangeItem("ActionDescription", Commend);
            //                    MailHelper.SendMail(10, currAction, wfContext, toList, new List<FLogin>());
            //                    toList.Clear();
            //                }
            //            #endregion
            //                }
            //            #endregion
            //}
        }

        /// <summary>
        /// MasterObject i verilen entity nin detail ini çıkartatan fonksiyon
        /// </summary>
        /// <param name="MasterObject"></param>
        /// <returns></returns>
        public static List<EntityBase> GetDetailEntityList(IEntity MasterObject)
        {
            //var lg = WFRepository<FLogin>.GetEntityList(NHibernate.Criterion.DetachedCriteria.For<FLogin>().Add(NHibernate.Criterion.Expression.Eq("LoginId", userId)));
            //IList<InstallationDetailRequest> DetailList=WFRepository<InstallationDetailRequest>.GetEntityList(NHibernate.Criterion.DetachedCriteria.For<InstallationDetailRequest>().Add(
            //NHibernate.Criterion.Expression.Eq(""//);
            List<EntityBase> detaillist = new List<EntityBase>();
            var Objectlist = WFRepository<EntityBase>.GetEntityList(NHibernate.Criterion.DetachedCriteria.For<EntityBase>().Add(NHibernate.Criterion.Expression.Eq("RelatedRequestID", MasterObject.RequestId)));
            return detaillist;
        }

        /// <summary>
        /// Entity i Herhangi bir State den geçerken versionlamak için kullanılır.
        /// </summary>
        /// <param name="WfInstanceId"></param>
        /// <param name="WfActionInstanceId"></param>
        public static void VersionUpdateToEntity(long WfInstanceId, long WfActionInstanceId)
        {
            using (UnitOfWork.Start())
            {
                ISession session = FSessionHelper.GetDTWorkflowSession();
                //WFContext wfCtx = new WFContext(wfIns);
                FWfWorkflowInstance wfIns = WFRepository<FWfWorkflowInstance>.GetEntity(WfInstanceId);
                if (wfIns.WfWorkflowInstanceFWfInstanceParameterList != null)
                {
                    WFContext wfCtx = new WFContext(wfIns);
                    Type type = wfCtx.Entity.GetType();
                    var newIns = Activator.CreateInstance(type);
                    foreach (var prop in type.GetProperties())
                    {
                        if (prop.Name == "RequestID")
                        {
                            continue;
                        }
                        prop.SetValue(newIns, type.GetProperty(prop.Name).GetValue(wfCtx.Entity, null), null);
                    }
                    type.GetProperty("VersionID").SetValue(newIns, WfActionInstanceId, null);
                    session.SaveOrUpdate(newIns);
                    wfIns = null;
                    wfCtx = null;
                    type = null;
                    session = null;
                }
            }
        }

        /// <summary>
        ///  RollBack işlemi sırası
        /// </summary>
        /// <param name="wfIns"></param>
        /// <param name="wfContext"></param>
        public static void RollbackWorkflow(FWfWorkflowInstance wfIns, WFContext wfContext, long currentActionTaskInstanceId)
        {
            // 1-  Bir Önceki State Bulunur
            // 2-  Bir Önceki Assigment Bulunur
            // 3-  Bir Önceki Assigment a atama yapılır
            // 4-  Atamaya ilişkin Mail atılır.

            #region Bir Önceki State Instance bulunur.

            FWfStateInstance prevStateIns = wfIns.WfWorkflowInstanceFWfStateInstanceList.OrderByDescending(t => t.WfStateInstanceId).ToList()[1];

            #endregion Bir Önceki State Instance bulunur.

            #region Bir Önceki ActionTaskInstance bulunur

            long prevActionTaskInstanceId = 0; //prevStateIns.WfStateInstanceFWfActionInstanceList.ToList()[0].WfActionInstanceId;
            foreach (var item in prevStateIns.WfStateInstanceFWfActionInstanceList.ToList())
            {
                if (item is FWfActionTaskInstance)
                {
                    prevActionTaskInstanceId = item.WfActionInstanceId;
                    break;
                }
            }
            FWfActionTaskInstance prevActionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(prevActionTaskInstanceId);

            #endregion Bir Önceki ActionTaskInstance bulunur

            #region Bir Önceki State deki Atanmış kullanıcılar bulunur.

            IList<FWfAssignment> AssignmentList = AssignmentHelper.GetAssignmentList("TASKINBOX", prevActionTaskInstanceId, wfIns.WfWorkflowDef.WfWorkflowDefId);

            #endregion Bir Önceki State deki Atanmış kullanıcılar bulunur.

            #region LastEntityRefId Bulunur ve Geri Alma İşlemi Yapılır

            long LastEntityRefId = DigiFlowRollBackHelper.EntityRefId(prevActionTaskInstanceId);
            DigiFlowRollBackHelper.Rollback(prevStateIns, LastEntityRefId, currentActionTaskInstanceId);
            System.Threading.Thread.Sleep(1000);
            #endregion LastEntityRefId Bulunur ve Geri Alma İşlemi Yapılır

            #region Yeni ActionInstance bulunur

            FWfWorkflowInstance NewWFInstance = WFRepository<FWfWorkflowInstance>.GetEntity(wfIns.WfWorkflowInstanceId);
            FWfActionInstance actionInstance = WFRepository<FWfActionInstance>.GetEntity(NewWFInstance.WfCurrentState.WfCurrentActionInstanceId);

            #endregion Yeni ActionInstance bulunur

            #region Bir Önceki ActionTaskInstance da atanmış Kullanıcılar bulunarak geriye doğru Assignment Yapılır

            foreach (var item in AssignmentList)
            {
                List<long> AssignToLoginList = new List<long>();
                AssignToLoginList.Add(item.AssignedOwnerRefId);

                #region Assignment İşlemleri

                StaticAssigmentHelper.AssignToStaticOperation(AssignToLoginList, actionInstance);

                #endregion Assignment İşlemleri
            }

            #endregion Bir Önceki ActionTaskInstance da atanmış Kullanıcılar bulunarak geriye doğru Assignment Yapılır
        }

        /// <summary>
        /// Görev Atama İşlemi Gerçekleştirmek için kullanılır
        /// </summary>
        /// <param name="InstanceId">InstanceId</param>
        /// <param name="SendedTaskLoginId">LoginId</param>
        /// <param name="fWfActionTaskInstance">ActionTaskInstacne</param>
        /// <param name="LoginUser">Login Olan User</param>
        /// <param name="Context">Workflow Contex</param>
        /// <param name="assignedUser">Atanan Kullanıcı</param>
        /// <param name="IsWfContextSave">Contex in Save edilip edilmemesine karar verir</param>
        /// <param name="Comment"> Comment içeriği</param>
        public static void Sendtask(long InstanceId, long SendedTaskLoginId, FWfActionTaskInstance fWfActionTaskInstance, FLogin LoginUser, WFContext Context, FLogin assignedUser, bool IsWfContextSave, string Comment)
        {
            ActionTaskWorker.TakeOn(fWfActionTaskInstance.WfActionInstanceId, LoginUser.LoginId);
            long WorkflowInstanceId = fWfActionTaskInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId;
            long AssignToLoginId = CheckingWorker.GetAssignToLoginId(WorkflowInstanceId);
            if (AssignToLoginId == 0)
            {
                AssignToLoginId = fWfActionTaskInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId;
            }
            long NewAssignLoginId = CheckingWorker.GetManager(AssignToLoginId);
            Context.Parameters.AddOrChangeItem("NewAssignLoginId", NewAssignLoginId);
            Context.Parameters.AddOrChangeItem("Onay", "Yes");
            Context.Parameters.AddOrChangeItem("WfInstanceId", InstanceId);
            Context.Parameters.AddOrChangeItem("LastUpdatedBy", FormInformationHelper.LastUpdatedBy(LoginUser, assignedUser));
            Context.Parameters.AddOrChangeItem("ContractForwardPersonel", SendedTaskLoginId.ToString());
            if (!Context.Parameters.ContainsKey("AssignmentLoginType")) Context.Parameters.AddOrChangeItem("AssignmentLoginType", "LOGIN");
            if (!Context.Parameters.ContainsKey("AssignmentType")) Context.Parameters.AddOrChangeItem("AssignmentType", "TASKINBOX");
            if (IsWfContextSave) Context.Save();
            ActionTaskWorker.Send(fWfActionTaskInstance.WfActionInstanceId, LoginUser.LoginId);
            WorkflowHistoryWorker.Execute(fWfActionTaskInstance, LoginUser.LoginId, AssignToLoginId, WorkflowHistoryActionType.SENDTASK, Comment);
        }
        /// <summary>
        /// WfContext'e çoklu atılmış parametreleri siler
        /// </summary>
        /// <param name="wfInsId"></param>
        public static void deleteMultipleParameter(long wfInsId)
        {
            for (int i = 0; i < 2; i++)
            {
                List<CustomParameterList> Param = new List<CustomParameterList>();
                Param.Add(new CustomParameterList(":WF_WORKFLOW_INSTANCE_ID", wfInsId));
                string sql = "delete from F_WF_INSTANCE_PARAMETER where WF_INSTANCE_PARAMETER_ID in ( select  min(WF_INSTANCE_PARAMETER_ID) as WF_INSTANCE_PARAMETER_ID from FRAMEWORK.F_WF_INSTANCE_PARAMETER a where A.WF_WORKFLOW_INSTANCE_ID=:WF_WORKFLOW_INSTANCE_ID group by wf_workflow_instance_ID , name having count(*) > 1)";
                Digiturk.Workflow.Digiflow.DataAccessLayer.ModelWorking.ExecuteQuery("FrameworkConnection", sql, Param);
            }
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="LoginId"></param>
        /// <param name="WorkFlowInstanceId"></param>
        public static void AssignMonitoring(long LoginId, long WorkFlowInstanceId)
        {
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="LoginId"></param>
        /// <param name="WorkFlowInstanceId"></param>
        public static void DenyMonitoring(long LoginId, long WorkFlowInstanceId)
        {
        }

        /// <summary>
        /// İptal Edilen Akışın İptal edilecek Açık Akış Atlatma Talepleri Çekilir
        /// </summary>
        /// <param name="WorkFlowInstanceId"></param>
        /// <returns></returns>
        public static DataTable GetJumToStateWorkFlows(long WorkFlowInstanceId)
        {
            string SQL = @"Select
           FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID as MasterInstanceId,
           JUMPTOSTATEINSTANCE.WF_WORKFLOW_INSTANCE_ID as JumpToStateInstanceId
           from FRAMEWORK.F_WF_WORKFLOW_INSTANCE
           Left Join DT_WORKFLOW.WF_DF_JUMP_TO_STATE_REQUEST On DT_WORKFLOW.WF_DF_JUMP_TO_STATE_REQUEST.FLOW_INSTANCE_ID=FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID
           Left Join  FRAMEWORK.F_WF_WORKFLOW_INSTANCE JumpToStateInstance On JUMPTOSTATEINSTANCE.ENTITY_REF_ID=DT_WORKFLOW.WF_DF_JUMP_TO_STATE_REQUEST.JUMP_TO_STATE_REQUEST_ID
           Where
           JUMPTOSTATEINSTANCE.WF_WORKFLOW_DEF_ID=1340
           and JUMPTOSTATEINSTANCE.WF_WORKFLOW_STATUS_TYPE_CD='STARTED'
           and FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID=:WorkFlowInstanceId
           Order By FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID Asc";
            SQL = SQL.Replace(":WorkFlowInstanceId", WorkFlowInstanceId.ToString());
            return Digiturk.Workflow.Digiflow.DataAccessLayer.ModelWorking.GetDataTable("DefaultConnection", SQL);
        }

        /// <summary>
        /// Son İşlemi Yapan Kullanıcının işlemi Kimin Yerine yaptığı bilgisi çekiliyor
        /// </summary>
        /// <param name="WfInstanceId"></param>
        /// <returns></returns>
        public static string GetLastActionUserNameSurName(long WfInstanceId)
        {
            string SQL = "Select DT_WORKFLOW.DIGIFLOWPACKACE.GET_LAST_MODIFIED_BY(:WfInstanceId) from dual";
            SQL = SQL.Replace(":WfInstanceId", WfInstanceId.ToString());
            return Digiturk.Workflow.Digiflow.DataAccessLayer.ModelWorking.GetOnlyColumnSQL<string>("DefaultConnection", SQL, new List<CustomParameterList>());
        }

        /// <summary>
        /// Delege edilip işlem yapan kullanıcıya görüntüleme yetkisi veriyoruz.
        /// </summary>
        /// <param name="WfInstanceId"></param>
        /// <param name="LoginId"></param>
        public static void SetWFView(long WfInstanceId, long LoginId)
        {
            //Bir probleme yol açarsa commentler açılacak. //Kerem
            //var viewList = Digiturk.Workflow.Digiflow.DataAccessLayer.InboxWorker.GetViewList(LoginId.ToString());
            //FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(WfInstanceId);
            //if (viewList.Count == 0 || viewList.IndexOf(WfIns) < 0)
            // {
            try
            {
                Digiturk.Workflow.Digiflow.AssignmentBase.AssignmentBase.Assign(LoginId, "LOGIN", WfInstanceId, "WFVIEW");
            }
            catch (Exception ex)
            {
                //throw;
            }
            //}
            //WfIns = null;
            //viewList = null;
        }
    }
}