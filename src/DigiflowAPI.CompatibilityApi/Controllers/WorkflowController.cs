﻿using DigiflowAPI.CompatibilityApi.Common.Converters;
using DigiflowAPI.CompatibilityApi.DTOs.Workflow;
using DigiflowAPI.Resources;
using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.DataAccessLayer;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.Framework;
using Digiturk.Workflow.Digiflow.WebCore.WorkFlowEntites;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Engine;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text.Json;
using System.Web.Http;
using IEntity = Digiturk.Workflow.Digiflow.Entities.IEntity;
using Digiturk.Workflow.Digiflow.Authorization;
using ActionHelpers = CompatibiliyAPI.ActionHelpers;
using Castle.Core;
using DevExpress.CodeParser;
using Digiturk.Workflow.Digiflow.Entities.Enums;
using System.Web.UI.WebControls;

namespace DigiflowAPI.CompatibilityApi.Controllers
{
    [RoutePrefix("api/workflow")]
    public class WorkflowController : ApiController
    {
        private readonly JsonSerializerOptions _jsonOptions;

        public WorkflowController()
        {
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                Converters =
                {
                    new EntityJsonConverter(),
                    new DetailEntityJsonConverter()
                }
            };
        }

        public string GetLoginNameSurname(long loginId)
        {
            string Command = @"Select DT_WORKFLOW.VW_USER_INFORMATION.NAME_SURNAME from DT_WORKFLOW.VW_USER_INFORMATION where DT_WORKFLOW.VW_USER_INFORMATION.LOGIN_ID='" + loginId.ToString() + "'";
            return ModelWorking.GetOnlyColumnSQL<string>("FrameworkConnection", Command, new List<CustomParameterList>());
        }

        [HttpGet]
        [Route("delegated-comment-login/{taskCommentId}")]
        public IHttpActionResult GetDelegatedCommentLogin(long taskCommentId)
        {
            try
            {
                // Get the comment
                var comment = WFRepository<FWfActionTaskComment>.GetEntity(taskCommentId);
                if (comment == null)
                {
                    return NotFound();
                }

                // Get owner string
                string owner = string.Empty;

                // Get assignment owner
                var assignments = AssignmentHelper.GetAssignmentList(
                    FWfAssignmentTypeValues.TaskInbox,
                    comment.WfActionTaskInstance.WfActionInstanceId,
                    comment.WfActionTaskInstance.WfActionDef.WfActionDefId
                );

                if (!assignments.Any())
                {
                    return NotFound();
                }

                long assignmentOwner = assignments.First().AssignedOwnerRefId;

                // Get delegation chain
                string delChain = WorkflowDelegationHelper.GetDelegationChain(
                    assignmentOwner,
                    comment.WfActionTaskInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId,
                    comment.CreatedDate.Value
                );

                if (string.IsNullOrEmpty(delChain))
                {
                    owner = GetLoginNameSurname(comment.Login.LoginId);
                }
                else
                {
                    if (delChain.IndexOf(',') < 0)
                    {
                        owner = GetLoginNameSurname(comment.Login.LoginId);
                    }
                    else
                    {
                        var owners = delChain.Split(',');
                        var ownerNames = owners.Select(own => GetLoginNameSurname(Convert.ToInt64(own)))
                            .Select(name => $" on behalf of {name}");

                        owner = string.Join("", ownerNames);
                        owner = owner.Remove(owner.Length - 8); // Remove last " on behalf of"

                        if (!owner.Contains(GetLoginNameSurname(comment.Login.LoginId)))
                        {
                            owner = $"{GetLoginNameSurname(comment.Login.LoginId)} on behalf of {owner}";
                        }
                    }
                }

                var result = new
                {
                    DelegationChain = delChain,
                    FormattedString = owner,
                    CommentOwner = new
                    {
                        LoginId = comment.Login.LoginId,
                        Name = GetLoginNameSurname(comment.Login.LoginId)
                    },
                    Delegates = delChain?.Split(',')
                        .Select(id => long.Parse(id))
                        .Select(id => new
                        {
                            LoginId = id,
                            Name = GetLoginNameSurname(id)
                        })
                        .ToList()
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpPost]
        [Route("create")]
        public IHttpActionResult Create([FromBody] CreateWorkflowRequestDto request)
        {
            var language = Request.Headers.GetValues("Accept-Language").FirstOrDefault() ?? "tr";

            try
            {
                IEntity entity = DeserializeEntity(request.EntityJson, request.WorkFlowDefinitionId);
                if (entity == null)
                {
                    return BadRequest("Entity data is missing or invalid.");
                }

                var details = DeserializeDetails(request.DetailJson);
                var details2 = DeserializeDetails(request.Detail2Json); 
                var details3 = DeserializeDetails(request.Detail3Json);
                var details4 = DeserializeDetails(request.Detail4Json);

                if (HasDetails(details, details2, details3, details4))
                {
                    CreateWorkflow(entity, details, details2, details3, details4, request.WorkFlowDefinitionId, request.LoginId);
                }
                else
                {
                    CreateWorkflow(entity, request.WorkFlowDefinitionId, request.LoginId);
                }

                return Ok(ResourceUtility.GetLocalizedValue("CreateWorkflow", "WorkflowCreatedSuccessfully", language, "Workflo" +
                    "w"));
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpPost]
        [Route("approve")]
        public IHttpActionResult Approve([FromBody] ApprovalWorkflowRequestDto request)
        {
            var language = Request.Headers.GetValues("Accept-Language").FirstOrDefault() ?? "tr";

            try
            {
                using (UnitOfWork.Start(true))
                {

                    var currentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(request.InstanceId);
                    var currentState = WFRepository<FWfStateInstance>.GetEntity(currentWfIns.WfCurrentState.WfStateInstanceId);
                    var actionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(currentState.WfCurrentActionInstanceId);
                    var context = new WFContext(currentWfIns);

                    var loginUser = WFRepository<FLogin>.GetEntity(request.LoginUserId);
                    long AssignToLoginId = CheckingWorker.GetAssignToLoginId(currentWfIns.WfWorkflowInstanceId);
                    var assignedUser = WFRepository<FLogin>.GetEntity(request.AssignedUserId!= null && request.AssignedUserId > 0? request.AssignedUserId : AssignToLoginId);

                    if (request.ActionType != null)
                    {
                        ActionTaskWorker.TakeOn(actionTaskInstance.WfActionInstanceId, loginUser.LoginId);

                        long WorkflowInstanceId = actionTaskInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId;
                        if (AssignToLoginId == 0)
                        {
                            AssignToLoginId = actionTaskInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId;
                        }

                        #region Action History Type Kararı

                        if (request.ActionType == WorkflowHistoryActionType.ACCEPTED)
                        {
                            context.Parameters.AddOrChangeItem("ActionType", "Onaylanmıştır.");
                        }
                        else if (request.ActionType == WorkflowHistoryActionType.CORRECTION)
                        {
                            context.Parameters.AddOrChangeItem("ActionType", "düzeltme olarak onaylanmıştır.");
                        }
                        else if (request.ActionType == WorkflowHistoryActionType.CNDACCCEPT)
                        {
                            context.Parameters.AddOrChangeItem("ActionType", "şartlı olarak onaylanmıştır.");
                        }
                        else
                        {
                            context.Parameters.AddOrChangeItem("ActionType", "Onaylanmıştır.");
                        }

                        #endregion Action History Type Kararı

                        long NewAssignLoginId = CheckingWorker.GetManager(AssignToLoginId);
                        context.Parameters.AddOrChangeItem("NewAssignLoginId", NewAssignLoginId);
                        context.Parameters.AddOrChangeItem("Onay", "Yes");
                        context.Parameters.AddOrChangeItem("WfInstanceId", request.InstanceId);
                        context.Parameters.AddOrChangeItem("LastUpdatedBy", FormInformationHelper.LastUpdatedBy(loginUser, assignedUser));
                        if (!context.Parameters.ContainsKey("AssignmentLoginType")) context.Parameters.AddOrChangeItem("AssignmentLoginType", "LOGIN");
                        if (!context.Parameters.ContainsKey("AssignmentType")) context.Parameters.AddOrChangeItem("AssignmentType", "TASKINBOX");

                        if (request.SendTaskUserId != null)
                        {
                            context.Parameters.AddOrChangeItem("ContractForwardPersonel", request.SendTaskUserId);
                            FlowAdminOprObject FlowAdminOprs = new FlowAdminOprObject(request.InstanceId, currentWfIns.WfWorkflowDef.WfWorkflowDefId);
                            if (request.IsWfContextSave) 
                                context.Save();
                            ActionTaskWorker.Send(actionTaskInstance.WfActionInstanceId, loginUser.LoginId);
                            WorkflowHistoryWorker.Execute(actionTaskInstance, loginUser.LoginId, AssignToLoginId, (WorkflowHistoryActionType)request.ActionType, request.Comment);
                        }
                        else
                        {
                            if (request.IsWfContextSave) context.Save();
                            WorkflowHistoryWorker.Execute(actionTaskInstance, loginUser.LoginId, AssignToLoginId, (WorkflowHistoryActionType)request.ActionType, request.Comment);
                            ActionTaskWorker.Send(actionTaskInstance.WfActionInstanceId, loginUser.LoginId);
                            SetWFView(request.InstanceId, loginUser.LoginId);
                        }

                        UnitOfWork.Commit();
                    }
                    else if (request.IsWfContextSave)
                    {
                        ActionTaskWorker.TakeOn(actionTaskInstance.WfActionInstanceId, loginUser.LoginId);
                        long WorkflowInstanceId = actionTaskInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId;
                        if (AssignToLoginId == 0)
                        {
                            AssignToLoginId = actionTaskInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId;
                        }
                        if (AssignToLoginId != assignedUser.LoginId)
                        {
                            AssignToLoginId = assignedUser.LoginId;
                        }
                        long NewAssignLoginId = CheckingWorker.GetManager(AssignToLoginId);
                        context.Parameters.AddOrChangeItem("NewAssignLoginId", NewAssignLoginId);
                        context.Parameters.AddOrChangeItem("Onay", "Yes");
                        context.Parameters.AddOrChangeItem("WfInstanceId", request.InstanceId);
                        context.Parameters.AddOrChangeItem("LastUpdatedBy", FormInformationHelper.LastUpdatedBy(loginUser, assignedUser));
                        if (!context.Parameters.ContainsKey("AssignmentLoginType")) context.Parameters.AddOrChangeItem("AssignmentLoginType", "LOGIN");
                        if (!context.Parameters.ContainsKey("AssignmentType")) context.Parameters.AddOrChangeItem("AssignmentType", "TASKINBOX");
                        if (request.IsWfContextSave) context.Save();
                        WorkflowHistoryWorker.Execute(actionTaskInstance, loginUser.LoginId, AssignToLoginId, WorkflowHistoryActionType.ACCEPTED, request.Comment);
                        ActionTaskWorker.Send(actionTaskInstance.WfActionInstanceId, loginUser.LoginId);
                        SetWFView(request.InstanceId, loginUser.LoginId);
                        UnitOfWork.Commit();
                    }
                    else
                    {
                        ActionTaskWorker.TakeOn(actionTaskInstance.WfActionInstanceId, loginUser.LoginId);
                        long WorkflowInstanceId = actionTaskInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId;
                        if (AssignToLoginId == 0)
                        {
                            AssignToLoginId = actionTaskInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId;
                        }
                        long NewAssignLoginId = CheckingWorker.GetManager(AssignToLoginId);
                        context.Parameters.AddOrChangeItem("NewAssignLoginId", NewAssignLoginId);
                        if (!context.Parameters.ContainsKey("WfInstanceId")) context.Parameters.AddOrChangeItem("WfInstanceId", request.InstanceId);
                        if (!context.Parameters.ContainsKey("Onay")) context.Parameters.AddOrChangeItem("Onay", "Yes");
                        if (!context.Parameters.ContainsKey("LastUpdatedBy")) context.Parameters.AddOrChangeItem("LastUpdatedBy", FormInformationHelper.LastUpdatedBy(loginUser, assignedUser));
                        if (!context.Parameters.ContainsKey("AssignmentLoginType")) context.Parameters.AddOrChangeItem("AssignmentLoginType", "LOGIN");
                        if (!context.Parameters.ContainsKey("AssignmentType")) context.Parameters.AddOrChangeItem("AssignmentType", "TASKINBOX");
                        ActionTaskWorker.Send(actionTaskInstance.WfActionInstanceId, loginUser.LoginId);
                        SetWFView(request.InstanceId, loginUser.LoginId);
                        UnitOfWork.Commit();

                        //Onay anındaki yorumu gönder - toplu onayda bu çalıştırılıyor
                        long YorumGrup = ConvertionHelper.ConvertValue<int>(LogicalGroupHelper.LogicalGroupIDBul("Satinalma_YorumGrup"));
                        DataTable DtYorum = LogicalGroupHelper.GetLoginPersonelList(YorumGrup);
                        Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmailAllAcceptLoginList(loginUser.LoginId, WorkflowInstanceId, DtYorum, 999, context);
                        DtYorum = null;

                    }
                }

                return Ok(ResourceUtility.GetLocalizedValue("ApprovalWorkflow", "WorkflowApprovedSuccessfully", language, "Workflow"));
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }


        [HttpPost]
        [Route("cancel")]
        public IHttpActionResult Cancel([FromBody] CancelWorkflowRequestDto request)
        {
            var language = Request.Headers.GetValues("Accept-Language").FirstOrDefault() ?? "tr";

            try
            {
                if (string.IsNullOrEmpty(request.Comment))
                {
                    return BadRequest("Comment is required.");
                }
                FlowAdminOprObject FlowAdminOprs;
                using (UnitOfWork.Start())
                {
                    var CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(request.InstanceId);

                    FlowAdminOprs = new FlowAdminOprObject(request.InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

                    #region İptal Öncesi Atanan kişilere yapılan görüntüleme yetkilerini iptal et
                    var assingToIdList = AssignToIdListLong(request.InstanceId);
                    for (int i = 0; i < assingToIdList.Count; i++)
                    {
                        Digiturk.Workflow.Entities.FWfAssignment asgnmt = new Digiturk.Workflow.Entities.FWfAssignment();
                        asgnmt.WfAssignmentId = ConvertionHelper.ConvertValue<long>(Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.MonitoringHelper.GetAssignmentId(assingToIdList[i].ToString(), request.InstanceId.ToString(), "WFVIEW", false));
                        if (asgnmt.WfAssignmentId != 0)
                        {
                            Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.MonitoringHelper.DeleteAssignment(asgnmt.WfAssignmentId);
                        }
                    }

                    #endregion İptal Öncesi Atanan kişilere yapılan görüntüleme yetkilerini iptal et

                    //if (CurrentWfIns.WfWorkflowStatusType.WfWorkflowStatusTypeCd == "COMPLETED")
                    //{
                    //    CancelEndFlow();
                    //}
                    FLogin user = WFRepository<FLogin>.GetEntity(request.LoginUserId);
                    CancelWorkflow(CurrentWfIns, user, request.Comment);

                    #region Form İptal edildikten sonra gerekli mailing yapılıyor

                    #region eski kontrol

                    //if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId != 1313 && CurrentWfIns.WfWorkflowDef.WfWorkflowDefId != 1336 && CurrentWfIns.WfWorkflowDef.WfWorkflowDefId != 2112 && CurrentWfIns.WfWorkflowDef.WfWorkflowDefId != 1429) // iade de eklendi

                    #endregion eski kontrol

                    bool isLogical = LogicalGroupHelper.IsDefExistLogicalGroup(659, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, "0");

                    if (!isLogical)
                    {
                        if (CurrentWfIns.WfCurrentState == null || CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId == null)
                        {
                            throw new Exception("Instance not found");
                        }
                    }
                    ContextObject SenderObj = new ContextObject();
                    List<FLogin> ToList = new List<FLogin>();
                    FLogin ToListItem = WFRepository<FLogin>.GetEntity(CurrentWfIns.OwnerLogin.LoginId);
                    ToList.Add(ToListItem);
                    SenderObj.Add("DelegateTo", "");
                    SenderObj.Add("Delegated", "");
                    SenderObj.Add("ActionDescription", request.Comment);
                    SenderObj.Add("ActionTo", "");
                    SendMail(0, 8, "iptal edildi", "Cancelled", false, request.LoginUserId, request.InstanceId, SenderObj, ToList);

                    #endregion Form İptal edildikten sonra gerekli mailing yapılıyor

                    #region Akış Reddedildiğinde Haberdar Edilecek Kullanıcıların listesi

                    List<long> AcceptUserList = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetLastAcceptLoginList(request.InstanceId);
                    for (int i = 0; i < AcceptUserList.Count; i++)
                    {
                        FLogin ToListItemRed = WFRepository<FLogin>.GetEntity(AcceptUserList[i]);
                        ToList.Add(ToListItemRed);
                        SenderObj.Add("DelegateTo", "");
                        SenderObj.Add("Delegated", "");
                        SenderObj.Add("ActionDescription", request.Comment);
                        SenderObj.Add("ActionTo", "");
                        SenderObj.Add("WfAssigner", WfDataHelpers.LoginNameSurnameGet(AcceptUserList[i]));
                        FLogin LoginObject = WFRepository<FLogin>.GetEntity(request.LoginUserId);
                        SendMail(0, 995, "iptal edildi", "Cancelled", false, LoginObject.LoginId, CurrentWfIns.WfWorkflowInstanceId, SenderObj, ToList);
                    }

                    #endregion Akış Reddedildiğinde Haberdar Edilecek Kullanıcıların listesi

                    #region History Kaydı Oluşturuluyor

                    WfHistoryExec(WorkflowHistoryActionType.CANCEL, request.LoginUserId, request.InstanceId, request.Comment);

                    #endregion History Kaydı Oluşturuluyor
                }

                FlowAdminOperationChecking(FlowAdminOprs, request.LoginUserId, request.InstanceId);

                return Ok(ResourceUtility.GetLocalizedValue("CancelWorkflow", "WorkflowCanceledSuccessfully", language, "Workflow"));
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }



        [HttpPost]
        [Route("finalize")]
        public IHttpActionResult Finalize([FromBody] FinalizeWorkflowRequestDto request)
        {
            var language = Request.Headers.GetValues("Accept-Language").FirstOrDefault() ?? "tr";

            try
            {
                if (string.IsNullOrEmpty(request.Comment))
                {
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
                }
                FlowAdminOprObject FlowAdminOprs;
                using (UnitOfWork.Start())
                {
                    var CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(request.InstanceId);
                    FlowAdminOprs = new FlowAdminOprObject(request.InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

                    #region Form Sonlandırılıyor

                    //FinalizeWorkflow(wfIns);

                    #endregion Form Sonlandırılıyor
                }
                FlowAdminOperationChecking(FlowAdminOprs, request.LoginUserId, request.InstanceId);


                return Ok(ResourceUtility.GetLocalizedValue("FinalizeWorkflow", "WorkflowFinalizedSuccessfully", language, "Workflow"));
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }


        [HttpPost]
        [Route("forward")]
        public IHttpActionResult Forward([FromBody] ForwardWorkflowRequestDto request)
        {
            var language = Request.Headers.GetValues("Accept-Language").FirstOrDefault() ?? "tr";

            try
            {
                if (request.ForwardUserId == 0)
                {
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Lütfen Yönlendirilecek Kullanıcıyı Seçiniz");
                }
                if (string.IsNullOrEmpty(request.Comment))
                {
                    //throw new Exception("Yorum alanı boş bırakılamaz.");
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
                }
                long AssignUserId = 0;
                long ActionUserId = request.LoginUserId;
                if (AssignToLoginIdCheck(request.InstanceId, request.LoginUserId))
                {
                    AssignUserId = request.LoginUserId;
                }
                else
                {
                    AssignUserId = AssignToId(request.InstanceId);
                }
                if (request.ForwardUserId == null || request.ForwardUserId == 0)
                {
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Lütfen Yönlendireceğiniz kullanıcıyı seçiniz");
                }
                FlowAdminOprObject FlowAdminOprs;
                using (UnitOfWork.Start())
                {
                    var CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(request.InstanceId);
                    var currentState = WFRepository<FWfStateInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfStateInstanceId);
                    var actionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(currentState.WfCurrentActionInstanceId);

                    FlowAdminOprs = new FlowAdminOprObject(request.InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

                    #region Forward İşlemi Yapılıyor
                    var LoginObject = WFRepository<FLogin>.GetEntity(request.LoginUserId);
                    ActionHelpers.ForwardWorkFlow(request.InstanceId, actionTaskInstance, LoginObject, request.ForwardUserId);

                    #endregion Forward İşlemi Yapılıyor

                    #region Yönlendirilen Kişiye Mail Atılıyor

                    var CurrentWFContext = new WFContext(CurrentWfIns);
                    ContextObject SenderObj = new ContextObject();
                    List<FLogin> ToList = new List<FLogin>();
                    var FwLogin = WFRepository<FLogin>.GetEntity(request.ForwardUserId);
                    ToList.Add(FwLogin);
                    SenderObj.Add("Action", "yönlendirildi");
                    SenderObj.Add("ActionEng", "Forwarded");
                    SenderObj.Add("ActionDescription", request.Comment);
                    //SenderObj.Add("ActionOwner",WfDataHelpers.GetCommentsLoginString(WfDataHelpers.GetLastComment(CurrentWfIns)));
                    CurrentWFContext = SendMail(request.ForwardUserId, 9, "yönlendirildi", "Forwarded", false, request.LoginUserId, request.InstanceId, SenderObj, ToList, CurrentWFContext);

                    #endregion Yönlendirilen Kişiye Mail Atılıyor

                    #region Formu Oluşturan Kişiye Mail Atılıyor

                    SenderObj.Add("DelegateTo", "");
                    SenderObj.Add("Delegated", "");
                    SenderObj.Add("WfOwner", WfDataHelpers.GetLoginNameSurname(CurrentWfIns.OwnerLogin.LoginId));
                    FLogin ToListItem = WFRepository<FLogin>.GetEntity(CurrentWfIns.OwnerLogin.LoginId);
                    ToList.Add(ToListItem);
                    SenderObj.Add("ActionDescription", request.Comment);
                    //SenderObj.Add("ActionTo", String.Format("(ilgili kişi: {0})", WfDataHelpers.GetLoginNameSurname(FwLogin)));
                    SenderObj.Add("ActionTo", String.Format("({0})", WfDataHelpers.GetLoginNameSurname(FwLogin)));

                    //CurrentWFContext.Parameters["ActionTo"] = CurrentWFContext.Parameters["ActionTo"].ToString().Replace("(ilgili kişi:", "").Replace(")", "");
                    CurrentWFContext.Parameters["ActionTo"] = SenderObj["ActionTo"].ToString().Replace("(", "").Replace(")", "");
                    CurrentWFContext.Parameters.AddOrChangeItem("ActionToPersonel", WfDataHelpers.GetLoginNameSurname(FwLogin));
                    SendMail(0, 997, "yönlendirildi", "Forwarded", false, request.LoginUserId, request.InstanceId, SenderObj, ToList);
                    CurrentWFContext.Parameters.AddOrChangeItem("ActionTo", WfDataHelpers.GetLoginNameSurname(FwLogin));
                    CurrentWFContext.Save();

                    //if (CurrentPageMode == PageMode.Modify)
                    //{
                    //    FLogin ToListItem = WFRepository<FLogin>.GetEntity(CurrentWfIns.OwnerLogin.LoginId);
                    //    ToList.Add(ToListItem);
                    //    SenderObj.Add("ActionDescription", Commend);
                    //    SenderObj.Add("ActionTo", String.Format("(ilgili kişi: {0})", WfDataHelpers.GetLoginNameSurname(FwLogin)));
                    //    SendMail(CurrentWfIns.OwnerLogin.LoginId, 8, "yönlendirildi", false);

                    //}
                    //else
                    //{
                    //}

                    #endregion Formu Oluşturan Kişiye Mail Atılıyor

                    #region History Kaydı Oluşturuluyor

                    WorkflowHistoryWorker.Execute(actionTaskInstance, ActionUserId, AssignUserId, WorkflowHistoryActionType.FORWARD, request.Comment);
                    WorkflowHistoryWorker.Execute(actionTaskInstance, AssignToId(request.InstanceId), AssignToId(request.InstanceId), WorkflowHistoryActionType.ASSIGN, "");

                    #endregion History Kaydı Oluşturuluyor
                }
                FlowAdminOperationChecking(FlowAdminOprs, request.LoginUserId, request.InstanceId);


                return Ok(ResourceUtility.GetLocalizedValue("ForwardWorkflow", "WorkflowForwardedSuccessfully", language, "Workflow"));
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }


        [HttpPost]
        [Route("resume")]
        public IHttpActionResult Resume([FromBody] ResumeWorkflowRequestDto request)
        {
            var language = Request.Headers.GetValues("Accept-Language").FirstOrDefault() ?? "tr";

            try
            {
                if (string.IsNullOrEmpty(request.Comment))
                {
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
                }
                FlowAdminOprObject FlowAdminOprs;

                using (UnitOfWork.Start())
                {
                    var CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(request.InstanceId);
                    FlowAdminOprs = new FlowAdminOprObject(request.InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

                    #region Resume İşlemi Yapılıyor
                    var LoginObject = WFRepository<FLogin>.GetEntity(request.LoginUserId);

                    var currentState = WFRepository<FWfStateInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfStateInstanceId);
                    var actionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(currentState.WfCurrentActionInstanceId);

                    ActionHelpers.ResumeRequest(CurrentWfIns, actionTaskInstance, LoginObject, request.Comment);

                    #endregion Resume İşlemi Yapılıyor

                    #region Resume İşleminden sonra bilgilendirme yapılıyor.


                    if (CurrentWfIns.WfCurrentState == null || CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId == null)
                    {
                        throw new Exception("Instance not found");
                    }
                    ContextObject SenderObj = new ContextObject();
                    List<FLogin> ToList = new List<FLogin>();
                    FLogin ToListItem = WFRepository<FLogin>.GetEntity(CurrentWfIns.OwnerLogin.LoginId);
                    ToList.Add(ToListItem);
                    SenderObj.Add("DelegateTo", "");
                    SenderObj.Add("Delegated", "");
                    SenderObj.Add("ActionDescription", request.Comment);
                    SenderObj.Add("ActionTo", "");
                    SendMail(0, 8, "devam ettirildi", "Resumed", false, request.LoginUserId, request.InstanceId, SenderObj, ToList);

                    #endregion Resume İşleminden sonra bilgilendirme yapılıyor.

                    #region History Kaydı Oluşturuluyor

                    WfHistoryExec(WorkflowHistoryActionType.RESUME, request.LoginUserId, request.InstanceId, request.Comment);

                    #endregion History Kaydı Oluşturuluyor
                }
                FlowAdminOperationChecking(FlowAdminOprs, request.LoginUserId, request.InstanceId);


                return Ok(ResourceUtility.GetLocalizedValue("ResumeWorkflow", "WorkflowResumedSuccessfully", language, "Workflow"));
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }



        [HttpPost]
        [Route("rollback")]
        public IHttpActionResult Rollback([FromBody] RollbackWorkflowRequestDto request)
        {
            var language = Request.Headers.GetValues("Accept-Language").FirstOrDefault() ?? "tr";

            try
            {
                #region Yorum Girilmiş mi? ve Geri Alma İşlemini Yapacak kişi Gerçekten Son State de mi kontrolü Yapılıyor
                var currentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(request.InstanceId);
                var currentState = WFRepository<FWfStateInstance>.GetEntity(currentWfIns.WfCurrentState.WfStateInstanceId);
                var actionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(currentState.WfCurrentActionInstanceId);

                long RollBackMailTemplate = 999;
                List<FLogin> LastAssignList = AssignToIdList(request.InstanceId);
                long LastStateId = currentState.WfStateInstanceId;
                if (string.IsNullOrEmpty(request.Comment))
                {
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
                }

                if (!IsLastSendTaskLoginUser(request.LoginUserId, request.InstanceId) && !IsFlowAdmin(request.LoginUserId, request.InstanceId) && !LastSendTaskLoginIdList(request.InstanceId).Contains(request.LoginUserId.ToString()))
                {
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Geri alma işlemi son işlem yapan kişi yada akış yöneticisi tarafından yapılır.");
                }
                FlowAdminOprObject FlowAdminOprs;

                #endregion Yorum Girilmiş mi? ve Geri Alma İşlemini Yapacak kişi Gerçekten Son State de mi kontrolü Yapılıyor

                using (UnitOfWork.Start())
                {
                    var CurrentWFContext = new WFContext(currentWfIns);

                    #region Geri Almadan Önceki Action Task Instance Kaydediliyor

                    FWfActionTaskInstance PreviusActionTaskIns = actionTaskInstance;
                    FlowAdminOprs = new FlowAdminOprObject(request.InstanceId, currentWfIns.WfWorkflowDef.WfWorkflowDefId);

                    #endregion Geri Almadan Önceki Action Task Instance Kaydediliyor

                    #region Helper Üzerindeki RollBack Fonksiyonu Çağrılır

                    FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(request.InstanceId);
                    WFContext WfCtx = new WFContext(WfIns);

                    FWfStateInstance WfStateInstance = WFRepository<FWfStateInstance>.GetEntity(WfIns.WfCurrentState.WfStateInstanceId);
                    FWfActionTaskInstance CurrentActionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(WfStateInstance.WfCurrentActionInstanceId);

                    ActionHelpers.RollbackWorkflow(WfIns, WfCtx, CurrentActionTaskInstance.WfActionInstanceId);

                    #endregion Helper Üzerindeki RollBack Fonksiyonu Çağrılır

                    #region Geri Alma Öncesi Atanmış olan kişilere

                    for (int i = 0; i < LastAssignList.Count; i++)
                    {

                        Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmailAction(LastAssignList[i].LoginId, request.LoginUserId, "Geri Alındı", "Received Back", request.Comment, RollBackMailTemplate, request.InstanceId, CurrentWFContext);
                        Digiturk.Workflow.Entities.FWfAssignment asgnmt = new Digiturk.Workflow.Entities.FWfAssignment();
                        asgnmt.WfAssignmentId = ConvertionHelper.ConvertValue<long>(Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.MonitoringHelper.GetAssignmentId(LastAssignList[i].LoginId.ToString(), request.InstanceId.ToString(), "WFVIEW", false));
                        if (asgnmt.WfAssignmentId != 0)
                        {
                            Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.MonitoringHelper.DeleteAssignment(asgnmt.WfAssignmentId);
                        }

                        #region History Kaydı Oluşturuluyor

                        if (i == 0)
                        {
                            WfHistoryExec(PreviusActionTaskIns, WorkflowHistoryActionType.ROLLBACK, request.LoginUserId, request.InstanceId, request.Comment);
                        }
                        else
                        {
                            WorkflowHistoryWorker.DeleteHistoryAssignLog(request.InstanceId, LastStateId, LastAssignList[i].LoginId);
                        }

                        #endregion History Kaydı Oluşturuluyor
                    }

                    #endregion Geri Alma Öncesi Atanmış olan kişilere

                    #region Akışı Oluşturan Kişiye Mail Atılır

                    Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmailAction(currentWfIns.OwnerLogin.LoginId, request.LoginUserId, "Geri Alındı", "Received Back", request.Comment, RollBackMailTemplate, request.InstanceId, CurrentWFContext);

                    #endregion Akışı Oluşturan Kişiye Mail Atılır

                    #region Assign İşlemine Ait History Kaydı Oluşturuluyor ve Yeniden Atanan Kullanıcılara Mail Atılır

                    foreach (var AssigmentItem in AssignToIdList(request.InstanceId))
                    {
                        RollBackMailTemplate = 994;
                        Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.Execute(actionTaskInstance, AssigmentItem.LoginId, AssigmentItem.LoginId, WorkflowHistoryActionType.ASSIGN, "");
                        Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmailAction(AssigmentItem.LoginId, request.LoginUserId, "Geri Alındı", "Received Back", request.Comment, RollBackMailTemplate, request.InstanceId, CurrentWFContext);
                    }

                    #endregion Assign İşlemine Ait History Kaydı Oluşturuluyor ve Yeniden Atanan Kullanıcılara Mail Atılır

                    #region Assign İşleminde İşlemi yapan kişinin görüntüleme yetkisi yoksa yetki verilir

                    ActionHelpers.SetWFView(request.InstanceId, request.LoginUserId);

                    #endregion Assign İşleminde İşlemi yapan kişinin görüntüleme yetkisi yoksa yetki verilir
                }
                FlowAdminOperationChecking(FlowAdminOprs, request.LoginUserId, request.InstanceId);

                return Ok(ResourceUtility.GetLocalizedValue("RollbackWorkflow", "WorkflowRollbackedSuccessfully", language, "Workflow"));
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }




        [HttpPost]
        [Route("reject")]
        public IHttpActionResult Reject([FromBody] RejectWorkflowRequestDto request)
        {
            var language = Request.Headers.GetValues("Accept-Language").FirstOrDefault() ?? "tr";

            try
            {
                #region Yorum Girilmiş mi? ve Geri Alma İşlemini Yapacak kişi Gerçekten Son State de mi kontrolü Yapılıyor

                using (UnitOfWork.Start(true))
                {
                    var currentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(request.InstanceId);
                    var currentState = WFRepository<FWfStateInstance>.GetEntity(currentWfIns.WfCurrentState.WfStateInstanceId);
                    var fWfActionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(currentState.WfCurrentActionInstanceId);
                    var LoginUser = WFRepository<FLogin>.GetEntity(request.LoginUserId);
                    var assignedUser = AssignedUser(request.InstanceId, request.LoginUserId);
                    ActionTaskWorker.TakeOn(fWfActionTaskInstance.WfActionInstanceId, request.LoginUserId);
                    WFContext Context = new WFContext(currentWfIns);
                    Context.Parameters.AddOrChangeItem("WfInstanceId", request.InstanceId);
                    Context.Parameters.AddOrChangeItem("Onay", "No");
                    Context.Parameters.AddOrChangeItem("LastUpdatedBy", FormInformationHelper.LastUpdatedBy(LoginUser, assignedUser));
                    Context.Parameters.AddOrChangeItem("Description", request.Comment);
                    Context.Save();
                    ActionTaskWorker.Send(fWfActionTaskInstance.WfActionInstanceId, LoginUser.LoginId);
                    WorkflowHistoryWorker.Execute(fWfActionTaskInstance, LoginUser.LoginId, assignedUser.LoginId, WorkflowHistoryActionType.REJECTED, request.Comment);
                    SetWFView(request.InstanceId, LoginUser.LoginId);
                    UnitOfWork.Commit();
                }

                #endregion

                return Ok(ResourceUtility.GetLocalizedValue("RejectWorkflow", "WorkflowRejectedSuccessfully", language, "Workflow"));
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }


        [HttpPost]
        [Route("send-back")]
        public IHttpActionResult SendBack([FromBody] SendBackWorkflowRequestDto request)
        {
            var language = Request.Headers.GetValues("Accept-Language").FirstOrDefault() ?? "tr";

            try
            {
                if (string.IsNullOrEmpty(request.Comment))
                {
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
                }
                var CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(request.InstanceId);
                var CurrentState = WFRepository<FWfStateInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfStateInstanceId);
                var CurrentActionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentState.WfCurrentActionInstanceId);
                using (UnitOfWork.Start(true))
                {
                    var CurrentWFContext = new WFContext(CurrentWfIns);
                    ActionTaskWorker.TakeOn(CurrentActionTaskInstance.WfActionInstanceId, request.LoginUserId);
                    long WorkflowInstanceId = CurrentActionTaskInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId;
                    long AssignToLoginId = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginId(WorkflowInstanceId);
                    if (AssignToLoginId == 0)
                    {
                        AssignToLoginId = CurrentWfIns.OwnerLogin.LoginId;
                    }
                    long NewAssignLoginId = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetManager(AssignToLoginId);
                    CurrentWFContext.Parameters.AddOrChangeItem("NewAssignLoginId", NewAssignLoginId);
                    if (!CurrentWFContext.Parameters.ContainsKey("WfInstanceId")) CurrentWFContext.Parameters.AddOrChangeItem("WfInstanceId", request.InstanceId);
                    CurrentWFContext.Parameters.AddOrChangeItem("Onay", "SendBack");
                    //if (!CurrentWFContext.Parameters.ContainsKey("LastUpdatedBy")) CurrentWFContext.Parameters.AddOrChangeItem("LastUpdatedBy", FormInformationHelper.LastUpdatedBy(( (AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, AssignedUser.LoginId));
                    if (!CurrentWFContext.Parameters.ContainsKey("AssignmentLoginType")) CurrentWFContext.Parameters.AddOrChangeItem("AssignmentLoginType", "LOGIN");
                    if (!CurrentWFContext.Parameters.ContainsKey("AssignmentType")) CurrentWFContext.Parameters.AddOrChangeItem("AssignmentType", "TASKINBOX");
                    CurrentWFContext.Save();
                    ActionTaskWorker.Send(CurrentActionTaskInstance.WfActionInstanceId, request.LoginUserId);
                    WfHistoryExec(WorkflowHistoryActionType.SENDBACK, request.LoginUserId, request.InstanceId, request.Comment);
                    UnitOfWork.Commit();
                }

                return Ok(ResourceUtility.GetLocalizedValue("SendBackWorkflow", "WorkflowSendBackedSuccessfully", language, "Workflow"));
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }



        [HttpPost]
        [Route("send-request-to-comment")]
        public IHttpActionResult SendRequestToComment([FromBody] SendRequestToCommentWorkflowRequestDto request)
        {
            var language = Request.Headers.GetValues("Accept-Language").FirstOrDefault() ?? "tr";

            try
            {
                if (string.IsNullOrEmpty(request.Comment))
                {
                    throw new Exception("Yorum alanı boş bırakılamaz.");
                }

                FlowAdminOprObject FlowAdminOprs;
                using (UnitOfWork.Start())
                {
                    #region Son ActionTaskInstanceÜretilir

                    FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(request.InstanceId);
                    FWfActionTaskInstance taskIns = WfDataHelpers.GetLastActionTaskInstance(WfIns.WfCurrentState.WfStateInstanceFWfActionInstanceList.ToList().OrderBy(t => t.WfActionInstanceId));
                    FlowAdminOprs = new FlowAdminOprObject(request.InstanceId, WfIns.WfWorkflowDef.WfWorkflowDefId);

                    #endregion Son ActionTaskInstanceÜretilir

                    #region Yorumu Cevaplama İşlemi Yapılır

                    //ActionHelpers.SendRequestToCommentWorkFlow(WfIns, taskIns, ( (AuthenticationResult)Session[SessionUserVariable]).LoginObject, CurrentPageMode, Commend);

                    #endregion Yorumu Cevaplama İşlemi Yapılır

                    #region History Kaydı Oluşturuluyor

                    //if (ActionTaskWorker.GetCommentInboxList(( (AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId).Contains(CurrentActionTaskInstance))
                    if (FormInformationHelper.IsWFCommentInbox(request.LoginUserId, request.InstanceId) || AssignToLoginIdCheck(request.InstanceId, request.LoginUserId))
                    {
                        WfHistoryExec(WorkflowHistoryActionType.COMMENTED, request.LoginUserId, request.InstanceId, request.Comment);
                        if (FormInformationHelper.IsWFCommentInbox(request.LoginUserId, request.InstanceId))
                        {
                            #region Yoruma Gönderen Kişiye Yorumunu Giriyor.

                            ContextObject SenderObj = new ContextObject();
                            List<FLogin> ToList = new List<FLogin>();
                            long SendToCommentLoginId = FormInformationHelper.SendToCommentLogin(request.LoginUserId, request.InstanceId);
                            var FwLogin = WFRepository<FLogin>.GetEntity(SendToCommentLoginId);
                            ToList.Add(FwLogin);
                            SenderObj.Add("Action", "Yorum Talebi Cevaplandı");
                            SenderObj.Add("ActionEng", "Comment Request Answered");
                            SenderObj.Add("ActionDescription", request.Comment);
                            SendMail(SendToCommentLoginId, 9, "Yorum Talebi Cevaplandı", "Comment Request Answered", false, request.LoginUserId, request.InstanceId, SenderObj, ToList);

                            #endregion Yoruma Gönderen Kişiye Yorumunu Giriyor.
                        }
                    }
                    else
                    {
                        WfHistoryExec(WorkflowHistoryActionType.ADDTOCOMMEND, request.LoginUserId, request.InstanceId, request.Comment);

                        //yorumu ekle adımında gönder 
                        long YorumGrup = ConvertionHelper.ConvertValue<int>(LogicalGroupHelper.LogicalGroupIDBul("Satinalma_YorumGrup"));
                        DataTable DtYorum = LogicalGroupHelper.GetLoginPersonelList(YorumGrup);
                        FLogin FLogin = WFRepository<FLogin>.GetEntity(request.LoginUserId);

                        Digiturk.Workflow.DigiFlow.Framework.Action.ContextList ContextList = new Digiturk.Workflow.DigiFlow.Framework.Action.ContextList();
                        ContextList.AddOrChangeItems("Yorum", request.Comment);
                        ContextList.AddOrChangeItems("WorkFlowInsId", WfIns.WfWorkflowInstanceId.ToString());
                        ContextList.AddOrChangeItems("OnayDurum", "");
                        ContextList.AddOrChangeItems("IslemYapan", WfDataHelpers.GetLoginNameSurname(request.LoginUserId));


                        Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmailAllAcceptLoginList(FLogin, WfIns.WfWorkflowInstanceId, DtYorum, 1007, ContextList);
                        DtYorum = null;
                    }

                    #endregion History Kaydı Oluşturuluyor
                }
                FlowAdminOperationChecking(FlowAdminOprs, request.LoginUserId, request.InstanceId);


                return Ok(ResourceUtility.GetLocalizedValue("SendRequestToCommentWorkflow", "WorkflowSendRequestToCommentSuccessfully", language, "Workflow"));
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }



        [HttpPost]
        [Route("file-upload")]
        public IHttpActionResult FileUpload([FromBody] FileUploadWorkflowRequestDto request)
        {
            var language = Request.Headers.GetValues("Accept-Language").FirstOrDefault() ?? "tr";

            try
            {
                if (string.IsNullOrEmpty(request.Comment))
                {
                    throw new Exception("Yorum alanı boş bırakılamaz.");
                }

                FlowAdminOprObject FlowAdminOprs;
                using (UnitOfWork.Start())
                {
                    #region Son ActionTaskInstanceÜretilir

                    FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(request.InstanceId);
                    FWfActionTaskInstance taskIns = WfDataHelpers.GetLastActionTaskInstance(WfIns.WfCurrentState.WfStateInstanceFWfActionInstanceList.ToList().OrderBy(t => t.WfActionInstanceId));
                    FlowAdminOprs = new FlowAdminOprObject(request.InstanceId, WfIns.WfWorkflowDef.WfWorkflowDefId);

                    #endregion Son ActionTaskInstance Üretilir

                    #region History Kaydı Oluşturuluyor

                    string filesHtml = string.Join("", request.Files.Select(file =>
                    {
                        string fileName = file.Split('/').Last(); // Get the last part after splitting by '/'
                        return $@" - <a target='_blank' href='{file}'>{fileName}</a><br/>";
                    }));

                    string commentWithFiles = $@"{request.Comment}<br/>{filesHtml}";

                    WfHistoryExec(WorkflowHistoryActionType.FILEUPLOADED, request.LoginUserId, request.InstanceId, commentWithFiles);

                    //yorumu ekle adımında gönder 
                    DataTable DtYorum = new DataTable();
                    FLogin FLogin = WFRepository<FLogin>.GetEntity(request.LoginUserId);

                    Digiturk.Workflow.DigiFlow.Framework.Action.ContextList ContextList = new Digiturk.Workflow.DigiFlow.Framework.Action.ContextList();
                    ContextList.AddOrChangeItems("Yorum", request.Comment);
                    ContextList.AddOrChangeItems("Files", string.Join(Environment.NewLine, request.Files.Select(file => $@"<a href='{file}'>{file}</a>")));
                    ContextList.AddOrChangeItems("WorkFlowInsId", WfIns.WfWorkflowInstanceId.ToString());
                    ContextList.AddOrChangeItems("OnayDurum", "");
                    ContextList.AddOrChangeItems("IslemYapan", WfDataHelpers.GetLoginNameSurname(request.LoginUserId));


                    //Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmailAllAcceptLoginList(FLogin, WfIns.WfWorkflowInstanceId, DtYorum, 1008, ContextList);
                    DtYorum = null;

                    #endregion History Kaydı Oluşturuluyor
                }
                FlowAdminOperationChecking(FlowAdminOprs, request.LoginUserId, request.InstanceId);


                return Ok(ResourceUtility.GetLocalizedValue("FileUploadWorkflow", "WorkflowFileUploadedSuccessfully", language, "Workflow"));
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }



        [HttpPost]
        [Route("send-to-comment")]
        public IHttpActionResult SendToComment([FromBody] SendToCommentWorkflowRequestDto request)
        {
            var language = Request.Headers.GetValues("Accept-Language").FirstOrDefault() ?? "tr";

            try
            {
                if (string.IsNullOrEmpty(request.Comment))
                {
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
                }
                if (request.SendToCommentUserId == null || request.SendToCommentUserId == 0)
                {
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Lütfen Yoruma Gönderilecek Kullanıcıyı Seçiniz");
                }
                FlowAdminOprObject FlowAdminOprs;
                using (UnitOfWork.Start())
                {
                    var CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(request.InstanceId);
                    var currentState = WFRepository<FWfStateInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfStateInstanceId);
                    var actionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(currentState.WfCurrentActionInstanceId);

                    FlowAdminOprs = new FlowAdminOprObject(request.InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

                    #region Yoruma Gönderme İşlemi Yapılır

                    ActionHelpers.SendtoCommendWorkFlow(request.InstanceId, actionTaskInstance, request.SendToCommentUserId, request.LoginUserId);

                    #endregion Yoruma Gönderme İşlemi Yapılır

                    #region Yoruma Gönderilen Kişiye Mail Atılıyor

                    ContextObject SenderObj = new ContextObject();
                    List<FLogin> ToList = new List<FLogin>();

                    var FwLogin = WFRepository<FLogin>.GetEntity(request.SendToCommentUserId);
                    ToList.Add(FwLogin);
                    SenderObj.Add("Action", "yoruma gönderildi");
                    SenderObj.Add("ActionEng", "Forwarded for remarks");
                    SenderObj.Add("ActionDescription", request.Comment);
                    //Delegesine de mail atılır.
                    long WfDelegationId = WorkflowRecursiveDelegationHelper.GetActiveDelegateWithRecursive(request.SendToCommentUserId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, DateTime.Now);
                    string DelegationNameSurName = "";
                    string DelegationByNameSurName = "";
                    if (WfDelegationId > 0)
                    {

                        DelegationNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(WfDelegationId);
                        DelegationByNameSurName = DelegationNameSurName + "(" + WflowDataHelpers.LoginNameSurnameGet(request.SendToCommentUserId) + " Delegesi)";

                        //var FwLogin2 = WFRepository<FLogin>.GetEntity(WfDelegationId);
                        //ToList.Add(FwLogin2);

                        SenderObj.Add("ActionTo", String.Format(DelegationByNameSurName));
                    }
                    SendMail(request.SendToCommentUserId, 9, "yoruma gönderildi", "Forwarded for remarks", false, request.LoginUserId, request.InstanceId, SenderObj, ToList);

                    #endregion Yoruma Gönderilen Kişiye Mail Atılıyor

                    #region Formu Oluşturan Kişiye Bilgilendirme Maili Atılıyor

                    if (CurrentWfIns.WfCurrentState == null || CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId == null)
                    {
                        throw new Exception("Instance not found");
                    }
                    ToList.Add(CurrentWfIns.OwnerLogin);
                    SenderObj.Add("ActionDescription", request.Comment);
                    //SenderObj.Add("ActionTo", String.Format("(ilgili kişi: {0})", WfDataHelpers.GetLoginNameSurname(FwLogin)));
                    SenderObj.Add("ActionTo", String.Format("({0})", WfDataHelpers.GetLoginNameSurname(FwLogin)));
                    SendMail(0, 997, "yoruma gönderildi", "Forwarded for remarks", false, request.LoginUserId, request.InstanceId, SenderObj, ToList);

                    //if (CurrentPageMode == PageMode.Modify)
                    //{
                    //}
                    //else
                    //{
                    //    ToList.Add(CurrentWfIns.OwnerLogin);
                    //    SenderObj.Add("ActionDescription", Commend);
                    //    SenderObj.Add("ActionTo", String.Format("(ilgili kişi: {0})", WfDataHelpers.GetLoginNameSurname(SendtoCommendUserId)));
                    //    SendMail(0, 997, "yoruma gönderildi", FwLogin, false);
                    //}

                    #endregion Formu Oluşturan Kişiye Bilgilendirme Maili Atılıyor

                    #region History Kaydı Oluşturuluyor

                    WfHistoryExec(WorkflowHistoryActionType.SENDTOCOMMENT, request.LoginUserId, request.InstanceId, request.Comment);

                    #endregion History Kaydı Oluşturuluyor
                }
                FlowAdminOperationChecking(FlowAdminOprs, request.LoginUserId, request.InstanceId);


                return Ok(ResourceUtility.GetLocalizedValue("SendToCommentWorkflow", "WorkflowSendToCommentSuccessfully", language, "Workflow"));
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }



        [HttpPost]
        [Route("suspend")]
        public IHttpActionResult Suspend([FromBody] SuspendWorkflowRequestDto request)
        {
            var language = Request.Headers.GetValues("Accept-Language").FirstOrDefault() ?? "tr";

            try
            {
                if (string.IsNullOrEmpty(request.Comment))
                {
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
                }
                FlowAdminOprObject FlowAdminOprs;
                using (UnitOfWork.Start())
                {
                    var CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(request.InstanceId);
                    var currentState = WFRepository<FWfStateInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfStateInstanceId);
                    var actionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(currentState.WfCurrentActionInstanceId);
                    FlowAdminOprs = new FlowAdminOprObject(request.InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

                    #region Form Askıya Alınıyor.

                    DateTime delegationStartTime = DateTime.MinValue;
                    var LoginObject = WFRepository<FLogin>.GetEntity(request.LoginUserId);

                    ActionHelpers.SuspendRequest(CurrentWfIns, request.SuspendDate, actionTaskInstance, LoginObject, request.Comment);

                    #endregion Form Askıya Alınıyor.

                    #region Form Askıya Alındıktan Sonra Bilgilendirme Yapılıyor.

                    if (CurrentWfIns.WfCurrentState == null || CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId == null)
                    {
                       throw new Exception("Instance not found");
                    }
                    ContextObject SenderObj = new ContextObject();
                    List<FLogin> ToList = new List<FLogin>();
                    FLogin ToListItem = WFRepository<FLogin>.GetEntity(CurrentWfIns.OwnerLogin.LoginId);
                    ToList.Add(ToListItem);
                    SenderObj.Add("ActionDescription", request.Comment);
                    SenderObj.Add("ActionTo", "");
                    SenderObj.Add("SuspendDate", Digiturk.Workflow.Digiflow.CoreHelpers.DateTimeHelper.TarihFormatla(request.SuspendDate) + " 00:00:00");
                    SendMail(0, 996, "askıya alındı", "Suspended", false, request.LoginUserId, request.InstanceId, SenderObj, ToList);

                    #endregion Form Askıya Alındıktan Sonra Bilgilendirme Yapılıyor.

                    #region History Kaydı Oluşturuluyor

                    WfHistoryExec(WorkflowHistoryActionType.SUSPEND, request.LoginUserId, request.InstanceId, request.Comment);

                    #endregion History Kaydı Oluşturuluyor
                }
                FlowAdminOperationChecking(FlowAdminOprs, request.LoginUserId, request.InstanceId);


                return Ok(ResourceUtility.GetLocalizedValue("SuspendWorkflow", "WorkflowSuspendedSuccessfully", language, "Workflow"));
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }





        private static void CancelWorkflow(FWfWorkflowInstance wfIns, FLogin LoginObject, string Commend)
        {
            using (UnitOfWork.Start())
            {
                DataTable jumToStateWorkFlows = GetJumToStateWorkFlows(wfIns.WfWorkflowInstanceId);
                if (wfIns.WfWorkflowStatusType.WfWorkflowStatusTypeCd != "STARTED")
                {
                    FWfWorkflowInstance entity = WFRepository<FWfWorkflowInstance>.GetEntity(wfIns.WfWorkflowInstanceId);
                    FWfWorkflowStatusType entity2 = WFRepository<FWfWorkflowStatusType>.GetEntity("CANCELED");
                    entity.WfWorkflowStatusType = entity2;
                    WFRepository<FWfWorkflowInstance>.SaveEntity(entity);
                    entity = null;
                    entity2 = null;
                }
                else
                {
                    WorkflowInstanceWorker.Cancel(wfIns.WfWorkflowInstanceId);
                }

                for (int i = 0; i < jumToStateWorkFlows.Rows.Count; i++)
                {
                    long num = ConvertionHelper.ConvertValue<long>(jumToStateWorkFlows.Rows[i]["JumpToStateInstanceId"]);
                    FWfWorkflowInstance entity3 = WFRepository<FWfWorkflowInstance>.GetEntity(num);
                    FWfStateInstance entity4 = WFRepository<FWfStateInstance>.GetEntity(entity3.WfCurrentState.WfStateInstanceId);
                    FWfActionTaskInstance entity5 = WFRepository<FWfActionTaskInstance>.GetEntity(entity4.WfCurrentActionInstanceId);
                    CancelWorkflow(num, LoginObject.LoginId);
                    WorkflowHistoryWorker.Execute(entity5, LoginObject.LoginId, LoginObject.LoginId, WorkflowHistoryActionType.CANCEL, "Akış İptal Edildiği İçin Akış Atlatma Talebi de İptal Edilmiştir");
                    entity3 = null;
                    entity4 = null;
                    entity5 = null;
                }

                SetWFView(wfIns.WfWorkflowInstanceId, LoginObject.LoginId);
                jumToStateWorkFlows = null;
            }
        }

        private static void CancelWorkflow(long WfWorkflowInstanceId, long UserId)
        {
            using (UnitOfWork.Start())
            {
                FWfWorkflowInstance entity = WFRepository<FWfWorkflowInstance>.GetEntity(WfWorkflowInstanceId);
                long wfWorkflowDefId = entity.WfWorkflowDef.WfWorkflowDefId;
                long wfStateDefId = entity.WfCurrentState.WfStateDef.WfStateDefId;
                AuthoInfo authoInfo = new AuthoInfo(wfWorkflowDefId, wfStateDefId, UserId);
                if (authoInfo.CanCancel)
                {
                    WorkflowInstanceWorker.Cancel(WfWorkflowInstanceId);
                    SetWFView(WfWorkflowInstanceId, UserId);
                    entity = null;
                    authoInfo = null;
                    return;
                }

                throw ExceptionHelper.ValidationError("Bu Akışı bu aşamada iptal edemezsiniz");
            }
        }

        private static DataTable GetJumToStateWorkFlows(long WorkFlowInstanceId)
        {
            string SQL = @"Select
           FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID as MasterInstanceId,
           JUMPTOSTATEINSTANCE.WF_WORKFLOW_INSTANCE_ID as JumpToStateInstanceId
           from FRAMEWORK.F_WF_WORKFLOW_INSTANCE
           Left Join DT_WORKFLOW.WF_DF_JUMP_TO_STATE_REQUEST On DT_WORKFLOW.WF_DF_JUMP_TO_STATE_REQUEST.FLOW_INSTANCE_ID=FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID
           Left Join  FRAMEWORK.F_WF_WORKFLOW_INSTANCE JumpToStateInstance On JUMPTOSTATEINSTANCE.ENTITY_REF_ID=DT_WORKFLOW.WF_DF_JUMP_TO_STATE_REQUEST.JUMP_TO_STATE_REQUEST_ID
           Where
           JUMPTOSTATEINSTANCE.WF_WORKFLOW_DEF_ID=1340
           and JUMPTOSTATEINSTANCE.WF_WORKFLOW_STATUS_TYPE_CD='STARTED'
           and FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID=:WorkFlowInstanceId
           Order By FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID Asc";
            SQL = SQL.Replace(":WorkFlowInstanceId", WorkFlowInstanceId.ToString());
            return Digiturk.Workflow.Digiflow.DataAccessLayer.ModelWorking.GetDataTable("DefaultConnection", SQL);
        }





        /// <summary>
        /// Standart Form Üzerinden mail gönderme fonksiyonudur
        /// </summary>
        /// <param name="ActionToLoginId"></param>
        /// <param name="MailTemplateId"></param>
        /// <param name="Action"></param>
        /// <param name="IsSave"></param>
        private WFContext SendMail(long ActionToLoginId, long MailTemplateId, string Action, bool IsSave, long LoginId, long InstanceId, ContextObject SenderObj, List<FLogin> ToList, WFContext CurrentWFContext = null)
        {
            var CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
            if(CurrentWFContext== null)
                CurrentWFContext = new WFContext(CurrentWfIns);
            if (ActionToLoginId > 0)
            {
                FLogin ActionTo = WFRepository<FLogin>.GetEntity(ActionToLoginId);
                CurrentWFContext.Parameters.AddOrChangeItem("ActionTo", WfDataHelpers.GetLoginNameSurname(ActionTo.LoginId));
                //toList.Add(flogin); // talep sahibi
            }
            foreach (var item in SenderObj)
            {
                CurrentWFContext.Parameters.AddOrChangeItem(item.Key, item.Value);
            }
            CurrentWFContext.Parameters.AddOrChangeItem("Action", Action);

            CurrentWFContext.Parameters.AddOrChangeItem("ActionDate", DateTime.Now.ToString());
            CurrentWFContext.Parameters.AddOrChangeItem("ActionOwner", WfDataHelpers.GetLoginNameSurname(LoginId));
            //if(WorkFlowHelpers.WfDataHelpers.GetLastComment(CurrentWfIns)!=null)CurrentWFContext.Parameters.AddOrChangeItem("ActionOwner", WorkFlowHelpers.WfDataHelpers.GetCommentsLoginString(WorkFlowHelpers.WfDataHelpers.GetLastComment(CurrentWfIns)));
            FWfWorkflowDef WfDef = WFRepository<FWfWorkflowDef>.GetEntity(CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
            CurrentWFContext.Parameters.AddOrChangeItem("WorkflowName", WfDef.Name);
            CurrentWFContext.Parameters.AddOrChangeItem("WfOwner", WfDataHelpers.GetLoginNameSurname(CurrentWfIns.OwnerLogin.LoginId));
            /// Burda Sadece Yes mi set edicek Ya Reddedilirse nolucak????
            if (!CurrentWFContext.Parameters.ContainsKey("Onay")) CurrentWFContext.Parameters.AddOrChangeItem("Onay", "Yes");
            var LoginObject = WFRepository<FLogin>.GetEntity(LoginId);
            if (!CurrentWFContext.Parameters.ContainsKey("LastUpdatedBy")) CurrentWFContext.Parameters.AddOrChangeItem("LastUpdatedBy", FormInformationHelper.LastUpdatedBy(LoginObject, AssignedUser(InstanceId, LoginId)));
            if (!CurrentWFContext.Parameters.ContainsKey("AssignmentLoginType")) CurrentWFContext.Parameters.AddOrChangeItem("AssignmentLoginType", "LOGIN");
            if (!CurrentWFContext.Parameters.ContainsKey("AssignmentType")) CurrentWFContext.Parameters.AddOrChangeItem("AssignmentType", "TASKINBOX");
            //FWfActionTaskInstance ActionTaskIns = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentActionTaskInstance.WfActionInstanceId);
            //Dictionary<string, string> MyDic = new Dictionary<string, string>();
            FWfActionTaskInstance CurrentActionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value);
            if (CurrentActionTaskInstance == null)
            {
                //FWfActionTaskInstance
                FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                FWfStateInstance StateInstance = WFRepository<FWfStateInstance>.GetEntity(WfIns.WfWorkflowInstanceFWfStateInstanceList[WfIns.WfWorkflowInstanceFWfStateInstanceList.Count - 1].WfStateInstanceId);
                FWfActionInstance ActionInstance = WFRepository<FWfActionInstance>.GetEntity(StateInstance.WfStateInstanceFWfActionInstanceList[StateInstance.WfStateInstanceFWfActionInstanceList.Count - 1].WfActionInstanceId);

                MailHelper.SendMail(MailTemplateId, ActionInstance, CurrentWFContext, ToList, new List<FLogin>());
                Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfIns.WfWorkflowInstanceId);
            }
            else
            {
                MailHelper.SendMail(MailTemplateId, CurrentActionTaskInstance, CurrentWFContext, ToList, new List<FLogin>());
                Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(CurrentActionTaskInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId);
            }

            //MailHelper.SendEmailDirect(MailTemplateId, CurrentActionTaskInstance, CurrentWFContext,MyDic,new List<string>(),new List<string>());
            //MailHelper.SendEmailDirect(
            if (IsSave) CurrentWFContext.Save();
            SenderObj.Clear();
            ToList.Clear();
            return CurrentWFContext;
        }

        /// <summary>
        /// Standart Form Üzerinden mail gönderme fonksiyonudur
        /// </summary>
        /// <param name="ActionToLoginId"></param>
        /// <param name="MailTemplateId"></param>
        /// <param name="Action"></param>
        /// <param name="IsSave"></param>
        private WFContext SendMail(long ActionToLoginId, long MailTemplateId, string Action, string ActionEng, bool IsSave, long LoginId, long InstanceId, ContextObject SenderObj, List<FLogin> ToList, WFContext CurrentWFContext = null)
        {
            var CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
            if(CurrentWFContext == null)
                CurrentWFContext = new WFContext(CurrentWfIns);
            if (ActionToLoginId > 0)
            {
                FLogin ActionTo = WFRepository<FLogin>.GetEntity(ActionToLoginId);
                CurrentWFContext.Parameters.AddOrChangeItem("ActionTo", WfDataHelpers.GetLoginNameSurname(ActionTo.LoginId));
                //toList.Add(flogin); // talep sahibi
            }
            foreach (var item in SenderObj)
            {
                CurrentWFContext.Parameters.AddOrChangeItem(item.Key, item.Value);
            }
            CurrentWFContext.Parameters.AddOrChangeItem("Action", Action);
            CurrentWFContext.Parameters.AddOrChangeItem("ActionEng", ActionEng);

            CurrentWFContext.Parameters.AddOrChangeItem("ActionDate", DateTime.Now.ToString());
            CurrentWFContext.Parameters.AddOrChangeItem("ActionOwner", WfDataHelpers.GetLoginNameSurname(LoginId));
            //if(WorkFlowHelpers.WfDataHelpers.GetLastComment(CurrentWfIns)!=null)CurrentWFContext.Parameters.AddOrChangeItem("ActionOwner", WorkFlowHelpers.WfDataHelpers.GetCommentsLoginString(WorkFlowHelpers.WfDataHelpers.GetLastComment(CurrentWfIns)));
            FWfWorkflowDef WfDef = WFRepository<FWfWorkflowDef>.GetEntity(CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
            CurrentWFContext.Parameters.AddOrChangeItem("WorkflowName", WfDef.Name);
            CurrentWFContext.Parameters.AddOrChangeItem("WfOwner", WfDataHelpers.GetLoginNameSurname(CurrentWfIns.OwnerLogin.LoginId));
            /// Burda Sadece Yes mi set edicek Ya Reddedilirse nolucak????
            if (!CurrentWFContext.Parameters.ContainsKey("Onay")) CurrentWFContext.Parameters.AddOrChangeItem("Onay", "Yes");
            FLogin UserInfo = WFRepository<FLogin>.GetEntity(LoginId);
            if (!CurrentWFContext.Parameters.ContainsKey("LastUpdatedBy")) CurrentWFContext.Parameters.AddOrChangeItem("LastUpdatedBy", FormInformationHelper.LastUpdatedBy(UserInfo, AssignedUser(InstanceId, LoginId)));
            if (!CurrentWFContext.Parameters.ContainsKey("AssignmentLoginType")) CurrentWFContext.Parameters.AddOrChangeItem("AssignmentLoginType", "LOGIN");
            if (!CurrentWFContext.Parameters.ContainsKey("AssignmentType")) CurrentWFContext.Parameters.AddOrChangeItem("AssignmentType", "TASKINBOX");
            //FWfActionTaskInstance ActionTaskIns = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentActionTaskInstance.WfActionInstanceId);
            //Dictionary<string, string> MyDic = new Dictionary<string, string>();
            FWfActionTaskInstance CurrentActionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value);
            if (CurrentActionTaskInstance == null)
            {
                //FWfActionTaskInstance
                FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                FWfStateInstance StateInstance = WFRepository<FWfStateInstance>.GetEntity(WfIns.WfWorkflowInstanceFWfStateInstanceList[WfIns.WfWorkflowInstanceFWfStateInstanceList.Count - 1].WfStateInstanceId);
                FWfActionInstance ActionInstance = WFRepository<FWfActionInstance>.GetEntity(StateInstance.WfStateInstanceFWfActionInstanceList[StateInstance.WfStateInstanceFWfActionInstanceList.Count - 1].WfActionInstanceId);

                MailHelper.SendMail(MailTemplateId, ActionInstance, CurrentWFContext, ToList, new List<FLogin>());
                Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfIns.WfWorkflowInstanceId);
            }
            else
            {
                MailHelper.SendMail(MailTemplateId, CurrentActionTaskInstance, CurrentWFContext, ToList, new List<FLogin>());
                Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(CurrentActionTaskInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId);
            }

            //MailHelper.SendEmailDirect(MailTemplateId, CurrentActionTaskInstance, CurrentWFContext,MyDic,new List<string>(),new List<string>());
            //MailHelper.SendEmailDirect(
            if (IsSave) CurrentWFContext.Save();
            SenderObj.Clear();
            ToList.Clear();
            return CurrentWFContext;
        }

        /// <summary>
        /// Standart Mail Gönderme Fonksiyonudur. ActionOwner Parametresini alır
        /// </summary>
        /// <param name="ActionToLoginId"></param>
        /// <param name="MailTemplateId"></param>
        /// <param name="Action"></param>
        /// <param name="IsSave"></param>
        private void SendMail(long ActionToLoginId, long MailTemplateId, string Action, FLogin ActionOwner, bool IsSave, long LoginId, long InstanceId, ContextObject SenderObj, List<FLogin> ToList, WFContext CurrentWFContext = null)
        {
            var CurrentWfInstance = WFRepository<FWfWorkflowInstance>.GetEntity(ActionOwner.LoginId);
            if(CurrentWFContext == null)
                CurrentWFContext = new WFContext(CurrentWfInstance);
            CurrentWFContext.Parameters.AddOrChangeItem("ActionOwner", ActionOwner);
            SendMail(ActionToLoginId, MailTemplateId, Action, IsSave, LoginId, InstanceId, SenderObj, ToList, CurrentWFContext);
        }

        /// <summary>
        /// Standart Mail Gönderme Fonksiyonudur. ActionOwner Parametresini alır
        /// </summary>
        /// <param name="ActionToLoginId"></param>
        /// <param name="MailTemplateId"></param>
        /// <param name="Action"></param>
        /// <param name="IsSave"></param>
        private void SendMail(long ActionToLoginId, long MailTemplateId, string Action, string ActionEng, FLogin ActionOwner, bool IsSave, long LoginId, long InstanceId, ContextObject SenderObj, List<FLogin> ToList, WFContext CurrentWFContext = null)
        {
            var CurrentWfInstance = WFRepository<FWfWorkflowInstance>.GetEntity(ActionOwner.LoginId);
            if (CurrentWFContext == null)
                CurrentWFContext = new WFContext(CurrentWfInstance);
            CurrentWFContext.Parameters.AddOrChangeItem("ActionOwner", ActionOwner);
            SendMail(ActionToLoginId, MailTemplateId, Action, ActionEng, IsSave, LoginId, InstanceId, SenderObj, ToList, CurrentWFContext);
        }




        private static void SetWFView(long WfInstanceId, long LoginId)
        {
            try
            {
                Digiturk.Workflow.Digiflow.AssignmentBase.AssignmentBase.Assign(LoginId, "LOGIN", WfInstanceId, "WFVIEW");
            }
            catch (Exception ex)
            {

            }
        }

        private IEntity DeserializeEntity(string entityJson, long workFlowDefinitionId)
        {
            if (string.IsNullOrWhiteSpace(entityJson))
            {
                return null;
            }

            var jsonDocument = JsonDocument.Parse(entityJson);
            var root = jsonDocument.RootElement;
            var dynamicObject = new System.Dynamic.ExpandoObject() as IDictionary<string, object>;

            dynamicObject["Type"] = workFlowDefinitionId;

            foreach (var property in root.EnumerateObject())
            {
                dynamicObject[property.Name] = property.Value.Clone();
            }

            var newJsonString = JsonSerializer.Serialize(dynamicObject);
            return JsonSerializer.Deserialize<IEntity>(newJsonString, _jsonOptions);
        }

        private List<IDetailEntity> DeserializeDetails(string detailJson)
        {
            if (!string.IsNullOrWhiteSpace(detailJson))
            {
                return JsonSerializer.Deserialize<List<IDetailEntity>>(detailJson, _jsonOptions);
            }
            return new List<IDetailEntity>();
        }

        private bool HasDetails(params List<IDetailEntity>[] detailLists)
        {
            return detailLists.Any(list => list != null && list.Count > 0);
        }

        private void CreateWorkflow(IEntity entity, long workFlowDefinitionId, long loginId)
        {
            ActionHelpers.CreateWorkFlow(entity, workFlowDefinitionId, loginId);
        }

        private void CreateWorkflow(IEntity entity, List<IDetailEntity> detail, List<IDetailEntity> detail2, List<IDetailEntity> detail3, List<IDetailEntity> detail4, long workFlowDefinitionId, long loginId)
        {
            if (detail4 != null && detail4.Count > 0)
                ActionHelpers.CreateWorkFlow(entity, detail, detail2, detail3, detail4, workFlowDefinitionId, loginId);
            else if (detail3 != null && detail3.Count > 0)
                ActionHelpers.CreateWorkFlow(entity, detail, detail2, detail3, workFlowDefinitionId, loginId);
            else if (detail2 != null && detail2.Count > 0)
                ActionHelpers.CreateWorkFlow(entity, detail, detail2, workFlowDefinitionId, loginId);
            else if (detail != null && detail.Count > 0)
                ActionHelpers.CreateWorkFlow(entity, detail, workFlowDefinitionId, loginId);
            else
                ActionHelpers.CreateWorkFlow(entity, workFlowDefinitionId, loginId);
        }


        private bool FormDelegation(long InstanceId, long LoginId)
        {
            var idList = AssignToIdListLong(InstanceId);
            if (InstanceId == 0) return false;
            foreach (var item in idList)
            {
                if (WorkFlowInformationHelper.DelegationCheck(InstanceId, item, LoginId, DateTime.Now))
                {
                    return true;
                }
            }
            return false;
        }

        private List<long> AssignToIdListLong(long InstanceId)
        {
            return Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetAssignLoginList(InstanceId);
        }

        private bool AssignToLoginIdCheck(long InstanceId, long LoginId)
        {
            return AssignToIdListLong(InstanceId).Contains(LoginId);


        }
        private FWfActionTaskInstance CurrentActionTaskInstance(long InstanceId)
        {
            FWfWorkflowInstance CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
            FWfStateDef CurrentStateDef = WFRepository<FWfStateDef>.GetEntity(CurrentWfIns.WfCurrentState.WfStateDef.WfStateDefId);
            using (UnitOfWork.Start())
            {
                try
                {
                    if (CurrentWfIns == null)
                    {
                        return null;
                    }
                    if (CurrentWfIns != null && CurrentWfIns.WfCurrentState != null)
                    {
                        return WFRepository<FWfActionTaskInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value);
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception ex)
                {
                    if (CurrentWfIns.WfCurrentState != null && CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId != null && CurrentStateDef.WfStateType.WfStateTypeCd == "MIDDLE")
                    {
                        return WFRepository<FWfActionTaskInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value);
                    }
                    System.Exception exs = ex;
                }
            }
            return null;
        }

        private FLogin FlowAdmin(long LoginId, long InstanceId)
        {
            FLogin sonuc = null;
            if (IsFlowAdmin(LoginId, InstanceId))
            {
                sonuc = (FLogin)WFRepository<FLogin>.GetEntity(LoginId);
            }

            return sonuc;
        }

        /// <summary>
        ///  Login olan Kişinin Akış Admini olup olmadığını Tespit eder.
        /// </summary>
        private bool IsFlowAdmin(long LoginId, long WfWorkflowInstanceId)
        {
            var CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(WfWorkflowInstanceId);
            bool sonuc = false;
            if (CurrentWfIns != null)
            {
                sonuc = WorkflowAdminHelpers.IsWfAdmin(LoginId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
            }

            return sonuc;
        }

        private void FlowAdminOperationChecking(FlowAdminOprObject Objs, long FlowAdminUserId, long InstanceId)
        {
            //CurrentActionTaskInstance.WfStateInstance.WfStateDef.ToStateDefFWfTransitionDefList[0].Condition.ToString

            long RealUserId = Objs.RealUserId;
            long DelegateUserId = Objs.DelegateUserId;
            DateTime StartDate = Objs.OperationStartTime;
            DateTime EndDate = DateTime.Now;
            var flowAdmin = FlowAdmin(RealUserId, InstanceId);
            if (IsFlowAdmin(RealUserId, InstanceId) && flowAdmin.LoginId != RealUserId)
            {
                var CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                if (DelegateUserId > 0)
                {
                    /// Delegasyonu Parçalıyoruz
                    Digiturk.Workflow.Digiflow.WebCore.WorkFlowEntites.DelegationObject dlgReq = new Digiturk.Workflow.Digiflow.WebCore.WorkFlowEntites.DelegationObject(DelegateUserId);
                    WorkflowDelegationHelper.EndDelegation(RealUserId, DelegateUserId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, StartDate);
                    WorkflowDelegationHelper.StartDelegation(
                        RealUserId,
                        FlowAdminUserId,
                        CurrentWfIns.WfWorkflowDef.WfWorkflowDefId,
                        "Akış Admini Delegasyonu",
                        StartDate,
                        EndDate);
                    WorkflowDelegationHelper.StartDelegation(
                        RealUserId,
                        DelegateUserId,
                        CurrentWfIns.WfWorkflowDef.WfWorkflowDefId,
                        dlgReq.DelegationComment + "(Akış Admini işlemi Sonrası devam ettirildi.)",
                        EndDate,
                        dlgReq.EndTime);
                }
                else
                {
                    /// Akış Admini için Delegasyon üretiyoruz
                    WorkflowDelegationHelper.StartDelegation(
                        RealUserId,
                        FlowAdminUserId,
                        CurrentWfIns.WfWorkflowDef.WfWorkflowDefId,
                        "Akış Admini Delegasyonu oluşturuldu",
                        StartDate,
                        EndDate);
                }
            }
        }


        private FLogin AssignedUser(long InstanceId, long LoginId)
        {
            FWfWorkflowInstance wfInstance = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
            FWfActionTaskInstance currentActionTaskInstance = CurrentActionTaskInstance(InstanceId);
            if (FormDelegation(InstanceId, LoginId))
            {
                /// Form Delege edilmişse
                if (AssignToIdListLong(InstanceId).Count > 1)
                {
                    FLogin LoginObject = WFRepository<FLogin>.GetEntity(LoginId);
                    // Birden Fazla Kişiye Delege edilmişse bu kişi kimin delegesi ise burdan delege edilenin Id si dönmeli.
                    //WorkflowDelegationHelper.(AssignToId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, DateTime.Now);
                    foreach (var item in AssignToIdListLong(InstanceId))
                    {
                        if (WorkFlowInformationHelper.DelegationCheck(InstanceId, item, LoginObject.LoginId, DateTime.Now))
                        {
                            FLogin AssignedUsr = WFRepository<FLogin>.GetEntity(item);
                            return AssignedUsr;
                        }
                    }
                    if (AssignToLoginIdCheck(InstanceId, LoginObject.LoginId))
                    {
                        return WFRepository<FLogin>.GetEntity(LoginObject.LoginId);
                    }
                    else
                    {
                        return FormInformationHelper.GetAssignedUser(currentActionTaskInstance);
                    }
                }
                else
                {
                    return FormInformationHelper.GetAssignedUser(currentActionTaskInstance);
                }
            }
            else
            {
                if (AssignToLoginIdCheck(InstanceId, LoginId))
                {
                    return WFRepository<FLogin>.GetEntity(LoginId);
                }
                else
                {
                    return FormInformationHelper.GetAssignedUser(currentActionTaskInstance);
                }
            }
        }
        private void WfHistoryExec(FWfActionTaskInstance PreviusActionTaskIns, WorkflowHistoryActionType wfHistory, long LoginId, long InstanceId, string Comment)
        {
            #region History Kaydı Atılıyor
            long assignToId = AssignToId(InstanceId);
            WorkflowHistoryWorker.Execute(PreviusActionTaskIns, LoginId, assignToId, wfHistory, Comment);

            #endregion History Kaydı Atılıyor
        }

        /// <summary>
        /// History Logunu yazdığımız fonksiyondur.
        /// </summary>
        /// <param name="wfHistory"></param>
        private void WfHistoryExec(WorkflowHistoryActionType wfHistory, long LoginId, long InstanceId, string Comment)
        {
            #region History Kaydı Atılıyor
            long assignToId = AssignToId(InstanceId);
            var currentActionTaskInstance = CurrentActionTaskInstance(InstanceId);
            if (currentActionTaskInstance == null)
            {
                FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                FWfStateInstance StateInstance = WFRepository<FWfStateInstance>.GetEntity(WfIns.WfWorkflowInstanceFWfStateInstanceList[WfIns.WfWorkflowInstanceFWfStateInstanceList.Count - 1].WfStateInstanceId);
                FWfActionInstance ActionInstance = WFRepository<FWfActionInstance>.GetEntity(StateInstance.WfStateInstanceFWfActionInstanceList[StateInstance.WfStateInstanceFWfActionInstanceList.Count - 1].WfActionInstanceId);
                WorkflowHistoryWorker.Execute(ActionInstance, LoginId, assignToId, wfHistory, Comment);
            }
            else
            {
                WorkflowHistoryWorker.Execute(currentActionTaskInstance, LoginId, assignToId, wfHistory, Comment);
            }

            #endregion History Kaydı Atılıyor
        }

        /// <summary>
        /// History Logunu ActionLoginId belirterek Yazdığımız Fonksiyon
        /// </summary>
        /// <param name="wfHistory"></param>
        /// <param name="ActionLoginId"></param>
        private void WfHistoryExec(long InstanceId, string Comment, WorkflowHistoryActionType wfHistory, long ActionLoginId)
        {
            var currentActionTaskInstance = CurrentActionTaskInstance(InstanceId);
            WorkflowHistoryWorker.Execute(currentActionTaskInstance, ActionLoginId, ActionLoginId, wfHistory, Comment);
        }

        private long AssignToId(long InstanceId)
        {

            return Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginId(InstanceId);

        }
        public List<FLogin> AssignToIdList(long InstanceId)
        {
            return Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginList(InstanceId);
        }

        public bool IsLastSendTaskLoginUser(long LoginId, long InstanceId)
        {
            return LastSendTaskLoginId(InstanceId) == LoginId;
        }
        public long LastSendTaskLoginId(long InstanceId)
        {
            return CheckingWorker.GetLastActionToLoginId(InstanceId);
        }
        public string[] LastSendTaskLoginIdList(long InstanceId)
        {
            return CheckingWorker.GetLastActionToLoginIdList(InstanceId);
        }
    }
}