﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.5.0.2" targetFramework="net481" />
  <package id="bootstrap" version="5.3.6" targetFramework="net481" />
  <package id="jQuery" version="3.7.1" targetFramework="net481" />
  <package id="Microsoft.AspNet.Cors" version="5.3.0" targetFramework="net481" />
  <package id="Microsoft.AspNet.Mvc" version="5.3.0" targetFramework="net481" />
  <package id="Microsoft.AspNet.Razor" version="3.3.0" targetFramework="net481" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net481" />
  <package id="Microsoft.AspNet.WebApi" version="5.3.0" targetFramework="net481" />
  <package id="Microsoft.AspNet.WebApi.Client" version="6.0.0" targetFramework="net481" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.3.0" targetFramework="net481" />
  <package id="Microsoft.AspNet.WebApi.Cors" version="5.3.0" targetFramework="net481" />
  <package id="Microsoft.AspNet.WebApi.HelpPage" version="5.3.0" targetFramework="net481" />
  <package id="Microsoft.AspNet.WebApi.SelfHost" version="5.3.0" targetFramework="net481" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.3.0" targetFramework="net481" />
  <package id="Microsoft.AspNet.WebPages" version="3.3.0" targetFramework="net481" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.6" targetFramework="net481" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="4.1.0" targetFramework="net481" />
  <package id="Microsoft.Web.Infrastructure" version="2.0.0" targetFramework="net481" />
  <package id="Modernizr" version="2.8.3" targetFramework="net481" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net481" />
  <package id="Newtonsoft.Json.Bson" version="1.0.3" targetFramework="net481" />
  <package id="NLog" version="6.0.0" targetFramework="net481" />
  <package id="Swashbuckle" version="5.6.0" targetFramework="net481" />
  <package id="Swashbuckle.Core" version="5.6.0" targetFramework="net481" />
  <package id="System.Buffers" version="4.6.1" targetFramework="net481" />
  <package id="System.IO.Pipelines" version="9.0.6" targetFramework="net481" />
  <package id="System.Memory" version="4.6.3" targetFramework="net481" />
  <package id="System.Numerics.Vectors" version="4.6.1" targetFramework="net481" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.1.2" targetFramework="net481" />
  <package id="System.Text.Encodings.Web" version="9.0.6" targetFramework="net481" />
  <package id="System.Text.Json" version="9.0.6" targetFramework="net481" />
  <package id="System.Threading.Tasks.Extensions" version="4.6.3" targetFramework="net481" />
  <package id="System.ValueTuple" version="4.6.1" targetFramework="net481" />
  <package id="WebActivatorEx" version="2.2.0" targetFramework="net481" />
  <package id="WebGrease" version="1.6.0" targetFramework="net481" />
</packages>