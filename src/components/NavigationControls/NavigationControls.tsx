import React from 'react'
import { useNavigationHistory } from '../../hooks/useNavigationHistory'

interface NavigationControlsProps {
  openScreenById: (screen: string, extra: any) => void
  className?: string // Optional class name for positioning
}

export const NavigationControls: React.FC<NavigationControlsProps> = ({
  openScreenById,
  className = 'fixed bottom-4 right-4', // Default positioning
}) => {
  const { goBack, goForward, canGoBack, canGoForward } = useNavigationHistory(openScreenById)

  return (
    <div className={`flex gap-2 ${className}`}>
      <button
        onClick={goBack}
        disabled={!canGoBack()}
        className={`p-2 rounded ${
          canGoBack() ? 'bg-blue-500 text-white hover:bg-blue-600 transition-colors' : 'bg-gray-300 text-gray-500 cursor-not-allowed'
        }`}
        aria-label="Go back"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M19 12H5M12 19l-7-7 7-7" />
        </svg>
      </button>

      <button
        onClick={goForward}
        disabled={!canGoForward()}
        className={`p-2 rounded ${
          canGoForward() ? 'bg-blue-500 text-white hover:bg-blue-600 transition-colors' : 'bg-gray-300 text-gray-500 cursor-not-allowed'
        }`}
        aria-label="Go forward"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M5 12h14M12 5l7 7-7 7" />
        </svg>
      </button>
    </div>
  )
}
