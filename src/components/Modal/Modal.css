/* Modal.css */
:root {
  --modal-background-color: var(--digi-color-background-surface, #ffffff);
  --modal-border-radius: var(--digi-border-radius-large, 8px);
  --modal-shadow: var(--digi-shadow-xlarge, 0 12px 32px rgba(0, 0, 0, 0.2));
  --modal-padding: var(--digi-spacing-large, 24px);
  --modal-header-padding: var(--digi-spacing-medium, 16px) var(--digi-spacing-large, 24px);
  --modal-content-padding: 0 var(--digi-spacing-large, 24px) var(--digi-spacing-large, 24px);
  --modal-actions-padding: var(--digi-spacing-medium, 16px) var(--digi-spacing-large, 24px);

  --modal-title-font-size: var(--digi-font-size-xlarge, 1.5rem);
  --modal-title-font-weight: var(--digi-font-weight-semibold, 600);
  --modal-title-color: var(--digi-color-text-primary, #333333);
  --modal-title-background: transparent; /* Or a subtle background: var(--digi-color-background-neutral-subtle, #f8f9fa) */
  --modal-title-border-bottom: 1px solid var(--digi-color-border-neutral-soft, #dee2e6);

  --modal-actions-background: transparent; /* Or a subtle background: var(--digi-color-background-neutral-subtle, #f8f9fa) */
  --modal-actions-border-top: 1px solid var(--digi-color-border-neutral-soft, #dee2e6);

  --modal-button-spacing: var(--digi-spacing-small, 8px);
  --modal-progress-bar-height: 4px;
}

/* Styles for the WDialog component itself via its root class if possible, or the .modal class */
.modal .MuiDialog-paper {
  border-radius: var(--modal-border-radius) !important;
  box-shadow: var(--modal-shadow) !important;
  background-color: var(--modal-background-color) !important;
  overflow: visible; /* Allow progress bar to be absolute to this */
}

.modal-title.MuiDialogTitle-root {
  font-size: var(--modal-title-font-size) !important;
  font-weight: var(--modal-title-font-weight) !important;
  color: var(--modal-title-color) !important;
  background: var(--modal-title-background) !important;
  padding: var(--modal-header-padding) !important;
  border-bottom: var(--modal-title-border-bottom);
  /* Remove old gradient and border */
}

.modal-content.MuiDialogContent-root {
  padding: var(--modal-content-padding) !important;
  color: var(--digi-color-text-secondary, #555555);
  font-size: var(--digi-font-size-medium, 1rem);
  line-height: var(--digi-line-height-medium, 1.6);
}

.modal-actions.MuiDialogActions-root {
  padding: var(--modal-actions-padding) !important;
  background: var(--modal-actions-background) !important;
  border-top: var(--modal-actions-border-top);
  /* Remove old gradient and border */
}

.modal-actions .MuiGrid-container {
  /* This class can be used for specific WGrid container adjustments if needed in the future. */
  /* For example: justify-content: space-between; if buttons needed to be spread out. */
  /* For now, no specific overrides are needed as WGrid props handle alignment. */
}

.modal-button.MuiButton-root {
  /* Using general button styles from a global CSS or DigiButton specific styles is preferred */
  /* This is a fallback or override if needed */
  font-weight: var(--digi-font-weight-medium, 500);
  border-radius: var(--digi-border-radius-medium, 4px) !important;
  padding: var(--digi-spacing-xsmall, 6px) var(--digi-spacing-medium, 16px) !important;
  text-transform: none !important; /* Overriding Material-UI's default uppercase */
}

/* Specific styles for confirm and cancel buttons if they differ beyond color */
.modal-button.MuiButton-outlined {
  border-color: var(--digi-color-border-neutral-strong, #cccccc) !important;
  color: var(--digi-color-text-primary, #333333) !important;
}

.modal-button.MuiButton-outlined:hover {
  background-color: var(--digi-color-background-hover, #f5f5f5) !important;
  border-color: var(--digi-color-border-neutral-hover, #bbbbbb) !important;
}

/* Assuming confirm button might have a primary color, handled by WButton props ideally */
/* Example if you wanted to force a primary color for confirm via CSS: */
/*
.modal-button-confirm.MuiButton-outlined {
  border-color: var(--digi-color-primary-main, #007bff) !important;
  color: var(--digi-color-primary-main, #007bff) !important;
}
.modal-button-confirm.MuiButton-outlined:hover {
  background-color: var(--digi-color-primary-light-transparent, rgba(0, 123, 255, 0.08)) !important;
}
*/

.modal-button .MuiCircularProgress-root {
  color: inherit !important; /* Spinner color matches button text */
}

/* Progress bar styling */
.modal-progress-bar.MuiLinearProgress-root {
  position: absolute !important; /* Relative to .MuiDialog-paper */
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%; /* Ensure it spans the full width of the dialog paper */
  height: var(--modal-progress-bar-height) !important;
  border-bottom-left-radius: var(--modal-border-radius); /* Match dialog rounding */
  border-bottom-right-radius: var(--modal-border-radius);
  overflow: hidden; /* Ensure progress bar respects rounded corners */
  background-color: var(--digi-color-background-neutral-soft, #e9ecef); /* Background for the track */
}

.modal-progress-bar .MuiLinearProgress-bar {
  background-color: var(--digi-color-primary-main, #007bff) !important;
  border-radius: 0 !important; /* Reset Mui default if it interferes with container rounding */
}

/* Remove old form-row styles as they are not part of a generic modal */
/*
.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.form-row label {
  width: 150px !important;
  font-weight: bold !important;
}

.form-row p {
  margin: 0;
  flex: 1;
}
*/
