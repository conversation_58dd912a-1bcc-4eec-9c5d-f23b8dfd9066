// Modal.tsx
import React, { useState, useCallback, useEffect } from 'react'
import { WD<PERSON>og, WDialogTitle, WDialogContent, WDialogActions, WButton, WGrid, WCircularProgress, WLinearProgress } from 'wface'
import './Modal.css'
import { useTranslation } from 'react-i18next'

interface ModalProps {
  open: boolean
  onClose: () => void
  onConfirm: () => Promise<void>
  title?: string
  children?: React.ReactNode
  loading?: boolean // External loading state
  confirmButtonText?: string
  cancelButtonText?: string
  confirmButtonColor?: 'inherit' | 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning'
  cancelButtonColor?: 'inherit' | 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning'
}

const Modal: React.FC<ModalProps> = ({
  open,
  onClose,
  onConfirm,
  title,
  children,
  loading = false,
  confirmButtonText,
  cancelButtonText,
  confirmButtonColor = 'primary',
  cancelButtonColor = 'inherit',
}) => {
  const { t } = useTranslation(['common'])
  const [isInternalConfirming, setIsInternalConfirming] = useState(false)
  const [progress, setProgress] = useState(0)

  const combinedLoading = loading || isInternalConfirming

  useEffect(() => {
    // Reset internal state if the modal is closed externally or loading prop changes
    if (!open || loading) {
      setIsInternalConfirming(false)
      setProgress(0)
    }
  }, [open, loading])

  const handleConfirm = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (combinedLoading) {
      return
    }

    setIsInternalConfirming(true)
    setProgress(0)
    let timer: NodeJS.Timeout | undefined = undefined

    try {
      // Simulate progress for onConfirm
      timer = setInterval(() => {
        setProgress((oldProgress) => {
          if (oldProgress >= 95) {
            if (timer) clearInterval(timer)
            return 95
          }
          const increment = Math.random() * 10 + 5
          return Math.min(oldProgress + increment, 95)
        })
      }, 150)

      await onConfirm()

      if (timer) clearInterval(timer)
      setProgress(100)

      // Optional: Close modal on successful confirm after a short delay to show 100% progress
      // setTimeout(() => {
      //   onClose();
      //   setTimeout(() => { // Delay reset after close animation
      //     setIsInternalConfirming(false);
      //     setProgress(0);
      //   }, 300);
      // }, 300);
    } catch (error) {
      console.error('[Modal] Confirm error:', error)
      if (timer) clearInterval(timer)
      setProgress(0)
    } finally {
      // If not auto-closing, reset state after a delay
      // This timeout helps show the 100% progress briefly or error state
      setTimeout(() => {
        setIsInternalConfirming(false)
        // Only reset progress if not externally controlled and modal still open
        if (!loading && open) {
          setProgress(0)
        }
      }, 700)
    }
  }

  const handleCancel = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (combinedLoading) {
      return
    }
    onClose()
  }

  const handleInternalClose = useCallback(
    (_event: {}, reason: string) => {
      if (reason === 'backdropClick' || reason === 'escapeKeyDown') {
        if (combinedLoading) {
          return
        }
        onClose()
      }
    },
    [combinedLoading, onClose]
  )

  return (
    <WDialog open={open} onClose={handleInternalClose} maxWidth="sm" fullWidth className="modal" aria-labelledby="modal-title">
      <WDialogTitle id="modal-title" className="modal-title">
        {title || t('confirmation')}
      </WDialogTitle>

      <WDialogContent className="modal-content">{children}</WDialogContent>

      <WDialogActions className="modal-actions">
        <WGrid container justifyContent="flex-end" spacing={1}>
          <WGrid item>
            <WButton
              variant="outlined"
              onClick={handleCancel}
              color={cancelButtonColor}
              disabled={combinedLoading}
              className="modal-button modal-button-cancel"
            >
              {cancelButtonText || t('cancel')}
            </WButton>
          </WGrid>

          <WGrid item>
            <WButton
              variant="contained"
              onClick={handleConfirm}
              color={confirmButtonColor}
              disabled={combinedLoading}
              startIcon={combinedLoading ? <WCircularProgress size={20} color="inherit" /> : null}
              className="modal-button modal-button-confirm"
            >
              {confirmButtonText || t('confirm')}
            </WButton>
          </WGrid>
        </WGrid>
      </WDialogActions>

      {combinedLoading && (
        <WLinearProgress
          variant={isInternalConfirming && !loading ? 'determinate' : 'indeterminate'}
          value={isInternalConfirming && !loading ? progress : undefined}
          className="modal-progress-bar"
        />
      )}
    </WDialog>
  )
}

export default Modal
