import { FC, useState } from 'react'
import { WDialog, WDialogContent, WDialogTitle, WDialogActions, WButton } from 'wface'
const MessagePopUp: FC<{
  message?: string
  title?: string
  closePopup: () => void
}> = ({ message, title, closePopup }) => {
  const [show, setShow] = useState(true)
  const onClick = () => {
    setShow(false)
    setTimeout(() => {
      closePopup()
      setShow(true)
    }, 200)
  }
  return (
    <WDialog id="alertDialog" open={Boolean(message) && show}>
      <WDialogTitle>{title}</WDialogTitle>
      <WDialogContent style={{ textAlign: 'center' }}>{message}</WDialogContent>
      <WDialogActions>
        <WButton id="okBtn" variant="outlined" color="primary" onClick={onClick}>
          Tamam
        </WButton>
      </WDialogActions>
    </WDialog>
  )
}
export default MessagePopUp
