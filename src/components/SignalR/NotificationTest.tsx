// src/App.tsx
import { useState, useEffect, useCallback } from 'react'
import * as signalR from '@microsoft/signalr'

function NotificationTest() {
  const [connection, setConnection] = useState<signalR.HubConnection | null>(null)
  const [messages, setMessages] = useState<{ user: string; message: string }[]>([])
  const [message, setMessage] = useState('')
  const [user, setUser] = useState('')
  const [status, setStatus] = useState('Disconnected')

  useEffect(() => {
    // Create the connection
    const newConnection = new signalR.HubConnectionBuilder()
      .withUrl('http://digiflowtest.digiturk.com.tr/api/notificationHub', {
        withCredentials: true, // Important for CORS
        skipNegotiation: true, // Skip negotiation
        transport: signalR.HttpTransportType.WebSockets, // Force WebSockets
      })
      .withAutomaticReconnect()
      .configureLogging(signalR.LogLevel.Information)
      .build()

    setConnection(newConnection)

    // Set up message handler
    newConnection.on('ReceiveMessage', (user: string, message: string) => {
      setMessages((prev) => [...prev, { user, message }])
    })

    // Start the connection
    const startConnection = async () => {
      try {
        setStatus('Connecting...')
        await newConnection.start()
        setStatus('Connected')
        console.log('Connected to hub')
      } catch (error) {
        setStatus('Failed to connect')
        console.error('Failed to connect:', error)
        // Retry in 5 seconds
        setTimeout(startConnection, 5000)
      }
    }

    startConnection()

    // Clean up on unmount
    return () => {
      if (newConnection) {
        newConnection.stop()
      }
    }
  }, [])

  const sendMessage = useCallback(async () => {
    if (connection && message && user) {
      try {
        await connection.invoke('SendMessage', user, message)
        setMessage('') // Clear message input after sending
      } catch (error) {
        console.error('Error sending message:', error)
      }
    }
  }, [connection, message, user])

  return (
    <div className="container mx-auto p-4 max-w-md">
      <br />
      <br />
      <br />
      <br />
      <br />
      <div className="mb-4">
        <div className={`px-4 py-2 rounded ${status === 'Connected' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
          Status: {status}
        </div>
      </div>

      <div className="space-y-4 mb-4">
        <input type="text" value={user} onChange={(e) => setUser(e.target.value)} placeholder="Your name" className="w-full p-2 border rounded" />

        <div className="flex space-x-2">
          <input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Type a message"
            className="flex-1 p-2 border rounded"
          />
          <button
            onClick={sendMessage}
            disabled={!connection || status !== 'Connected'}
            className={`px-4 py-2 rounded ${!connection || status !== 'Connected' ? 'bg-gray-300' : 'bg-blue-500 text-white hover:bg-blue-600'}`}
          >
            Send
          </button>
        </div>
      </div>

      <div className="border rounded p-4 h-96 overflow-y-auto">
        {messages.map((msg, index) => (
          <div key={index} className="mb-2">
            <span className="font-bold">{msg.user}: </span>
            <span>{msg.message}</span>
          </div>
        ))}
      </div>
    </div>
  )
}

export default NotificationTest
