import React from 'react'
import { Check, Clock, AlertCircle } from 'lucide-react'

// Simple className utility
const cn = (...classes: (string | undefined | null | false)[]) => {
  return classes.filter(Boolean).join(' ')
}

export interface WorkflowStep {
  id: string
  title: string
  description?: string
  status: 'completed' | 'active' | 'pending' | 'error'
  timestamp?: string
}

export interface WorkflowProgressProps {
  steps: WorkflowStep[]
  className?: string
  orientation?: 'horizontal' | 'vertical'
  size?: 'sm' | 'md' | 'lg'
}

const WorkflowProgress: React.FC<WorkflowProgressProps> = ({
  steps,
  className,
  orientation = 'horizontal',
  size = 'md'
}) => {
  const getStepIcon = (status: WorkflowStep['status']) => {
    const iconSize = size === 'sm' ? 16 : size === 'md' ? 20 : 24

    switch (status) {
      case 'completed':
        return <Check size={iconSize} className="text-white" />
      case 'active':
        return <Clock size={iconSize} className="text-white animate-pulse" />
      case 'error':
        return <AlertCircle size={iconSize} className="text-white" />
      default:
        return <div className={cn('rounded-full bg-white',
          size === 'sm' ? 'w-2 h-2' : size === 'md' ? 'w-3 h-3' : 'w-4 h-4'
        )} />
    }
  }

  const getStepColors = (status: WorkflowStep['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500 border-green-500 text-green-700'
      case 'active':
        return 'bg-purple-600 border-purple-600 text-purple-700'
      case 'error':
        return 'bg-red-500 border-red-500 text-red-700'
      default:
        return 'bg-gray-300 border-gray-300 text-gray-500'
    }
  }

  const stepSizes = {
    sm: {
      icon: 'w-6 h-6',
      text: 'text-xs',
      gap: 'gap-2'
    },
    md: {
      icon: 'w-8 h-8',
      text: 'text-sm',
      gap: 'gap-3'
    },
    lg: {
      icon: 'w-10 h-10',
      text: 'text-base',
      gap: 'gap-4'
    }
  }

  if (orientation === 'vertical') {
    return (
      <div className={cn('flex flex-col', stepSizes[size].gap, className)}>
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-start gap-4">
            {/* Step Icon */}
            <div className="flex flex-col items-center">
              <div className={cn(
                'rounded-full border-2 flex items-center justify-center',
                stepSizes[size].icon,
                getStepColors(step.status)
              )}>
                {getStepIcon(step.status)}
              </div>
              {/* Connecting Line */}
              {index < steps.length - 1 && (
                <div className={cn(
                  'w-0.5 flex-1 mt-2',
                  step.status === 'completed' ? 'bg-green-300' : 'bg-gray-200'
                )} style={{ minHeight: '24px' }} />
              )}
            </div>

            {/* Step Content */}
            <div className="flex-1 pb-6">
              <h4 className={cn(
                'font-medium',
                stepSizes[size].text,
                step.status === 'active' ? 'text-purple-900' :
                  step.status === 'completed' ? 'text-green-900' :
                    step.status === 'error' ? 'text-red-900' : 'text-gray-700'
              )}>
                {step.title}
              </h4>
              {step.description && (
                <p className={cn(
                  'mt-1 text-gray-600',
                  size === 'sm' ? 'text-xs' : 'text-sm'
                )}>
                  {step.description}
                </p>
              )}
              {step.timestamp && (
                <p className={cn(
                  'mt-1 text-gray-400',
                  size === 'sm' ? 'text-xs' : 'text-xs'
                )}>
                  {step.timestamp}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className={cn('flex items-center', stepSizes[size].gap, className)}>
      {steps.map((step, index) => (
        <React.Fragment key={step.id}>
          {/* Step */}
          <div className="flex flex-col items-center">
            <div className={cn(
              'rounded-full border-2 flex items-center justify-center',
              stepSizes[size].icon,
              getStepColors(step.status)
            )}>
              {getStepIcon(step.status)}
            </div>
            <div className="mt-2 text-center max-w-24">
              <p className={cn(
                'font-medium truncate',
                stepSizes[size].text,
                step.status === 'active' ? 'text-purple-900' :
                  step.status === 'completed' ? 'text-green-900' :
                    step.status === 'error' ? 'text-red-900' : 'text-gray-700'
              )}>
                {step.title}
              </p>
              {step.timestamp && (
                <p className="text-xs text-gray-400 mt-1">
                  {step.timestamp}
                </p>
              )}
            </div>
          </div>

          {/* Connecting Line */}
          {index < steps.length - 1 && (
            <div className={cn(
              'flex-1 h-0.5',
              step.status === 'completed' ? 'bg-green-300' : 'bg-gray-200'
            )} style={{ minWidth: '40px' }} />
          )}
        </React.Fragment>
      ))}
    </div>
  )
}

export default WorkflowProgress