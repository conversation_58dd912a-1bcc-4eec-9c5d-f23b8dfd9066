import React, { forwardRef, ButtonHTMLAttributes } from 'react'

// Simple className utility
const cn = (...classes: (string | undefined | null | false)[]) => {
  return classes.filter(Boolean).join(' ')
}

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'success'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  loading?: boolean
  icon?: React.ReactNode
  iconPosition?: 'left' | 'right'
  fullWidth?: boolean
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant = 'primary',
    size = 'md',
    loading = false,
    icon,
    iconPosition = 'left',
    fullWidth = false,
    children,
    disabled,
    ...props
  }, ref) => {

    const baseClasses = cn(
      'inline-flex items-center justify-center gap-2 rounded-lg font-medium transition-all duration-200',
      'focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2',
      'disabled:pointer-events-none disabled:opacity-50',
      'active:scale-95'
    )

    const variantClasses = {
      primary: cn(
        'bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-sm',
        'hover:from-purple-700 hover:to-purple-800 hover:shadow-md',
        'active:from-purple-800 active:to-purple-900'
      ),
      secondary: cn(
        'bg-gray-100 text-gray-900 border border-gray-200',
        'hover:bg-gray-200 hover:border-gray-300',
        'active:bg-gray-300'
      ),
      outline: cn(
        'border-2 border-purple-600 text-purple-600 bg-transparent',
        'hover:bg-purple-50 hover:border-purple-700',
        'active:bg-purple-100'
      ),
      ghost: cn(
        'text-gray-700 bg-transparent',
        'hover:bg-gray-100 hover:text-gray-900',
        'active:bg-gray-200'
      ),
      danger: cn(
        'bg-gradient-to-r from-red-600 to-red-700 text-white shadow-sm',
        'hover:from-red-700 hover:to-red-800 hover:shadow-md',
        'active:from-red-800 active:to-red-900'
      ),
      success: cn(
        'bg-gradient-to-r from-green-600 to-green-700 text-white shadow-sm',
        'hover:from-green-700 hover:to-green-800 hover:shadow-md',
        'active:from-green-800 active:to-green-900'
      ),
    }

    const sizeClasses = {
      sm: 'px-3 py-1.5 text-sm min-h-[32px]',
      md: 'px-4 py-2 text-sm min-h-[40px]',
      lg: 'px-6 py-3 text-base min-h-[48px]',
      xl: 'px-8 py-4 text-lg min-h-[56px]',
    }

    const widthClasses = fullWidth ? 'w-full' : ''

    const LoadingSpinner = () => (
      <svg
        className="animate-spin h-4 w-4"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    )

    return (
      <button
        ref={ref}
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          widthClasses,
          className
        )}
        disabled={disabled || loading}
        {...props}
      >
        {loading && <LoadingSpinner />}
        {!loading && icon && iconPosition === 'left' && icon}
        {children}
        {!loading && icon && iconPosition === 'right' && icon}
      </button>
    )
  }
)

Button.displayName = 'Button'

export { Button }