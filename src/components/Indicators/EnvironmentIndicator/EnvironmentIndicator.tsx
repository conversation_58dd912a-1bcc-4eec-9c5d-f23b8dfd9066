import { useDigiflow } from '@/contexts/DigiflowContext'
import useMediaQuery from '@/hooks/useMediaQuery'
import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Info } from 'lucide-react'

const styles = {
  container: {
    position: 'absolute' as const,
    zIndex: 9999,
    fontFamily: 'Segoe UI Light',
  },
  desktopContainer: {
    left: '50%',
    top: 23,
    transform: 'translate(-50%, -50%)',
  },
  mobileContainer: {
    top: 8,
    right: 2,
  },
  banner: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#dc3545',
    borderRadius: '0 0 8px 8px',
    padding: '6px 18px 9px 18px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
  },
  text: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: '14px',
    letterSpacing: '1px',
  },
  circleButton: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '32px',
    height: '32px',
    borderRadius: '50%',
    backgroundColor: '#dc3545',
    cursor: 'pointer',
    border: 'none',
    padding: 0,
    boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
    transition: 'all 0.2s ease',
    '&:hover': {
      backgroundColor: '#c82333',
      transform: 'scale(1.05)',
    },
  },
  tooltip: {
    position: 'absolute' as const,
    top: '100%',
    right: 0,
    marginTop: '4px',
    backgroundColor: '#333',
    color: 'white',
    padding: '8px 12px',
    borderRadius: '4px',
    fontSize: '12px',
    whiteSpace: 'nowrap' as const,
    boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
  },
}

const EnvironmentIndicator: React.FC = () => {
  const { i18n } = useTranslation()
  const { isTest } = useDigiflow()
  const [showMobileText, setShowMobileText] = useState(false)

  const isMobile = useMediaQuery('(max-width: 901px)')

  if (!isTest) {
    return null
  }

  const environmentText =
    i18n.language === 'tr'
      ? import.meta.env.MODE === 'test'
        ? 'TEST ORTAMI'
        : 'DEVELOPMENT ORTAMI'
      : import.meta.env.MODE === 'test'
        ? 'TEST ENVIRONMENT'
        : 'DEVELOPMENT ENVIRONMENT'

  if (isMobile) {
    return (
      <div style={{ ...styles.container, ...styles.mobileContainer }}>
        <button style={styles.circleButton} onClick={() => setShowMobileText(!showMobileText)}>
          <Info size={18} color="white" />
        </button>
        {showMobileText && <div style={styles.tooltip}>{environmentText}</div>}
      </div>
    )
  }

  return (
    <div style={{ ...styles.container, ...styles.desktopContainer }}>
      <div style={styles.banner}>
        <span style={styles.text}>{environmentText}</span>
      </div>
    </div>
  )
}

export default EnvironmentIndicator
