import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { getUserId } from '@/services'
import { useLocalStorage } from '@/hooks'
import { useDigiflow } from '@/contexts/DigiflowContext'
import useMediaQuery from '@/hooks/useMediaQuery'

const styles = {
  container: {
    position: 'absolute' as const,
    zIndex: 9999,
    fontFamily: 'Segoe UI Light',
  },
  desktopContainer: {
    left: '50%',
    top: 23,
    transform: 'translate(-50%, -50%)',
    width: '440px',
  },
  mobileContainer: {
    top: 8,
    right: 2,
  },
  banner: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ffc107',
    borderRadius: '0 0 8px 8px',
    padding: '6px 18px 9px 18px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
  },
  text: {
    color: 'black',
    fontWeight: 'bold',
    fontSize: '14px',
    letterSpacing: '0.5px',
    textAlign: 'center' as const,
  },
  circleButton: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '32px',
    height: '32px',
    borderRadius: '50%',
    backgroundColor: '#ffc107',
    cursor: 'pointer',
    border: 'none',
    padding: 0,
  },
  tooltip: {
    position: 'absolute' as const,
    top: '100%',
    right: 0,
    marginTop: '8px',
    backgroundColor: '#333',
    color: 'white',
    padding: '8px 12px',
    borderRadius: '4px',
    fontSize: '12px',
    whiteSpace: 'nowrap' as const,
    boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
  },
}

const AdminWarningIndicator: React.FC = () => {
  const { i18n } = useTranslation()
  const { isTest } = useDigiflow()
  const [showWarning, setShowWarning] = useState(false)
  const [showMobileText, setShowMobileText] = useState(false)
  const [storedUserId] = useLocalStorage<number>('UserId')

  const isMobile = useMediaQuery('(max-width: 901px)')

  useEffect(() => {
    const checkUserChange = async () => {
      const currentUserId = await getUserId()
      if (storedUserId && currentUserId && storedUserId !== currentUserId) {
        setShowWarning(true)
      } else {
        setShowWarning(false)
      }
    }

    !isTest && checkUserChange()
    window.addEventListener('storage', checkUserChange)

    return () => {
      window.removeEventListener('storage', checkUserChange)
    }
  }, [storedUserId])

  if (!showWarning) {
    return null
  }

  const warningText =
    i18n.language === 'tr'
      ? 'DİKKAT! BAŞKASI ADINA İŞLEM YAPIYORSUNUZ, EMİN MİSİNİZ!'
      : 'ATTENTION! YOU ARE PERFORMING ACTIONS ON BEHALF OF SOMEONE ELSE, ARE YOU SURE?'

  if (isMobile) {
    return (
      <div style={{ ...styles.container, ...styles.mobileContainer }}>
        <button style={styles.circleButton} onClick={() => setShowMobileText(!showMobileText)}>
          <span style={{ fontSize: '20px' }}>⚠️</span>
        </button>
        {showMobileText && <div style={styles.tooltip}>{warningText}</div>}
      </div>
    )
  }

  return (
    <div style={{ ...styles.container, ...styles.desktopContainer }}>
      <div style={styles.banner}>
        <span style={styles.text}>{warningText}</span>
      </div>
    </div>
  )
}

export default AdminWarningIndicator
