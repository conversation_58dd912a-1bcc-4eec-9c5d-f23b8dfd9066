import WorkflowHistoryTable from '@/components/Tables/WorkflowHistory/WorkflowHistoryTable'
import useGetParams from '@/hooks/useGetParams'
import { IWorkflowData } from '@/types/WorkflowTypes'
import { FC } from 'react'
import { useTranslation } from 'react-i18next'
import { WGrid } from 'wface'

const HistoryComponent: FC<IWorkflowData> = () => {
  const { t } = useTranslation(['common'])
  const params = useGetParams<{ wfInstanceId: number }>(['wfInstanceId'])

  return (
    <WGrid item style={{ justifyContent: 'center', width: '100%' }}>
      <WGrid
        container
        alignItems="center"
        direction="column"
        style={{ borderTopLeftRadius: 5, borderTopRightRadius: 5, background: 'linear-gradient(to right bottom, #01248a, #662E85)' }}
      >
        <h3 style={{ textAlign: 'center', color: 'white', fontSize: 16 }}>{t('history')}</h3>
        <WGrid container alignItems="center" direction="column" style={{ background: 'white', height: 'auto' }}></WGrid>
      </WGrid>
      <WorkflowHistoryTable wfInstanceId={params.wfInstanceId as number} />
    </WGrid>
  )
}

export default HistoryComponent
