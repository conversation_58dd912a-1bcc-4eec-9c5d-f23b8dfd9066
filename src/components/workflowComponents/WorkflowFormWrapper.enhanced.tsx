import { ReactNode } from 'react'
import { Form<PERSON><PERSON>ider, SubmitHandler, UseFormReturn } from 'react-hook-form'
import { useWorkflow } from '@/contexts/WorkflowContext'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/UI/Card/Card'
import UnauthorizedComponent from './UnauthorizedComponent'
import useWorkflowFormSetup from '@/hooks/WorkflowHooks/useWorkflowFormSetup'
import {
  AlertTriangle,
  CheckCircle,
  Clock,
  User
} from 'lucide-react'

interface WorkflowFormWrapperProps {
  onSubmit?: SubmitHandler<any>
  children: ReactNode | ((methods: UseFormReturn<any>) => ReactNode)
  defaultValues: any
  workflowState: string
  schemas: any
  title?: string
  description?: string
}

const EnhancedWorkflowFormWrapper: React.FC<WorkflowFormWrapperProps> = ({
  onSubmit,
  children,
  defaultValues,
  workflowState,
  title = "Workflow Form",
  description = "Complete the form below to proceed with your workflow"
}) => {
  const { canSeeWorkflow, initialData } = useWorkflow()
  const methods = useWorkflowFormSetup(defaultValues)

  if (!canSeeWorkflow) {
    return <UnauthorizedComponent />
  }

  const getStateInfo = (state: string) => {
    switch (state?.toLowerCase()) {
      case 'draft':
        return {
          icon: <Clock className="w-5 h-5 text-yellow-600" />,
          label: 'Draft',
          color: 'bg-yellow-50 text-yellow-700 border-yellow-200'
        }
      case 'pending':
        return {
          icon: <Clock className="w-5 h-5 text-orange-600" />,
          label: 'Pending Review',
          color: 'bg-orange-50 text-orange-700 border-orange-200'
        }
      case 'approved':
        return {
          icon: <CheckCircle className="w-5 h-5 text-green-600" />,
          label: 'Approved',
          color: 'bg-green-50 text-green-700 border-green-200'
        }
      case 'rejected':
        return {
          icon: <AlertTriangle className="w-5 h-5 text-red-600" />,
          label: 'Rejected',
          color: 'bg-red-50 text-red-700 border-red-200'
        }
      default:
        return {
          icon: <User className="w-5 h-5 text-gray-600" />,
          label: 'In Progress',
          color: 'bg-gray-50 text-gray-700 border-gray-200'
        }
    }
  }

  const stateInfo = getStateInfo(workflowState)

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header Section */}
        <Card variant="elevated" className="bg-white">
          <CardHeader>
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <CardTitle className="text-2xl font-bold text-gray-900">
                    {title}
                  </CardTitle>
                  <div className={`px-3 py-1 rounded-full border flex items-center gap-2 ${stateInfo.color}`}>
                    {stateInfo.icon}
                    <span className="text-sm font-medium">{stateInfo.label}</span>
                  </div>
                </div>
                <p className="text-gray-600">{description}</p>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Form Section */}
        <FormProvider {...methods}>
          <form
            id="enhanced-workflow-form"
            onSubmit={methods.handleSubmit(onSubmit || (() => { }))}
            className="space-y-6"
          >
            {/* Main Form Content */}
            <Card variant="elevated" className="bg-white">
              <CardContent className="p-8">
                {typeof children === 'function' ? children(methods) : children}
              </CardContent>
            </Card>
          </form>
        </FormProvider>

        {/* Additional Information */}
        {initialData?.additionalInfo && (
          <Card variant="outlined" className="border-blue-200 bg-blue-50">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0">
                  ℹ️
                </div>
                <div>
                  <h4 className="font-medium text-blue-900 mb-1">
                    Additional Information
                  </h4>
                  <p className="text-sm text-blue-700">
                    {initialData.additionalInfo}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

export default EnhancedWorkflowFormWrapper