:root {
  --primary-color: #5c2d91;
  --primary-color-dark: #4a2474;
  --text-color-on-primary: #ffffff;
  --border-radius: 6px;
  --transition-speed: 0.3s;
  --button-padding: 10px 20px;
  /* Standardized padding */
  --button-font-size: 1rem;
  --button-font-weight: 500;
  /* Medium weight for button text */
  --disabled-bg-color: #f5f5f5;
  --disabled-text-color: #888;
  --disabled-border-color: #ddd;
}

.workflow-admin-name {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  background-color: #f2f2f2;
  padding: 10px;
  width: 100%;
  border-radius: var(--border-radius);
  border: 1px solid #ccc;
  display: inline-block;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: inherit;
}

/* Styles for WButton components used in DigiflowButtons.tsx */
.digiflow-styled-button.MuiButton-root {
  background-color: var(--primary-color);
  color: var(--text-color-on-primary);
  border-radius: var(--border-radius);
  padding: var(--button-padding);
  font-size: var(--button-font-size);
  font-weight: var(--button-font-weight);
  text-transform: none;
  /* Prevent MUI default uppercase */
  transition:
    background-color var(--transition-speed) ease,
    box-shadow var(--transition-speed) ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border: none;
  /* Remove default MUI border if any */
  font-family: inherit;
}

.digiflow-styled-button.MuiButton-root:hover {
  background-color: var(--primary-color-dark);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.digiflow-styled-button.MuiButton-root.Mui-disabled {
  background-color: var(--disabled-bg-color);
  color: var(--disabled-text-color);
  border: 1px solid var(--disabled-border-color);
  box-shadow: none;
  cursor: not-allowed;
}

/* Typography inside the button */
.digiflow-styled-button .MuiTypography-root {
  color: inherit;
  /* Ensure typography inherits button text color */
  font-weight: inherit;
  font-size: inherit;
  font-family: inherit;
}
