import { useGetUser } from '@/hooks/UserHooks/UserHooks'
import { FC } from 'react'
import { WGrid, WTypography } from 'wface'

interface IWorkflowTopInfoComponent {
  flowName: string
  bolum: string
  atanan: number
}

const WorkflowTopInfoComponent: FC<IWorkflowTopInfoComponent> = ({ flowName, bolum, atanan }) => {
  const { data: selectedUserData } = useGetUser({ userId: atanan })

  return (
    <WGrid container direction={'column'} style={{ marginTop: 20, alignItems: 'center' }}>
      <WTypography variant="h5" gutterBottom style={{ fontWeight: 600 }}>
        {flowName}
      </WTypography>
      <WTypography variant="body1" gutterBottom style={{ fontWeight: 500 }}>
        {selectedUserData?.nameSurname}
      </WTypography>
      <WTypography variant="body2" gutterBottom style={{ fontWeight: 400 }}>
        {bolum}
      </WTypography>
    </WGrid>
  )
}

export default WorkflowTopInfoComponent
