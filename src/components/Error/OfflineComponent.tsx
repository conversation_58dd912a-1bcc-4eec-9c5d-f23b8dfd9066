import React, { useEffect, useState } from 'react'
import { Typography, Button, Container } from '@mui/material'
import WifiOffIcon from '@mui/icons-material/WifiOff'
import { useTranslation } from 'react-i18next'
import './OfflineComponent.css'

const OfflineComponent: React.FC = () => {
  const [isOffline, setIsOffline] = useState(!navigator.onLine)
  const { t } = useTranslation('offlineComponent')

  useEffect(() => {
    const handleOnline = () => setIsOffline(false)
    const handleOffline = () => setIsOffline(true)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const handleRetry = () => {
    if (navigator.onLine) {
      setIsOffline(false)
    } else {
      alert(t('stillOfflineMessage'))
    }
  }

  if (!isOffline) return null

  return (
    <Container component="div" className="offline-container">
      <WifiOffIcon className="offline-icon" />
      <Typography variant="h4" component="h1" className="offline-title">
        {t('offlineMessageTitle')}
      </Typography>
      <Typography variant="body1" className="offline-body">
        {t('offlineMessageBody')}
      </Typography>
      <Button onClick={handleRetry} variant="contained" className="offline-retry-button">
        {t('retryButton')}
      </Button>
    </Container>
  )
}

export default OfflineComponent
