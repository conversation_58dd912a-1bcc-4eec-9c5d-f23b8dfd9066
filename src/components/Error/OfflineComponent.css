.offline-container {
  padding: var(--digi-padding-large, 25px 30px);
  text-align: center;
  background-color: var(--digi-background-dark-blue, #001f70);
  color: var(--digi-text-color-light, #f8f9fa);
  border-radius: var(--digi-border-radius, 10px);
  box-shadow: var(--digi-box-shadow, 0 6px 12px rgba(0, 0, 0, 0.15));
  margin: 20px auto;
  max-width: 450px; /* Slightly wider for better text flow */
  width: 90%; /* Responsive width */
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: var(--digi-font-family, 'Arial', sans-serif);
}

.offline-title {
  color: var(--digi-white-color, #fff); /* Title color changed to white for better contrast on dark blue */
  font-size: var(--digi-font-size-h4, 22px);
  font-weight: 600; /* Make title bolder */
  margin-bottom: 20px;
}

.offline-body {
  font-size: var(--digi-font-size-body, 16px);
  line-height: 1.6;
  margin-bottom: 25px; /* Increased margin */
}

.offline-retry-button {
  background-color: var(--digi-accent-purple, #5a3a7e);
  color: var(--digi-white-color, #fff);
  padding: 10px 25px; /* Adjusted padding */
  border: none;
  border-radius: var(--digi-border-radius, 10px); /* Match container's border radius */
  font-size: var(--digi-font-size-body, 16px);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.25s ease-in-out;
  text-transform: none; /* Remove default uppercase from some button libraries */
}

.offline-retry-button:hover {
  background-color: var(--digi-button-hover-bg, #4e316a);
}

.offline-icon {
  font-size: 48px; /* Example size, adjust as needed */
  margin-bottom: 15px;
  color: var(--digi-accent-purple, #5a3a7e); /* Or use white if preferred */
  /* If using an SVG or font icon, you might target it differently */
}

/* Example: if you add an icon like Material UI's WifiOffIcon */
.offline-container .MuiSvgIcon-root {
  font-size: 50px;
  margin-bottom: 15px;
  color: var(--digi-text-color-light, #f8f9fa); /* Icon color to match text */
}
