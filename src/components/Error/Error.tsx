import { FC } from 'react'
import { Dialog, DialogContent } from '@mui/material' // Using Material-UI Dialog
import './Error.css' // Import the CSS file

export const Error: FC<{ show: boolean; fullScreen?: boolean; error?: string }> = ({ show, fullScreen, error }) => {
  if (!show) {
    return null // Don't render anything if show is false
  }

  const errorMessage = error || 'Bir hata oluştu.' // Default error message

  return fullScreen ? (
    <Dialog
      id="errorDialog"
      open={show}
      aria-labelledby="error-dialog-title"
      aria-describedby="error-dialog-description"
      BackdropProps={{
        className: 'error-dialog-backdrop', // Apply class for backdrop styling
      }}
      PaperProps={{
        className: 'error-dialog-paper', // Apply class for paper/dialog styling
      }}
    >
      <DialogContent className="error-dialog-content">
        {' '}
        {/* Apply class for content styling */}
        <h1 id="error-dialog-title">Hata</h1>
        <p id="error-dialog-description" className="error-message">
          {errorMessage}
        </p>
      </DialogContent>
    </Dialog>
  ) : (
    <div className={`error-inline-container ${show ? '' : 'hidden-error'}`}>
      {' '}
      {/* Control visibility with class */}
      <div className="error-inline-content">
        <h1>Hata</h1>
        <p className="error-message">{errorMessage}</p>
      </div>
    </div>
  )
}
