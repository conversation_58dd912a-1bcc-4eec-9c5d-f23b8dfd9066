import React from 'react'
import { Shield, RefreshCw } from 'lucide-react'
import './UnauthorizedComponent/UnauthorizedComponent.css'

interface StandaloneUnauthorizedComponentProps {
  message?: string
  onRetry?: () => void
}

const StandaloneUnauthorizedComponent: React.FC<StandaloneUnauthorizedComponentProps> = ({
  message = 'Bu sayfaya erişim yetkiniz bulunmamaktadır. Lütfen sistem yöneticinizle iletişime geçin.',
  onRetry,
}) => {
  const handleRetry = () => {
    if (onRetry) {
      onRetry()
    } else {
      window.location.reload()
    }
  }

  return (
    <div className="unauthorized-container">
      <div className="unauthorized-content">
        <div className="unauthorized-icon">
          <Shield size={80} />
        </div>

        <h1 className="unauthorized-title">401</h1>
        <h2 className="unauthorized-subtitle">Kimlik Doğrulama Gerekli</h2>

        <p className="unauthorized-message">{message}</p>

        <div className="unauthorized-actions">
          <button className="unauthorized-button unauthorized-button-primary" onClick={handleRetry}>
            <RefreshCw size={20} />
            <span>Tekrar Dene</span>
          </button>
        </div>
      </div>
    </div>
  )
}

export default StandaloneUnauthorizedComponent
