.unauthorized-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
  padding: 1rem;
}

.unauthorized-content {
  text-align: center;
  max-width: 500px;
  width: 100%;
  background-color: white;
  padding: 3rem;
  border-radius: 1rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.unauthorized-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  background-color: #fee2e2;
  border-radius: 50%;
  margin-bottom: 2rem;
  color: #dc2626;
}

.unauthorized-title {
  font-size: 4rem;
  font-weight: 700;
  color: #dc2626;
  margin: 0;
  margin-bottom: 0.5rem;
  line-height: 1;
}

.unauthorized-subtitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
  margin-bottom: 1rem;
}

.unauthorized-message {
  font-size: 1rem;
  color: #6b7280;
  margin-bottom: 2rem;
  line-height: 1.5;
}

.unauthorized-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.unauthorized-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.unauthorized-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.unauthorized-button:active {
  transform: translateY(0);
}

.unauthorized-button-primary {
  background-color: #3b82f6;
  color: white;
}

.unauthorized-button-primary:hover {
  background-color: #2563eb;
}

.unauthorized-button-secondary {
  background-color: #e5e7eb;
  color: #374151;
}

.unauthorized-button-secondary:hover {
  background-color: #d1d5db;
}

/* Responsive Design */
@media (max-width: 640px) {
  .unauthorized-content {
    padding: 2rem;
  }

  .unauthorized-title {
    font-size: 3rem;
  }

  .unauthorized-subtitle {
    font-size: 1.25rem;
  }

  .unauthorized-icon {
    width: 100px;
    height: 100px;
  }

  .unauthorized-icon svg {
    width: 60px;
    height: 60px;
  }

  .unauthorized-actions {
    flex-direction: column;
    width: 100%;
  }

  .unauthorized-button {
    width: 100%;
    justify-content: center;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .unauthorized-container {
    background-color: #111827;
  }

  .unauthorized-content {
    background-color: #1f2937;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  }

  .unauthorized-icon {
    background-color: #7f1d1d;
    color: #fca5a5;
  }

  .unauthorized-title {
    color: #ef4444;
  }

  .unauthorized-subtitle {
    color: #f3f4f6;
  }

  .unauthorized-message {
    color: #9ca3af;
  }

  .unauthorized-button-primary {
    background-color: #2563eb;
  }

  .unauthorized-button-primary:hover {
    background-color: #3b82f6;
  }

  .unauthorized-button-secondary {
    background-color: #374151;
    color: #f3f4f6;
  }

  .unauthorized-button-secondary:hover {
    background-color: #4b5563;
  }
}
