import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Shield, Home, ArrowLeft } from 'lucide-react'
import './UnauthorizedComponent.css'

interface UnauthorizedComponentProps {
  message?: string
  showBackButton?: boolean
  showHomeButton?: boolean
}

const UnauthorizedComponent: React.FC<UnauthorizedComponentProps> = ({
  message = 'Bu sayfaya erişim yetkiniz bulunmamaktadır.',
  showBackButton = true,
  showHomeButton = true,
}) => {
  const navigate = useNavigate()

  const handleGoBack = () => {
    navigate(-1)
  }

  const handleGoHome = () => {
    navigate('/')
  }

  return (
    <div className="unauthorized-container">
      <div className="unauthorized-content">
        <div className="unauthorized-icon">
          <Shield size={80} />
        </div>

        <h1 className="unauthorized-title">401</h1>
        <h2 className="unauthorized-subtitle">Yet<PERSON><PERSON></h2>

        <p className="unauthorized-message">{message}</p>

        <div className="unauthorized-actions">
          {showBackButton && (
            <button className="unauthorized-button unauthorized-button-secondary" onClick={handleGoBack}>
              <ArrowLeft size={20} />
              <span>Geri Dön</span>
            </button>
          )}

          {showHomeButton && (
            <button className="unauthorized-button unauthorized-button-primary" onClick={handleGoHome}>
              <Home size={20} />
              <span>Ana Sayfa</span>
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

export default UnauthorizedComponent
