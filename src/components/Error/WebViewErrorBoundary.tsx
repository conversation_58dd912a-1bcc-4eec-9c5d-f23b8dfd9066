import { Component, ErrorInfo, ReactNode } from 'react'
import { mobileBridge } from '@/utils/mobileBridge'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

interface ErrorBoundaryProps {
  children: ReactNode
  fallback?: ReactNode
}

export class WebViewErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)

    this.setState({ errorInfo })

    if (mobileBridge.isInWebView()) {
      mobileBridge.sendMessage({
        type: 'ERROR',
        data: {
          message: error.message,
          stack: error.stack,
          componentStack: errorInfo.componentStack,
          timestamp: new Date().toISOString(),
        },
        timestamp: new Date().toISOString(),
      })
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              minHeight: '400px',
              padding: '2rem',
              backgroundColor: '#f9f9f9',
              textAlign: 'center',
            }}
          >
            <div
              style={{
                maxWidth: '500px',
                padding: '2rem',
                background: 'white',
                borderRadius: '8px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
              }}
            >
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>⚠️</div>
              <h2 style={{ color: '#e53e3e', marginBottom: '1rem', fontSize: '1.5rem' }}>
                {mobileBridge.isInWebView() ? 'Mobil uygulamada bir hata oluştu' : 'Bir şeyler yanlış gitti'}
              </h2>
              <p style={{ color: '#718096', marginBottom: '1.5rem', lineHeight: '1.6' }}>
                Beklenmedik bir hata oluştu. Sayfayı yenileyerek tekrar deneyin.
              </p>
              <button
                onClick={() => this.setState({ hasError: false, error: undefined, errorInfo: undefined })}
                style={{
                  padding: '0.75rem 1.5rem',
                  border: 'none',
                  borderRadius: '6px',
                  fontWeight: '500',
                  cursor: 'pointer',
                  backgroundColor: '#5c2d91',
                  color: 'white',
                }}
              >
                Tekrar Dene
              </button>
            </div>
          </div>
        )
      )
    }

    return this.props.children
  }
}

export default WebViewErrorBoundary
