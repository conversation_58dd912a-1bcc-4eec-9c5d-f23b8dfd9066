.error-dialog-backdrop {
  background-color: var(--digi-overlay-background-color, rgba(0, 0, 0, 0.65));
}

.error-dialog-paper {
  background-color: var(--digi-white-color, #fff);
  border-radius: var(--digi-border-radius, 8px);
  box-shadow: var(--digi-box-shadow, 0 5px 15px rgba(0, 0, 0, 0.2));
  padding: var(--digi-padding-large, 25px 30px);
  text-align: center;
  max-width: 450px;
  /* Limit width of the dialog */
  width: 90%;
  /* Responsive width */
}

.error-dialog-content h1,
.error-inline-content h1 {
  font-family: var(--digi-font-family, 'Arial', sans-serif);
  color: var(--digi-error-color-text, #721c24);
  font-size: var(--digi-font-size-large, 24px);
  margin-top: 0;
  margin-bottom: 15px;
}

.error-dialog-content .error-message,
.error-inline-content .error-message {
  font-family: var(--digi-font-family, 'Arial', sans-serif);
  color: var(--digi-error-color-text, #721c24);
  font-size: var(--digi-font-size-normal, 16px);
  line-height: 1.6;
  word-break: break-word;
  /* Prevent long error messages from breaking layout */
}

/* Styles for the non-fullscreen (inline) error message */
.error-inline-container {
  display: flex;
  /* Used instead of WGrid's display:block/none for visibility */
  justify-content: center;
  padding-top: 15px;
  text-align: center;
  width: 100%;
}

.error-inline-content {
  background-color: var(--digi-error-background-color, #f8d7da);
  border: 1px solid var(--digi-error-border-color, #f5c6cb);
  color: var(--digi-error-color-text, #721c24);
  padding: var(--digi-padding-normal, 15px 20px);
  border-radius: var(--digi-border-radius, 8px);
  box-shadow: var(--digi-box-shadow, 0 2px 4px rgba(0, 0, 0, 0.05));
  max-width: 600px;
  /* Max width for inline error */
  width: 100%;
}

/* Hide elements when not shown, rather than display:none, to allow transitions if added later */
.hidden-error {
  display: none !important;
}
