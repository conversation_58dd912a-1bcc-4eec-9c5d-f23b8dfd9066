import React from 'react'
import { useTranslation } from 'react-i18next'
import { motion } from 'framer-motion'
import { Container } from '@mui/material'
import { WTypography, WButton } from 'wface'
import { Smartphone, ShieldX } from 'lucide-react'

const MobileUnauthorizedComponent: React.FC = () => {
  const { t } = useTranslation('mobileUnauthorized')

  const styles = {
    container: {
      display: 'flex',
      flexDirection: 'column' as const,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh',
      padding: '1rem',
      backgroundColor: '#f5f5f5',
    },
    card: {
      backgroundColor: 'white',
      borderRadius: '12px',
      padding: '2rem',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
      maxWidth: '400px',
      width: '100%',
      textAlign: 'center' as const,
    },
    iconContainer: {
      position: 'relative' as const,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: '1.5rem',
    },
    phoneIcon: {
      color: '#662E85',
      marginRight: '8px',
    },
    shieldIcon: {
      color: '#dc004e',
      position: 'absolute' as const,
      top: '-8px',
      right: '-8px',
    },
    title: {
      background: 'linear-gradient(to right bottom, #01248a, #662E85)',
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
      fontSize: '1.5rem',
      fontWeight: 'bold',
      marginBottom: '1rem',
    },
    description: {
      color: '#666',
      fontSize: '1rem',
      lineHeight: '1.5',
      marginBottom: '1.5rem',
    },
    button: {
      background: 'linear-gradient(to right bottom, #01248a, #662E85)',
      color: 'white',
      '&:hover': {
        background: 'linear-gradient(to right bottom, #01248a, #662E85)',
        opacity: 0.9,
      },
    },
  }

  const containerVariants = {
    initial: { opacity: 0, scale: 0.8 },
    animate: {
      opacity: 1,
      scale: 1,
    },
  }

  const iconVariants = {
    initial: { rotate: -10, scale: 0 },
    animate: {
      rotate: 0,
      scale: 1,
    },
  }

  const handleContactSupport = () => {
    // You can customize this to open a specific support channel
    // For now, it will close the current window/tab or go back
    if (window.ReactNativeWebView) {
      window.ReactNativeWebView.postMessage(
        JSON.stringify({
          type: 'UNAUTHORIZED_ACCESS',
          action: 'CONTACT_SUPPORT',
        })
      )
    } else {
      window.history.back()
    }
  }

  return (
    <Container maxWidth={false} style={styles.container}>
      <motion.div 
        variants={containerVariants} 
        initial="initial" 
        animate="animate" 
        transition={{ duration: 0.5, ease: "easeOut" }}
        style={styles.card}
      >
        <motion.div 
          variants={iconVariants} 
          initial="initial" 
          animate="animate" 
          transition={{ delay: 0.3, duration: 0.4, ease: "easeOut" }}
          style={styles.iconContainer}
        >
          <Smartphone size={48} style={styles.phoneIcon} />
          <ShieldX size={24} style={styles.shieldIcon} />
        </motion.div>

        <WTypography variant="h4" style={styles.title}>
          {t('title')}
        </WTypography>

        <WTypography variant="body1" style={styles.description}>
          {t('description')}
        </WTypography>

        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <WButton variant="contained" style={styles.button} onClick={handleContactSupport} size="large" fullWidth>
            {t('button')}
          </WButton>
        </motion.div>
      </motion.div>
    </Container>
  )
}

export default MobileUnauthorizedComponent
