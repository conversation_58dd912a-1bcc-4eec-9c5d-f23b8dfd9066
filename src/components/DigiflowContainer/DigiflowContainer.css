.digiflow-container {
  display: flex;
  flex-direction: column;
  /* <PERSON>ack children vertically */
  align-items: center;
  /* Center content horizontally */
  justify-content: flex-start;
  /* Align content to the top */
  min-height: calc(100vh - 120px);
  /* Adjust based on header/footer height */
  padding: var(--digi-container-padding, 20px);
  background-color: var(--digi-background-color, #f8f9fa);
  font-family: var(--digi-font-family, 'Arial', sans-serif);
  color: var(--digi-text-color, #212529);
  width: 100%;
  box-sizing: border-box;
}

.digiflow-container--centered {
  justify-content: center;
  /* For pages where content should be vertically centered */
}

.digiflow-container .warning-message {
  background-color: var(--digi-warning-background-color, #fff3cd);
  color: var(--digi-warning-text-color, #856404);
  border: 1px solid var(--digi-warning-border-color, #ffeeba);
  padding: 15px 20px;
  border-radius: var(--digi-border-radius, 8px);
  text-align: center;
  font-size: var(--digi-font-size-normal, 16px);
  margin-top: 20px;
  /* Space from top or other elements */
  box-shadow: var(--digi-box-shadow, 0 2px 4px rgba(0, 0, 0, 0.05));
  max-width: 600px;
  /* Prevent it from being too wide */
  width: 100%;
}

/* Example of how sections within the container could be styled */
.digiflow-container .content-section {
  background-color: var(--digi-white-color, #fff);
  padding: 20px;
  border-radius: var(--digi-border-radius, 8px);
  box-shadow: var(--digi-box-shadow, 0 4px 8px rgba(0, 0, 0, 0.1));
  width: 100%;
  max-width: 1200px;
  /* Max width for content area */
  margin-bottom: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .digiflow-container {
    padding: 15px;
  }

  .digiflow-container .warning-message {
    padding: 12px 15px;
    font-size: 15px;
  }

  .digiflow-container .content-section {
    padding: 15px;
  }
}

@media (max-width: 576px) {
  .digiflow-container {
    padding: 10px;
  }

  .digiflow-container .warning-message {
    padding: 10px;
    font-size: 14px;
  }

  .digiflow-container .content-section {
    padding: 10px;
  }
}
