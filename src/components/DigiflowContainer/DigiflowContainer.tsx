import { useAppContext } from 'wface'
import { FC } from 'react'
import { Loading } from '../Loading/Loading'
import MessagePopUp from '../MessagePopUp/MessagePopUp'
import './DigiflowContainer.css'

interface IContainerProps {
  isAccessPermission: boolean
  loading?: boolean
  message?: string
  className?: string
  children: JSX.Element[] | JSX.Element
  centered?: boolean
}
const DigiflowContainer: FC<IContainerProps> = (props) => {
  const { cache, setValue } = useAppContext()
  const loading = cache['loading'] || props.loading
  const message = cache['message'] || props.message

  const containerClasses = ['digiflow-container', props.className, props.centered ? 'digiflow-container--centered' : ''].filter(Boolean).join(' ')

  return (
    <div className={containerClasses}>
      <Loading show={loading} fullScreen />
      <MessagePopUp message={message} closePopup={() => setValue('message', null)} />
      {props.isAccessPermission ? props.children : <div className="warning-message">Bu sayfa için yetkiniz bulunmamaktadır</div>}
    </div>
  )
}
export default DigiflowContainer
