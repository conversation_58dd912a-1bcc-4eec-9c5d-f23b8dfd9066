import React, { useEffect, useCallback } from 'react'
import { useNavigationHistory } from '../../hooks/useNavigationHistory'

interface NavigationManagerProps {
  openScreenById: (screen: string, extra: any) => void
}

export const NavigationManager: React.FC<NavigationManagerProps> = ({ openScreenById }) => {
  const { goBack } = useNavigationHistory(openScreenById)

  const handlePopState = useCallback(() => {
    goBack()
  }, [goBack])

  useEffect(() => {
    window.addEventListener('popstate', handlePopState)
    return () => window.removeEventListener('popstate', handlePopState)
  }, [handlePopState])

  return null
}
