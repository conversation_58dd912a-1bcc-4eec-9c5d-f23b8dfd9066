import React from 'react'
import { useTranslation } from 'react-i18next'
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  Button
} from '@/components/UI'
import {
  Save,
  Send,
  Download,
  Plus,
  Settings,
  User,
  CheckCircle,
  Clock,
  AlertTriangle
} from 'lucide-react'

const UIShowcase: React.FC = () => {
  const { t } = useTranslation('common')

  return (
    <div className="p-8 space-y-8 bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {t('enhanced_components')}
          </h1>
          <p className="text-gray-600">
            {t('modern_design_showcase')}
          </p>
        </div>



        {/* Button Variants Section */}
        <Card variant="elevated" className="mb-8">
          <CardHeader>
            <CardTitle>{t('button_components')}</CardTitle>
            <CardDescription>
              {t('modern_buttons_desc')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Primary Buttons */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-gray-700">{t('primary_actions')}</h4>
                <div className="space-y-2">
                  <Button variant="primary" size="sm" icon={<Save className="w-4 h-4" />}>
                    {t('save_changes')}
                  </Button>
                  <Button variant="primary" size="md" icon={<Send className="w-4 h-4" />}>
                    {t('submit_form')}
                  </Button>
                  <Button variant="primary" size="lg" icon={<Plus className="w-5 h-5" />}>
                    {t('create_new')}
                  </Button>
                </div>
              </div>

              {/* Secondary Buttons */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-gray-700">{t('secondary_actions')}</h4>
                <div className="space-y-2">
                  <Button variant="secondary" size="sm" icon={<Download className="w-4 h-4" />}>
                    {t('download')}
                  </Button>
                  <Button variant="outline" size="md" icon={<Settings className="w-4 h-4" />}>
                    {t('settings')}
                  </Button>
                  <Button variant="ghost" size="md" icon={<User className="w-4 h-4" />}>
                    {t('profile')}
                  </Button>
                </div>
              </div>

              {/* State Buttons */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-gray-700">{t('states_special')}</h4>
                <div className="space-y-2">
                  <Button variant="success" size="md" icon={<CheckCircle className="w-4 h-4" />}>
                    {t('approved')}
                  </Button>
                  <Button variant="danger" size="md" icon={<AlertTriangle className="w-4 h-4" />}>
                    {t('delete')}
                  </Button>
                  <Button variant="primary" size="md" loading>
                    {t('processing')}
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Card Variants Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Default Card */}
          <Card variant="default">
            <CardHeader>
              <CardTitle>{t('default_card')}</CardTitle>
              <CardDescription>
                {t('standard_card_desc')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                This is the default card style with clean borders and minimal shadow.
              </p>
              <Button variant="outline" size="sm" className="mt-4">
                {t('learn_more')}
              </Button>
            </CardContent>
          </Card>

          {/* Elevated Card */}
          <Card variant="elevated">
            <CardHeader>
              <CardTitle>{t('elevated_card')}</CardTitle>
              <CardDescription>
                {t('enhanced_shadow_desc')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Elevated cards use stronger shadows to create visual hierarchy.
              </p>
              <Button variant="primary" size="sm" className="mt-4">
                {t('get_started')}
              </Button>
            </CardContent>
          </Card>

          {/* Gradient Card */}
          <Card variant="gradient">
            <CardHeader>
              <CardTitle>{t('gradient_card')}</CardTitle>
              <CardDescription>
                {t('purple_gradient_desc')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-purple-700">
                Special gradient cards for featured content and highlights.
              </p>
              <Button variant="outline" size="sm" className="mt-4 border-purple-300 text-purple-700">
                {t('explore')}
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Interactive Cards Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
          <Card variant="outlined" interactive className="cursor-pointer">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5 text-orange-500" />
                {t('interactive_card')}
              </CardTitle>
              <CardDescription>
                {t('interactive_desc')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                {t('interactive_content')}
              </p>
            </CardContent>
          </Card>

          <Card variant="ghost" className="border-dashed border-2 border-gray-300">
            <CardHeader>
              <CardTitle className="text-gray-500">
                {t('ghost_card')}
              </CardTitle>
              <CardDescription>
                {t('transparent_desc')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-500">
                {t('ghost_content')}
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default UIShowcase