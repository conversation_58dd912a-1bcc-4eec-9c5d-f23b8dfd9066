import React, { useContext } from 'react'
import { useLocation } from 'react-router-dom'
import UserChangeControl from './UserChangeControl'
import UserChangeControlWorkflow from './UserChangeControlWorkflow'
import { WorkflowContext } from '@/contexts/WorkflowContext'

interface UserChangeControlWrapperProps {
  userChangeEvent?: (userId: number) => void
}

const UserChangeControlWrapper: React.FC<UserChangeControlWrapperProps> = (props) => {
  const location = useLocation()

  // Safely check if we're within a WorkflowProvider
  const isInWorkflowContext = () => {
    try {
      // This will return null if context doesn't exist, won't throw
      const context = useContext(WorkflowContext)
      return context !== undefined
    } catch {
      return false
    }
  }

  // Check if we're on a workflow path
  const isWorkflowPath = location.pathname.startsWith('/react/main/workflow') && location.pathname !== '/react/main/workflow-management'

  // Only use WorkflowComponent if we're in a workflow path AND have context
  if (isWorkflowPath && isInWorkflowContext()) {
    return <UserChangeControlWorkflow {...props} />
  }

  // Default to regular UserChangeControl
  return <UserChangeControl {...props} />
}

export default UserChangeControlWrapper
