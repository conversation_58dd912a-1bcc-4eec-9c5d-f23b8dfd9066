import useAppHelper from '@/services/wface/appHelper'
import { useTranslation } from 'react-i18next'
import { useUpdateEffect } from '@/hooks'
import { useListAllUsers } from '@/hooks/UserHooks/UserHooks'
import SelectBox from '../formElements/SelectBox/SelectBox'
import { IOption } from '@/types'
import { useWorkflow } from '@/contexts/WorkflowContext'
import { useDigiflow } from '@/contexts/DigiflowContext'
import { useUserStore } from '@/stores/userStore'

interface UserChangeControlProps {
  userChangeEvent?: (userId: number) => void
}

const UserChangeControlWorkflow: React.FC<UserChangeControlProps> = ({ userChangeEvent = () => {} }) => {
  const { t } = useTranslation(['common'])
  const { isSysAdmin, isTest } = useDigiflow()

  const { getInitialScreenValue, setInitialScreenValue } = useAppHelper()
  const { setSelectedUser: setWorkflowSelectedUser, refetchInitialData } = useWorkflow()
  const { data: users, isLoading } = useListAllUsers()

  // Use Zustand store
  const { selectedUser, setSelectedUser } = useUserStore()

  useUpdateEffect(() => {
    if (users && users.length > 0 && !selectedUser) {
      const initialUser = users.find((x) => x.value == getInitialScreenValue('UserId')) ?? null
      setSelectedUser(initialUser)
      setWorkflowSelectedUser(initialUser) // Also update workflow context
    }
  }, [users, selectedUser, setSelectedUser, setWorkflowSelectedUser])

  useUpdateEffect(() => {
    if (selectedUser) {
      setInitialScreenValue('UserId', Number(selectedUser.value))
      setWorkflowSelectedUser(selectedUser) // Update workflow context
      userChangeEvent(selectedUser.value)
    }
  }, [selectedUser, setWorkflowSelectedUser])

  return (
    <div style={{ width: '100%' }}>
      asd
      {isSysAdmin && isTest && (
        <SelectBox
          value={selectedUser}
          isLoading={isLoading}
          disabled={users?.length === 0}
          label={t('active_user')}
          defaultText={null}
          searchable={true}
          onChange={(option: IOption | IOption[] | null) => {
            if (!Array.isArray(option) && option?.value != '0') {
              setSelectedUser(option)
              setWorkflowSelectedUser(option) // Update workflow context
              refetchInitialData()
            }
          }}
          options={users ?? []}
          id="selectedActiveUser"
          name="selectedActiveUser"
        />
      )}
    </div>
  )
}

export default UserChangeControlWorkflow
