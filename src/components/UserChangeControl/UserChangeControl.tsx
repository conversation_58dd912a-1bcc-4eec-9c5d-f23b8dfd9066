import { useListAllUsers } from '@/hooks/UserHooks/UserHooks'
import SelectBox from '../formElements/SelectBox/SelectBox'
import { IOption } from '@/types'
import { useLocalStorage } from '@/hooks'
import { useLayoutEffect } from 'react'
import { useDigiflow } from '@/contexts/DigiflowContext'
import { useUserStore } from '@/stores/userStore'

interface UserChangeControlProps {
  userChangeEvent?: (userId: number) => void
}

const UserChangeControl: React.FC<UserChangeControlProps> = ({ userChangeEvent = () => {} }) => {
  const { isSysAdmin, isTest } = useDigiflow()
  const [storedValue, setStoredValue] = useLocalStorage<number>('UserId')
  const { data: users, isLoading } = useListAllUsers()

  // Use Zustand store
  const { selectedUser, setSelectedUser } = useUserStore()

  useLayoutEffect(() => {
    if (users && users.length > 0 && !selectedUser) {
      const initialUser = users.find((x) => x.value == storedValue) ?? null
      setSelectedUser(initialUser)
    }
  }, [storedValue, users, selectedUser, setSelectedUser])

  return (
    <div style={{ width: '100%', backgroundColor: 'white' }}>
      {selectedUser && isSysAdmin && isTest && (
        <SelectBox
          value={selectedUser}
          isLoading={isLoading}
          disabled={users?.length === 0}
          label={''}
          defaultText={null}
          searchable={true}
          onChange={(option: IOption | IOption[] | null) => {
            if (!Array.isArray(option)) {
              if (option?.value && option?.value != '0') {
                setSelectedUser(option)
                setStoredValue(Number(option.value))
                userChangeEvent(option.value)
              }
            }
          }}
          options={users ?? []}
          id="selectedActiveUser"
          name="selectedActiveUser"
        />
      )}
    </div>
  )
}

export default UserChangeControl
