import { useState, useRef, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { ChevronDown, Globe } from 'lucide-react'
import useMediaQuery from '@/hooks/useMediaQuery'
import './LanguageSelect.css' // Import the CSS file

type LanguageOption = {
  code: string
  name: string
}

interface LanguageSelectProps {
  languages: LanguageOption[]
}

const LanguageSelect: React.FC<LanguageSelectProps> = ({ languages }) => {
  const [isOpen, setIsOpen] = useState(false)
  const { i18n } = useTranslation()
  const [selectedLanguage, setSelectedLanguage] = useState<string>(localStorage.getItem('language') || i18n.language)
  const containerRef = useRef<HTMLDivElement>(null)
  const isMobile = useMediaQuery('(max-width: 901px)')

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleLanguageChange = (language: LanguageOption) => {
    if (localStorage.getItem('language') !== language.code) {
      localStorage.setItem('language', language.code)
      i18n.changeLanguage(language.code)
      setSelectedLanguage(language.code)
    }
    setIsOpen(false)
  }

  const currentLanguage = languages.find((lang) => lang.code === selectedLanguage)

  return (
    <div ref={containerRef} className="language-select-container">
      <div onClick={() => setIsOpen(!isOpen)} className={`language-select-button ${isMobile ? 'mobile' : ''} ${isOpen ? 'active' : ''}`}>
        <span className="language-select-icon">
          <Globe size={isMobile ? 14 : 16} />
        </span>
        <span className="language-select-selected-text">{currentLanguage?.name || 'Language'}</span>
        <span className={`language-select-chevron ${isOpen ? 'open' : ''}`}>
          <ChevronDown size={isMobile ? 14 : 16} />
        </span>
      </div>

      <div className={`language-select-dropdown ${isMobile ? 'mobile' : ''} ${isOpen ? 'open' : ''}`}>
        {languages.map((language) => (
          <div
            key={language.code}
            onClick={() => handleLanguageChange(language)}
            className={`language-select-option ${isMobile ? 'mobile' : ''} ${language.code === selectedLanguage ? 'active' : ''}`}
          >
            <span className="language-select-icon">
              <Globe size={isMobile ? 14 : 16} />
            </span>
            {language.name}
          </div>
        ))}
      </div>
    </div>
  )
}

export default LanguageSelect
