import React from 'react'
import { styled } from '@mui/material/styles'
import RadioGroup from '@mui/material/RadioGroup'
import FormControl from '@mui/material/FormControl'
import FormLabel from '@mui/material/FormLabel'
import FormHelperText from '@mui/material/FormHelperText'
import { DigiRadio } from './DigiRadio'
import { DigiRadioGroupProps } from '@/types'
import './DigiRadio.css' // Import the CSS file

const StyledFormControl = styled(FormControl)(() => ({
  // Removed default MUI styling, will be handled by CSS classes
}))

const StyledRadioGroup = styled(RadioGroup)<{ axis?: 'horizontal' | 'vertical' }>(() => ({
  // Removed default MUI styling, will be handled by CSS classes
}))

export const DigiRadioGroup: React.FC<DigiRadioGroupProps> = ({
  axis = 'vertical',
  label,
  options = [],
  value,
  onChange,
  error,
  helperText,
  className,
  style,
  disabled,
  ...rest
}) => {
  const handleChange = (_: React.ChangeEvent<HTMLInputElement>, value: string) => {
    const processedValue = /^\d+$/.test(value) ? Number(value) : value
    onChange?.(processedValue)
  }

  return (
    <StyledFormControl error={error} className={`digi-radio-form-control ${className || ''}`} style={style} disabled={disabled}>
      {label && <FormLabel className="digi-radio-form-label">{label}</FormLabel>}
      <StyledRadioGroup value={value} onChange={handleChange} axis={axis} className={`digi-radio-group ${axis}`} {...rest}>
        {options.map((option) => (
          <DigiRadio key={option.value} label={option.label} value={option.value} disabled={disabled || option.disabled} />
        ))}
      </StyledRadioGroup>
      {helperText && <FormHelperText className="digi-radio-form-helper-text">{helperText}</FormHelperText>}
    </StyledFormControl>
  )
}
