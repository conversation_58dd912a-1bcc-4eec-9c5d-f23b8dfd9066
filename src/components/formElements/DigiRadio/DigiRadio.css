/* DigiRadio.css */
:root {
  --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --primary-color: #5c2d91;
  /* Purple */
  --primary-color-dark: #45226d;
  --primary-color-light: #b39ddb;
  --text-color: #212529;
  --text-color-secondary: #6c757d;
  --disabled-color: #adb5bd;
  --error-color: #dc3545;
  --border-radius-small: 0.25rem;
  /* 4px */
  --spacing-xs: 0.25rem;
  /* 4px */
  --spacing-sm: 0.5rem;
  /* 8px */
  --spacing-md: 1rem;
  /* 16px */
  --font-size-base: 1rem;
  /* 16px */
  --font-size-small: 0.875rem;
  /* 14px */
}

/* Styles for DigiRadioGroup FormControl */
.digi-radio-form-control {
  font-family: var(--font-family);
  margin-bottom: var(--spacing-md);
  /* Add some space below the group */
}

/* Styles for the FormLabel within DigiRadioGroup */
.digi-radio-form-label {
  color: var(--text-color) !important;
  /* Override MUI default */
  font-size: var(--font-size-base) !important;
  font-weight: 600 !important;
  margin-bottom: var(--spacing-sm) !important;
  padding-left: 0;
  /* Align with radio buttons if they have padding */
}

.digi-radio-form-label.Mui-focused {
  color: var(--primary-color-dark) !important;
}

.digi-radio-form-label.Mui-disabled {
  color: var(--disabled-color) !important;
}

.digi-radio-form-label.Mui-error {
  color: var(--error-color) !important;
}

/* Styles for the RadioGroup itself */
.digi-radio-group {
  gap: var(--spacing-sm);
  /* Control spacing between radio buttons */
}

.digi-radio-group.horizontal {
  flex-direction: row;
}

.digi-radio-group.vertical {
  flex-direction: column;
}

/* Styles for individual DigiRadio FormControlLabel */
.digi-radio-form-control-label {
  margin-left: 0;
  /* Reset MUI default margin */
  margin-right: var(--spacing-md);
  /* Space between horizontal radios */
  font-family: var(--font-family);
}

.digi-radio-form-control-label:last-child {
  margin-right: 0;
}

/* Styles for the label text of each radio button */
.digi-radio-label {
  font-size: var(--font-size-base) !important;
  color: var(--text-color-secondary) !important;
  font-family: var(--font-family) !important;
}

.digi-radio-form-control-label.Mui-disabled .digi-radio-label {
  color: var(--disabled-color) !important;
}

/* Styles for the Radio component (the circle) */
.digi-radio-root .MuiSvgIcon-root {
  font-size: 1.4rem;
  /* Adjust size of the radio icon */
}

.digi-radio-root.Mui-checked {
  color: var(--primary-color) !important;
}

.digi-radio-root.Mui-disabled {
  color: var(--disabled-color) !important;
}

.digi-radio-root.Mui-error {
  /* You might not need this if error is shown at group level */
}

/* Styles for FormHelperText within DigiRadioGroup */
.digi-radio-form-helper-text {
  font-size: var(--font-size-small) !important;
  color: var(--text-color-secondary) !important;
  font-family: var(--font-family) !important;
  margin-top: var(--spacing-xs) !important;
  padding-left: var(--spacing-sm);
  /* Align with radio buttons if they have padding */
}

.digi-radio-form-helper-text.Mui-error {
  color: var(--error-color) !important;
}

.digi-radio-form-helper-text.Mui-disabled {
  color: var(--disabled-color) !important;
}
