import React from 'react'
import Radio from '@mui/material/Radio'
import FormControlLabel from '@mui/material/FormControlLabel'
import { DigiRadioProps } from '@/types'
import './DigiRadio.css' // Import the CSS file

export const DigiRadio: React.FC<DigiRadioProps> = ({ label, value, checked, onChange, disabled, FormControlLabelProps, ...rest }) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.(event.target.value)
  }

  return (
    <FormControlLabel
      className={`digi-radio-form-control-label ${FormControlLabelProps?.className || ''}`}
      control={
        <Radio
          className={`digi-radio-root ${rest.className || ''}`}
          checked={checked}
          onChange={handleChange}
          value={value}
          disabled={disabled}
          {...rest}
        />
      }
      label={<span className="digi-radio-label">{label}</span>}
      disabled={disabled}
      {...FormControlLabelProps}
    />
  )
}
