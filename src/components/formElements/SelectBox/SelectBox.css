/* SelectBox.css */
.select-box {
  border-radius: 4px;
  transition:
    border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  position: relative;
  width: 100%;
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
  box-sizing: border-box;
  z-index: 1;
}

.select-box.full-width {
  width: 100%;
}

.select-box.active {
  z-index: 1301;
}

.select-box:not(.disabled):hover {
  outline: 1px solid #5c2d91;
  box-shadow: 0 0 0 1px rgba(92, 45, 145, 0.5);
}

.select-box:not(.disabled):focus-within {
  border-color: #5c2d91;
  box-shadow: 0 0 0 2px rgba(92, 45, 145, 0.6);
}

.select-box.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.select-box.disabled:hover,
.select-box.disabled:focus-within {
  border-color: #ccc;
  box-shadow: none;
}

/* Label Styles */
.select-box-label {
  position: absolute;
  top: 50%;
  left: 10px;
  transform: translateY(-50%);
  transition: all 0.2s ease;
  pointer-events: none;
  background: white;
  padding: 0 4px;
  font-size: 14px;
  color: #999;
}

.select-box-select:disabled ~ .select-box-label {
  background-color: transparent;
}

/* Select Input Styles */
.select-box-select {
  width: 100%;
  padding: 12px;
  border: 1px solid #ccc;
  color: rgba(0, 0, 0, 0.87);
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  position: relative;
  background: transparent;
  box-sizing: border-box;
  cursor: pointer;
}

.select-box-select:focus {
  outline: 2px solid #5c2d91;
  border-color: #45226d;
}

.select-box-select:focus + .select-box-label,
.select-box-select:not(:placeholder-shown) + .select-box-label {
  top: 0;
  left: 5px;
  font-size: 12px;
  color: #45226d;
}

.select-box-select:disabled {
  background-color: #f0f0f0;
  cursor: not-allowed;
  opacity: 0.5;
}

/* Icons and Indicators */
.select-box-disabled-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  pointer-events: none;
}

.select-box.disabled .select-box-arrow {
  display: none;
}

.select-box-arrow {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  transition: transform 0.2s ease;
  color: #999;
  width: 16px;
  height: 16px;
}

.select-box-arrow.open:not(.upward) {
  transform: translateY(-50%) rotate(180deg);
}

.select-box-arrow.open.upward {
  transform: translateY(-50%) rotate(0deg);
}

/* Dropdown Styles */
.select-box-dropdown {
  position: absolute;
  width: 100%;
  max-height: 320px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #fff;
  overflow-y: auto;
  z-index: 1300;
  box-sizing: border-box;
  opacity: 0;
  transform: translateY(8px);
  animation: dropdownFadeIn 0.2s ease forwards;
  transform-origin: top center;
  transition:
    transform 0.2s ease,
    opacity 0.2s ease,
    box-shadow 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  scrollbar-width: thin;
  scrollbar-color: #5c2d91 #f0f0f0;
}

.select-box-dropdown.dropdown-upward {
  bottom: calc(100% + 8px);
  top: auto;
  transform-origin: bottom center;
  transform: translateY(-8px);
  animation: dropdownFadeInUpward 0.2s ease forwards;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
}

/* Dropdown Items */
.select-box-item {
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  line-height: normal;
  transition:
    background-color 0.15s ease,
    color 0.15s ease,
    transform 0.2s ease;
}

.select-box-item:hover {
  background-color: rgba(92, 45, 145, 0.1);
}

.select-box-item.selected {
  background-color: #5c2d91;
  color: white;
}

.select-box-item.highlighted {
  background-color: rgba(92, 45, 145, 0.15);
}

.select-box-item.selected.highlighted,
.select-box-item.selected-highlighted {
  background-color: #45226d;
  color: white;
}

.select-box-item.disabled {
  color: #999;
  cursor: not-allowed;
  pointer-events: none;
}

.select-box-item:focus {
  outline: none;
  background-color: #f5f5f5;
}

.select-box-item.highlighted:focus {
  background-color: #e8e8e8;
}

/* Error and Loading States */
.select-box-error {
  margin-top: 4px;
  color: red;
  font-size: 12px;
}

.select-box-spinner {
  position: absolute;
  right: 10px;
  top: 20%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border: 3px solid #ccc;
  border-top: 3px solid #5c2d91;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Dropdown Interaction Area */
.select-box-dropdown::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 8px;
  background: transparent;
  pointer-events: none;
}

.select-box-dropdown.dropdown-upward::before {
  bottom: -8px;
}

.select-box-dropdown:not(.dropdown-upward)::before {
  top: -8px;
}

/* Scrollbar Styles */
.select-box-dropdown::-webkit-scrollbar {
  width: 6px;
}

.select-box-dropdown::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 3px;
}

.select-box-dropdown::-webkit-scrollbar-thumb {
  background-color: #5c2d91;
  border-radius: 3px;
  border: 1px solid #f0f0f0;
}

/* Animations */
@keyframes spin {
  0% {
    transform: translateY(-50%) rotate(0deg);
  }

  100% {
    transform: translateY(-50%) rotate(360deg);
  }
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(8px) scaleY(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scaleY(1);
  }
}

@keyframes dropdownFadeInUpward {
  from {
    opacity: 0;
    transform: translateY(-8px) scaleY(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scaleY(1);
  }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .select-box-dropdown {
    max-height: 50vh;
  }

  .select-box-item {
    padding: 12px 16px;
  }
}

/* High Contrast Mode */
@media (forced-colors: active) {
  .select-box-item.selected {
    forced-color-adjust: none;
    background-color: Highlight;
    color: HighlightText;
  }
}

/* Z-index Management */
.WPopper-root {
  z-index: 1300 !important;
}

/* Size Variations */
.select-box.small {
  min-height: 32px;
}

.select-box.medium {
  min-height: 40px;
}

.select-box.default {
  min-height: 48px;
}

/* Select Input Size Variations */
.select-box-select.small {
  padding: 10px 28px 8px 12px;
  font-size: 12px;
  min-height: 32px;
}

.select-box-select.medium {
  padding: 10px 32px 10px 14px;
  font-size: 13px;
  min-height: 40px;
}

.select-box-select.default {
  padding: 12px 36px 12px 16px;
  font-size: 14px;
  min-height: 48px;
}

/* Label Size Adjustments */
.select-box-label.small {
  font-size: 12px;
}

.select-box-label.medium {
  font-size: 13px;
}

.select-box-label.default {
  font-size: 14px;
}

/* Focused Label Size Adjustments */
.select-box-select.small:focus + .select-box-label.small,
.select-box-select.small:not(:placeholder-shown) + .select-box-label.small {
  font-size: 10px;
}

.select-box-select.medium:focus + .select-box-label.medium,
.select-box-select.medium:not(:placeholder-shown) + .select-box-label.medium {
  font-size: 11px;
}

.select-box-select.default:focus + .select-box-label.default,
.select-box-select.default:not(:placeholder-shown) + .select-box-label.default {
  font-size: 12px;
}

/* Arrow Icon Size Variations */
.select-box-arrow.small {
  width: 16px;
  height: 16px;
  right: 6px;
}

.select-box-arrow.medium {
  width: 16px;
  height: 16px;
  right: 8px;
}

.select-box-arrow.default {
  width: 16px;
  height: 16px;
  right: 12px;
}

/* Dropdown Item Size Variations */
.select-box-item.small {
  padding: 6px 12px;
  font-size: 12px;
  min-height: 28px;
}

.select-box-item.medium {
  padding: 8px 14px;
  font-size: 13px;
  min-height: 34px;
}

.select-box-item.default {
  padding: 10px 16px;
  font-size: 14px;
  min-height: 40px;
}

/* Loading Spinner Size Variations */
.select-box-spinner.small {
  width: 16px;
  height: 16px;
  border-width: 2px;
  right: 8px;
}

.select-box-spinner.medium {
  width: 18px;
  height: 18px;
  border-width: 2.5px;
  right: 10px;
}

.select-box-spinner.default {
  width: 20px;
  height: 20px;
  border-width: 3px;
  right: 12px;
}

/* Disabled Icon Size Variations */
.select-box-disabled-icon.small {
  width: 12px;
  height: 12px;
  right: 8px;
}

.select-box-disabled-icon.medium {
  width: 14px;
  height: 14px;
  right: 10px;
}

.select-box-disabled-icon.default {
  width: 16px;
  height: 16px;
  right: 12px;
}

/* Error Message Size Variations */
.select-box-error.small {
  font-size: 10px;
  margin-top: 3px;
}

.select-box-error.medium {
  font-size: 11px;
  margin-top: 4px;
}

.select-box-error.default {
  font-size: 12px;
  margin-top: 5px;
}

/* Dropdown Max Heights by Size */
.select-box-dropdown.small {
  max-height: 280px;
}

.select-box-dropdown.medium {
  max-height: 300px;
}

.select-box-dropdown.default {
  max-height: 320px;
}

/* Mobile Size Adjustments */
@media (max-width: 768px) {
  .select-box-dropdown.small {
    max-height: 40vh;
  }

  .select-box-dropdown.medium {
    max-height: 45vh;
  }

  .select-box-dropdown.default {
    max-height: 50vh;
  }

  .select-box-item.small {
    padding: 10px 12px;
  }

  .select-box-item.medium {
    padding: 12px 14px;
  }

  .select-box-item.default {
    padding: 14px 16px;
  }
}

/* RTL Support */
[dir='rtl'] .select-box-label {
  left: auto;
  right: 10px;
}

[dir='rtl'] .select-box-arrow,
[dir='rtl'] .select-box-spinner,
[dir='rtl'] .select-box-disabled-icon {
  left: 10px;
  right: auto;
}

[dir='rtl'] .select-box-select {
  text-align: right;
  padding-right: 12px;
  padding-left: 28px;
}

/* Touch Device Optimizations */
@media (hover: none) {
  .select-box-item:hover {
    background-color: transparent;
  }

  .select-box-item:active {
    background-color: rgba(92, 45, 145, 0.1);
  }
}

/* Print Styles */
@media print {
  .select-box {
    border: 1px solid #000;
  }

  .select-box-select {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  .select-box-arrow,
  .select-box-spinner,
  .select-box-disabled-icon {
    display: none;
  }
}

/* Dropdown Item Size Variations - Revised */
.select-box-item.small {
  padding: 4px 8px;
  font-size: 12px;
  min-height: 24px;
  line-height: 16px;
}

.select-box-item.medium {
  padding: 6px 12px;
  font-size: 13px;
  min-height: 32px;
  line-height: 20px;
}

.select-box-item.default {
  padding: 8px 12px;
  font-size: 14px;
  min-height: 36px;
  line-height: 22px;
}

/* Adjust dropdown max heights to match new item sizes */
.select-box-dropdown.small {
  max-height: 240px;
  /* Accommodates approximately 10 small items */
}

.select-box-dropdown.medium {
  max-height: 288px;
  /* Accommodates approximately 9 medium items */
}

.select-box-dropdown.default {
  max-height: 324px;
  /* Accommodates approximately 9 default items */
}

/* Mobile Size Adjustments - Revised */
@media (max-width: 768px) {
  .select-box-item.small {
    padding: 6px 8px;
    min-height: 28px;
  }

  .select-box-item.medium {
    padding: 8px 12px;
    min-height: 34px;
  }

  .select-box-item.default {
    padding: 10px 12px;
    min-height: 38px;
  }
}
