/* DigiTextField.css */
.digi-textfield {
  border-radius: 6px; /* Updated from 4px to match common style */
  transition:
    border-color 0.3s ease-in-out,
    /* Standardized transition */ box-shadow 0.3s ease-in-out;
  position: relative;
  width: 100%;
  margin: 0 0 15px 0; /* Added bottom margin for spacing in forms */
  padding: 0;
  font-family: inherit; /* Ensure font is inherited */
  box-sizing: border-box;
  z-index: 1;
}

.digi-textfield.full-width {
  width: 100%;
}

.digi-textfield.active {
  z-index: 1301; /* Keep for specific active states if needed */
}

/* Hover and Focus States */
.digi-textfield:not(.disabled) .digi-textfield-input:hover {
  border-color: #5c2d91; /* Primary color for hover */
}

.digi-textfield:not(.disabled) .digi-textfield-input:focus {
  border-color: #5c2d91; /* Primary color for focus */
  box-shadow: 0 0 0 3px rgba(92, 45, 145, 0.25); /* Shadow consistent with ASPX */
  outline: none;
}

/* Disabled State */
.digi-textfield.disabled {
  opacity: 1; /* Let child elements handle their disabled appearance */
  pointer-events: none;
}

.digi-textfield.disabled .digi-textfield-input {
  background-color: #f5f5f5 !important; /* Match ASPX disabled style */
  color: #888 !important;
  border-color: #ddd !important;
  cursor: not-allowed !important;
}

.digi-textfield.disabled .digi-textfield-input:hover,
.digi-textfield.disabled .digi-textfield-input:focus {
  border-color: #ddd !important;
  box-shadow: none;
  outline: none;
}

.digi-textfield.disabled .digi-textfield-label {
  color: #888 !important;
  background-color: transparent !important; /* Ensure label is readable on disabled bg */
}

/* Label Styles */
.digi-textfield-label {
  position: absolute;
  top: 50%;
  left: 12px; /* Adjusted for padding */
  transform: translateY(-50%);
  transition: all 0.2s ease-in-out; /* Smoother transition */
  pointer-events: none;
  background: white; /* Ensure label bg is white for overlap */
  padding: 0 4px;
  font-size: 14px;
  color: #777; /* Softer label color */
}

.digi-textfield-input:disabled ~ .digi-textfield-label {
  background-color: transparent;
}

/* Input Styles */
.digi-textfield-input {
  width: 100%;
  padding: 10px 12px !important; /* Standardized padding */
  border: 1px solid #ddd; /* Standard border */
  color: #333; /* Darker input text color */
  border-radius: 6px; /* Match container */
  font-size: 14px;
  outline: none;
  position: relative;
  background: transparent;
  box-sizing: border-box;
  font-family: inherit;
  resize: none;
  transition:
    border-color 0.3s ease-in-out,
    box-shadow 0.3s ease-in-out; /* Added for consistency */
}

/* Multiline styles */
.digi-textfield.multiline .digi-textfield-input {
  min-height: 100px; /* Match ASPX textarea */
  line-height: 1.5;
  padding-top: 16px; /* Adjust for label */
}

.digi-textfield.multiline .digi-textfield-label {
  top: 18px; /* Adjusted for multiline */
}

.digi-textfield.multiline .digi-textfield-input:focus + .digi-textfield-label,
.digi-textfield.multiline .digi-textfield-input:not(:placeholder-shown) + .digi-textfield-label {
  top: -8px; /* Adjusted for multiline */
  font-size: 12px;
  color: #5c2d91; /* Primary color when active */
  background-color: white;
  padding: 0 4px;
}

.digi-textfield-input:focus {
  /* Focus styles are handled by .digi-textfield:not(.disabled) .digi-textfield-input:focus */
  outline: none;
}

.digi-textfield-input:focus + .digi-textfield-label,
.digi-textfield-input:not(:placeholder-shown) + .digi-textfield-label {
  top: -8px; /* Adjusted for better floating position */
  left: 10px;
  font-size: 12px;
  color: #5c2d91; /* Primary color when active */
}

/* Error Styles */
.digi-textfield-helper-text {
  margin-top: 4px;
  padding-left: 12px; /* Align with input text */
  font-size: 0.85em; /* Match ASPX form-note */
  min-height: 16px;
  color: #777; /* Match ASPX form-note */
}

.digi-textfield-helper-text.error {
  color: #e74c3c; /* Match ASPX required-field color */
}

.digi-textfield.error .digi-textfield-input,
.digi-textfield.error .digi-textfield-input:hover,
.digi-textfield.error .digi-textfield-input:focus {
  border-color: #e74c3c !important; /* Error color */
}

.digi-textfield.error .digi-textfield-input:focus {
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.25) !important; /* Error shadow */
}

.digi-textfield.error .digi-textfield-label {
  color: #e74c3c !important; /* Error color for label */
}

/* Required Field Indicator */
.digi-textfield-required {
  color: #e74c3c; /* Match ASPX */
  margin-left: 4px;
}

/* Size Variations - Simplified for now, can be expanded if specific small/medium/default pixel values are critical */
.digi-textfield.small .digi-textfield-input {
  padding: 8px 10px !important;
  font-size: 13px;
  min-height: 36px;
}
.digi-textfield.small .digi-textfield-label {
  font-size: 13px;
}
.digi-textfield.small .digi-textfield-input:focus + .digi-textfield-label.small,
.digi-textfield.small .digi-textfield-input:not(:placeholder-shown) + .digi-textfield-label.small {
  font-size: 11px;
  top: -7px;
}

/* Button Container Styles */
.digi-textfield-button-container {
  display: flex;
  align-items: center;
  gap: 4px; /* Gap between buttons if multiple */
}

.digi-textfield-adornment {
  display: flex;
  align-items: center;
  color: #777; /* Softer color for adornments */
  padding: 0 8px; /* Padding around adornments */
}

/* Individual Button Style */
.digi-textfield-button {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #5c2d91; /* Primary color for button icons */
  border-radius: 4px; /* Slight rounding for buttons */
}

.digi-textfield-button:hover:not(:disabled) {
  background-color: rgba(92, 45, 145, 0.1); /* Hover effect */
}

.digi-textfield-button.disabled {
  color: #bbb;
  cursor: not-allowed;
}

.digi-textfield-helper-container {
  display: flex;
  justify-content: space-between; /* For helper text and counter */
  align-items: flex-start;
  padding: 4px 12px 0 12px; /* Align with input text */
  min-height: 16px;
  font-size: 0.85em; /* Match ASPX form-note */
}

.digi-textfield-counter {
  /* margin-left: 12px; */ /* Removed to allow space-between to work */
  color: #777; /* Match ASPX form-note */
  font-size: 1em; /* Relative to helper-container */
  white-space: nowrap;
}

/* Ensure helper text and counter are on the same line and spaced out */
.digi-textfield-helper-container .digi-textfield-helper-text {
  flex-grow: 1;
  padding-left: 0; /* Reset padding as it's on the container now */
  margin-top: 0; /* Reset margin */
}
