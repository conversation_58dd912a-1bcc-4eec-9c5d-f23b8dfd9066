import React, { forwardRef, useCallback, useState, useRef, useEffect } from 'react'
import './DigiTextField.css'
import { Visibility, VisibilityOff } from '@mui/icons-material'
import type { TextFieldProps } from '@mui/material/TextField'
import type { InputHTMLAttributes, TextareaHTMLAttributes } from 'react'

export interface WTextFieldButton {
  icon: React.ReactNode
  onClick(event: React.MouseEvent<HTMLButtonElement>, value: string): void
  id?: string
  className?: string
  style?: React.CSSProperties
  title?: string
  disabled?: boolean
}

// Add TextareaHTMLAttributes to support textarea props
export interface DigiTextFieldProps
  extends Omit<InputHTMLAttributes<HTMLInputElement> & TextareaHTMLAttributes<HTMLTextAreaElement>, 'size' | 'value' | 'onChange'> {
  label: string
  value?: string | number
  onChange?: (value: string) => void
  leftButtons?: WTextFieldButton[]
  rightButtons?: WTextFieldButton[]
  size?: 'small' | 'medium' | 'default'
  fullWidth?: boolean
  error?: string | boolean
  helperText?: string
  variant?: 'outlined' | 'filled' | 'standard'
  InputProps?: TextFieldProps['InputProps']
  inputProps?: InputHTMLAttributes<HTMLInputElement> & TextareaHTMLAttributes<HTMLTextAreaElement>
  onFocus?: (event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  onBlur?: (event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  multiline?: boolean
  rows?: number
}

export const DigiTextField = forwardRef<HTMLInputElement | HTMLTextAreaElement, DigiTextFieldProps>(
  (
    {
      // Base props
      id,
      className,
      style,
      label,
      error,
      helperText,
      disabled = false,
      size = 'small',
      fullWidth = true,
      name,
      autoComplete,
      required,

      // Custom button props
      leftButtons,
      rightButtons,

      // TextField specific props
      type = 'text',
      value = '',
      onChange,
      InputProps,
      inputProps = {}, // Add inputProps with default empty object
      variant = 'outlined',
      onFocus,
      onBlur,
      multiline = false,
      rows = 1,

      // Rest of the props
      ...rest
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState<boolean>(false)
    const [isFocused, setIsFocused] = useState<boolean>(false)
    const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement | any>(null)

    // Convert value to string for input
    const inputValue = value?.toString() ?? ''

    // Merge refs
    const handleRef = useCallback(
      (instance: HTMLInputElement | HTMLTextAreaElement | null) => {
        inputRef.current = instance

        // Handle forwarded ref
        if (typeof ref === 'function') {
          ref(instance)
        } else if (ref) {
          ref.current = instance
        }
      },
      [ref]
    )

    // Handle value changes
    const handleChange = useCallback(
      (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        onChange?.(event.target.value)
      },
      [onChange]
    )

    // Handle password visibility toggle
    const handleClickShowPassword = useCallback(() => {
      setShowPassword((prev) => !prev)
    }, [])

    // Prevent mouse down event from propagating
    const handleMouseDownPassword = useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
      event.preventDefault()
    }, [])

    // Handle focus events
    const handleFocus = useCallback(
      (event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setIsFocused(true)
        onFocus?.(event)
      },
      [onFocus]
    )

    const handleBlur = useCallback(
      (event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setIsFocused(false)
        onBlur?.(event)
      },
      [onBlur]
    )

    // Create button elements from button props
    const renderButtons = useCallback(
      (buttons?: WTextFieldButton[], position: 'start' | 'end' = 'end') => {
        if (!buttons?.length) return null

        return (
          <div className={`digi-textfield-adornment ${position}`}>
            <div className="digi-textfield-button-container">
              {buttons.map((button, index) => (
                <button
                  key={button.id || `${position}-button-${index}`}
                  onClick={(e) => button.onClick(e, inputValue)}
                  className={`digi-textfield-button ${button.className || ''} ${button.disabled ? 'disabled' : ''}`}
                  style={button.style}
                  type="button"
                  title={button.title}
                  disabled={button.disabled}
                  aria-label={button.title}
                >
                  {button.icon}
                </button>
              ))}
            </div>
          </div>
        )
      },
      [inputValue]
    )

    // Password visibility toggle button
    const passwordToggleButton = type === 'password' && (
      <button
        className="digi-textfield-button"
        onClick={handleClickShowPassword}
        onMouseDown={handleMouseDownPassword}
        type="button"
        aria-label={showPassword ? 'Hide password' : 'Show password'}
      >
        {showPassword ? <VisibilityOff /> : <Visibility />}
      </button>
    )

    // Effect to handle autofocus
    useEffect(() => {
      if (rest.autoFocus && inputRef.current) {
        inputRef.current.focus()
      }
    }, [rest.autoFocus])

    const inputType = type === 'password' ? (showPassword ? 'text' : 'password') : type
    const hasError = Boolean(error)
    const helperTextContent = (error || helperText)?.toString()

    const InputComponent = multiline ? 'textarea' : 'input'

    return (
      <div
        className={`digi-textfield ${fullWidth ? 'full-width' : ''} ${
          disabled ? 'disabled' : ''
        } ${hasError ? '' : ''} ${isFocused ? 'focused' : ''} ${size} ${variant} ${className || ''} ${multiline ? 'multiline' : ''}`}
        style={style}
      >
        <div className="digi-textfield-wrapper">
          {renderButtons(leftButtons, 'start')}
          <InputComponent
            {...rest}
            {...inputProps}
            ref={handleRef}
            id={id}
            name={name}
            className={`digi-textfield-input ${size}`}
            type={!multiline ? inputType : undefined}
            value={inputValue}
            onChange={handleChange}
            disabled={disabled}
            required={required}
            aria-invalid={hasError}
            aria-describedby={helperTextContent ? `${id}-helper-text` : undefined}
            placeholder=" "
            autoComplete={!multiline ? autoComplete : undefined}
            onFocus={handleFocus}
            onBlur={handleBlur}
            rows={multiline ? rows : undefined}
          />
          <label htmlFor={id} className={`digi-textfield-label ${size}`}>
            {label}
            {required && <span className="digi-textfield-required">*</span>}
          </label>
          {(rightButtons?.length || type === 'password') && (
            <div className="digi-textfield-adornment end">
              <div className="digi-textfield-button-container">
                {renderButtons(rightButtons)}
                {!multiline && passwordToggleButton}
              </div>
            </div>
          )}
        </div>
        {(helperTextContent || (multiline && inputProps?.maxLength)) && (
          <div className="digi-textfield-helper-container">
            {multiline && inputProps?.maxLength && <div className="digi-textfield-counter">{inputProps.maxLength - inputValue.length}</div>}
          </div>
        )}{' '}
      </div>
    )
  }
)

DigiTextField.displayName = 'DigiTextField'

export default DigiTextField
