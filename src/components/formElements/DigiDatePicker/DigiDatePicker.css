/* DigiDatePicker.css */

/* Define root variables for colors and common styles */
:root {
  --primary-color: #5c2d91;
  --primary-color-dark: #4a2474; /* A darker shade for hover states */
  --error-color: #e74c3c;
  --disabled-bg-color: #f5f5f5;
  --disabled-text-color: #888;
  --disabled-border-color: #ddd;
  --text-color: #333;
  --label-color: #777;
  --border-color: #ddd;
  --border-radius: 6px;
  --transition-speed: 0.3s;
  --form-note-font-size: 0.85em;
}

.digi-date-picker {
  position: relative;
  width: 100%;
  display: inline-block;
  font-family: inherit; /* Ensure font is inherited */
  margin-bottom: 15px; /* Spacing similar to DigiTextField */
}

.digi-date-picker-input .MuiInputBase-root {
  border-radius: var(--border-radius);
  transition:
    border-color var(--transition-speed) ease,
    box-shadow var(--transition-speed) ease;
  color: var(--text-color);
}

.digi-date-picker-input .MuiOutlinedInput-notchedOutline {
  border-color: var(--border-color);
}

.digi-date-picker-input .MuiInputBase-root:hover .MuiOutlinedInput-notchedOutline {
  border-color: var(--primary-color);
}

.digi-date-picker-input .MuiInputBase-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(92, 45, 145, 0.25); /* Focus shadow */
  border-width: 1px; /* Keep border width consistent on focus, shadow provides emphasis */
}

.digi-date-picker-input .MuiInputLabel-root {
  color: var(--label-color);
  transition:
    color var(--transition-speed) ease,
    transform var(--transition-speed) ease;
}

.digi-date-picker-input .MuiInputLabel-root.Mui-focused {
  color: var(--primary-color);
}

.digi-date-picker .MuiInputLabel-shrink {
  background-color: white; /* Ensure label background is white for overlap */
  padding: 0 5px;
  margin-left: -5px; /* Adjust to align with border */
  transform: translate(14px, -9px) scale(0.75); /* MUI default shrink, ensure it looks good */
}

/* Position the date picker absolutely within the container */
.digi-date-picker-popper {
  position: absolute;
  top: calc(100% + 4px); /* Add a small gap */
  left: 0;
  z-index: 1300;
  margin-top: 4px; /* Adjusted from 8px for tighter feel */
  background: white;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); /* Softer shadow */
  animation: datepickerFadeIn 0.2s ease-out;
  width: auto; /* Allow popper to size based on content */
  min-width: 280px; /* Ensure a minimum width */
}

/* Size Variations - Standardize padding and font-size */
.digi-date-picker.small .MuiInputBase-input {
  padding: 8px 10px;
  font-size: 0.875rem;
}
.digi-date-picker.small .MuiInputLabel-root {
  font-size: 0.875rem;
}
.digi-date-picker.small .MuiInputLabel-shrink {
  transform: translate(14px, -6px) scale(0.75);
}

.digi-date-picker.medium .MuiInputBase-input {
  padding: 10px 12px; /* Consistent padding */
  font-size: 1rem;
}
.digi-date-picker.medium .MuiInputLabel-root {
  font-size: 1rem;
}

.digi-date-picker.large .MuiInputBase-input {
  padding: 12px 14px;
  font-size: 1.125rem;
}
.digi-date-picker.large .MuiInputLabel-root {
  font-size: 1.125rem;
}
.digi-date-picker.large .MuiInputLabel-shrink {
  transform: translate(14px, -10px) scale(0.75);
}

/* Custom Calendar Styles */
.digi-date-picker .MuiPickersDay-root {
  font-size: 0.875rem;
  transition:
    background-color var(--transition-speed) ease,
    color var(--transition-speed) ease;
  border-radius: var(--border-radius); /* Rounded days */
}

.digi-date-picker .MuiPickersDay-root.Mui-selected {
  background-color: var(--primary-color);
  color: white;
}
.digi-date-picker .MuiPickersDay-root.Mui-selected:hover {
  background-color: var(--primary-color-dark);
}

.digi-date-picker .MuiPickersDay-root:hover {
  background-color: rgba(92, 45, 145, 0.1); /* Light primary hover */
}

.digi-date-picker .MuiPickersDay-today {
  border-color: var(--primary-color); /* Highlight today with primary color border */
}

/* Disabled State */
.digi-date-picker.disabled .MuiInputBase-root {
  background-color: var(--disabled-bg-color) !important;
  color: var(--disabled-text-color) !important;
  cursor: not-allowed;
}

.digi-date-picker.disabled .MuiOutlinedInput-notchedOutline {
  border-color: var(--disabled-border-color) !important;
}

.digi-date-picker.disabled .MuiInputLabel-root {
  color: var(--disabled-text-color) !important;
}

.digi-date-picker.disabled .MuiIconButton-root {
  color: var(--disabled-text-color) !important;
  cursor: not-allowed;
}
.digi-date-picker.disabled .MuiInputBase-input {
  cursor: not-allowed;
}

/* Error States */
.digi-date-picker-input .MuiInputBase-root.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: var(--error-color);
}
.digi-date-picker-input .MuiInputBase-root.Mui-error.Mui-focused .MuiOutlinedInput-notchedOutline {
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.25); /* Error focus shadow */
}

.digi-date-picker-input .MuiFormHelperText-root.Mui-error {
  color: var(--error-color);
  font-size: var(--form-note-font-size);
  margin-left: 14px; /* Align with input text */
  margin-right: 14px;
}
.digi-date-picker-input .MuiFormHelperText-root:not(.Mui-error) {
  color: var(--label-color); /* Default helper text color */
  font-size: var(--form-note-font-size);
  margin-left: 14px;
  margin-right: 14px;
}

/* Icons */
.digi-date-picker .MuiIconButton-root {
  color: var(--label-color); /* Softer icon color */
  transition: color var(--transition-speed) ease;
}
.digi-date-picker .MuiIconButton-root:hover {
  color: var(--primary-color);
}
.digi-date-picker .MuiInputAdornment-root .MuiIconButton-root {
  padding: 6px; /* Adjust padding for adornment icons */
}
.digi-date-picker .MuiInputAdornment-positionEnd {
  margin-right: 0px; /* Adjust if needed */
}

/* Animation */
@keyframes datepickerFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile Responsive Styles - Retain existing for now, review if needed */
@media (max-width: 600px) {
  .digi-date-picker-popper {
    position: fixed !important;
    top: auto !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    transform: none !important;
    max-height: 80vh;
    border-radius: 12px 12px 0 0;
    margin: 0 !important;
    animation: slideUpMobile 0.3s ease-out;
  }

  @keyframes slideUpMobile {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }
}

/* Remove redundant/conflicting styles if StyledTextField in .tsx handles them */
/* For example, .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline from original CSS might conflict */
/* The .tsx StyledTextField already defines some focus/hover styles. Ensure these CSS rules complement them. */

/* Cleanup: Remove styles that might be overly specific or handled by MUI defaults if they cause issues */
/* .digi-date-picker .MuiPickersDay-root:disabled was very specific, MUI default might be fine */
.digi-date-picker .MuiPickersDay-root.Mui-disabled {
  color: #bdbdbd; /* Standard disabled day color */
  /* background-color: transparent !important; */ /* Let MUI handle this */
  /* border: none !important; */ /* Let MUI handle this */
}

/* Ensure helper text is styled correctly */
.digi-date-picker-input .MuiFormHelperText-root {
  font-family: inherit; /* Ensure helper text font matches */
}

/* Adjustments for clear button (X icon) */
.digi-date-picker-input .MuiInputAdornment-root button[aria-label='Clear date'] {
  /* More specific selector if needed */
  color: var(--label-color);
}
.digi-date-picker-input .MuiInputAdornment-root button[aria-label='Clear date']:hover {
  color: var(--error-color); /* Make clear button red on hover for emphasis */
}

/* Calendar icon styling */
.digi-date-picker-input .MuiInputAdornment-root button:not([aria-label="Clear date"]) .MuiSvgIcon-root, /* Target calendar icon */
.digi-date-picker-input .MuiInputAdornment-root button svg[data-lucide="calendar-icon"] {
  /* Target lucide icon if used directly */
  color: var(--primary-color);
}
.digi-date-picker-input .MuiInputAdornment-root button:not([aria-label='Clear date']):hover .MuiSvgIcon-root,
.digi-date-picker-input .MuiInputAdornment-root button:hover svg[data-lucide='calendar-icon'] {
  color: var(--primary-color-dark);
}
