import React, { useState, useRef, useCallback, forwardRef } from 'react'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { styled } from '@mui/material/styles'
import TextField from '@mui/material/TextField'
import IconButton from '@mui/material/IconButton'
import { useTranslation } from 'react-i18next'
import dayjs, { Dayjs } from 'dayjs'
import 'dayjs/locale/tr'
import { ClickAwayListener, Paper } from '@mui/material'
import { X, CalendarIcon } from 'lucide-react'
import './DigiDatePicker.css'

interface DigiDatePickerProps {
  id?: string
  name?: string
  label?: string
  value?: Date | null
  onChange?: (date: Date | null) => void
  error?: string
  helperText?: string
  disabled?: boolean
  readOnly?: boolean
  clearable?: boolean
  minDate?: Date
  maxDate?: Date
  fullWidth?: boolean
  required?: boolean
  disablePast?: boolean
  disableFuture?: boolean
  size?: 'small' | 'medium' | 'large'
  variant?: 'outlined' | 'filled' | 'standard'
  format?: string
  autoFocus?: boolean
  className?: string
  style?: React.CSSProperties
  placeholder?: string
  disableHighlightToday?: boolean
  shouldDisableDate?: (date: Date) => boolean
  onOpen?: () => void
  onClose?: () => void
  onError?: (error: Error | null) => void
  inputProps?: any
}

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiInputBase-root': {
    borderRadius: theme.shape.borderRadius,
  },
  '& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline': {
    borderColor: theme.palette.primary.main,
    borderWidth: 2,
  },
  '& .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline': {
    borderColor: theme.palette.primary.main,
  },
  '& .MuiInputLabel-root.Mui-focused': {
    color: theme.palette.primary.main,
  },
  '& .MuiInputAdornment-root': {
    marginRight: -8,
  },
}))

const PopperContainer = styled(Paper)(({ theme }) => ({
  zIndex: 1300,
  background: 'white',
  borderRadius: '4px',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
  animation: 'datepickerFadeIn 0.2s ease-out',
  width: '100%',
  position: 'relative',

  '& .MuiPickersDay-root': {
    fontSize: '0.875rem',

    '&.Mui-selected': {
      backgroundColor: theme.palette.primary.main,
      color: theme.palette.primary.contrastText,

      '&:hover': {
        backgroundColor: theme.palette.primary.dark,
      },
    },

    '&:hover': {
      backgroundColor: 'rgba(0, 0, 0, 0.04)',
    },
  },
}))

const DigiDatePicker = forwardRef<HTMLDivElement, DigiDatePickerProps>((props, ref) => {
  const {
    id,
    name,
    label,
    value,
    onChange,
    error,
    helperText,
    disabled = false,
    readOnly = false,
    clearable = true,
    minDate,
    maxDate,
    fullWidth = true,
    required = false,
    disablePast = false,
    disableFuture = false,
    size = 'medium',
    variant = 'outlined',
    format = 'DD.MM.YYYY',
    autoFocus = false,
    className,
    style,
    placeholder,
    shouldDisableDate,
    onError,
    inputProps,
    onOpen,
    onClose,
  } = props

  const { i18n } = useTranslation()
  const [isOpen, setIsOpen] = useState(false)
  const [inputValue, setInputValue] = useState('')
  const [internalError, setInternalError] = useState<string | null>(null)
  const inputRef = useRef<HTMLDivElement>(null)
  const isInternalChange = useRef(false)

  const handleDateChange = useCallback(
    (date: Dayjs | null) => {
      if (onChange) {
        // Only convert to Date if date is valid
        const dateValue = date?.isValid() ? date.toDate() : null
        onChange(dateValue)
      }
      setIsOpen(false)
      onClose?.()
    },
    [onChange, onClose]
  )

  const handleInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = event.target.value
      setInputValue(newValue)

      if (newValue && !dayjs(newValue, format).isValid()) {
        setInternalError('Invalid date format')
        onError?.(new Error('Invalid date format'))
      } else {
        setInternalError(null)
        onError?.(null)
      }
    },
    [format, onError]
  )

  const handleClear = useCallback(
    (event: React.MouseEvent) => {
      event.stopPropagation()
      event.preventDefault()

      isInternalChange.current = true
      setInputValue('')
      setInternalError(null)
      onError?.(null)

      // Explicitly pass null to onChange
      if (onChange) {
        onChange(null)
      }

      setIsOpen(false)
      onClose?.()
    },
    [onChange, onClose, onError]
  )

  const handleClickAway = useCallback(() => {
    setIsOpen(false)
    onClose?.()
  }, [onClose])

  const handleOpen = useCallback(
    (event: React.MouseEvent) => {
      event.stopPropagation()
      isInternalChange.current = false
      if (!disabled && !readOnly) {
        setIsOpen(true)
        onOpen?.()
      }
    },
    [disabled, readOnly, onOpen]
  )

  const formatDate = useCallback(
    (date: Date | null): string => {
      if (!date) return ''
      const dayjsDate = dayjs(date)
      return dayjsDate.isValid() ? dayjsDate.format(format) : ''
    },
    [format]
  )

  React.useEffect(() => {
    if (!isInternalChange.current) {
      setInputValue(formatDate(value as Date))
    }
    isInternalChange.current = false
  }, [value, formatDate])

  const containerClasses = ['digi-date-picker', size, className, error || internalError ? '' : '', disabled ? 'disabled' : '']
    .filter(Boolean)
    .join(' ')

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale={i18n.language}>
      <ClickAwayListener onClickAway={handleClickAway}>
        <div className={containerClasses} ref={ref} style={style}>
          <div className="digi-date-picker-input" ref={inputRef}>
            <StyledTextField
              id={id}
              name={name}
              label={label}
              value={inputValue}
              onChange={handleInputChange}
              onClick={handleOpen}
              error={Boolean(error || internalError)}
              helperText={error || internalError || helperText}
              disabled={disabled}
              required={required}
              fullWidth={fullWidth}
              size={size === 'large' ? 'medium' : size}
              variant={variant}
              autoFocus={autoFocus}
              placeholder={placeholder}
              InputProps={{
                ...inputProps,
                readOnly: true,
                endAdornment: (
                  <>
                    {clearable && inputValue && !disabled && !readOnly && (
                      <IconButton size="small" onClick={handleClear} sx={{ padding: '4px' }}>
                        <X size={16} />
                      </IconButton>
                    )}
                    <IconButton size="small" sx={{ padding: '4px' }} disabled={disabled} onClick={handleOpen}>
                      <CalendarIcon size={16} />
                    </IconButton>
                  </>
                ),
              }}
            />
          </div>
          {isOpen && (
            <div className="digi-date-picker-popper">
              <PopperContainer>
                <DatePicker
                  open={true}
                  value={value ? dayjs(value) : null}
                  onChange={handleDateChange}
                  minDate={minDate ? dayjs(minDate) : undefined}
                  maxDate={maxDate ? dayjs(maxDate) : undefined}
                  disablePast={disablePast}
                  disableFuture={disableFuture}
                  shouldDisableDate={shouldDisableDate ? (date) => shouldDisableDate(date.toDate()) : undefined}
                  slots={{
                    textField: () => null,
                  }}
                />
              </PopperContainer>
            </div>
          )}
        </div>
      </ClickAwayListener>
    </LocalizationProvider>
  )
})

DigiDatePicker.displayName = 'DigiDatePicker'

export default DigiDatePicker
