import React, { useState, useRef, useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { WButton, <PERSON>ard, WCardActions, WCardContent, WCircularProgress, WTextField, WTypography } from 'wface'
import { useMutation } from '@tanstack/react-query'
import toast from 'react-hot-toast'
import { useUpdateEffect } from '@/hooks'
import DigiTable from '@/components/Tables/DigiTable/DigiTable'
import { IFile, IUploadedFile, IUploadFile } from '@/types'
import api from '@/api'
import { useDownloadFile } from '@/hooks/FileHooks/FileHooks'
import './FileUpload.css' // Import the CSS file

interface FileUploadProps {
  onUpload: (files: IUploadedFile[]) => void
  onDelete: (fileName: string) => void
  pathKey: string
  initialFiles?: IUploadedFile[]
  disabled?: boolean
  variant?: 'simple' | 'full'
  title?: string
  useMobileTable?: boolean
  buttonText?: string
  uploadOnSelect?: boolean
  onUploadStart?: () => void
  onUploadEnd?: () => void
  className?: string // Add className prop
}

// 1) Hook for uploading files
const useUploadFiles = (pathKey: string) => {
  return useMutation({
    mutationFn: async (files: IFile[] | IFile) => {
      const formData = new FormData()

      if (Array.isArray(files)) {
        files.forEach((file) => {
          if (file.file) {
            const uniqueFile = new File([file.file], file.name, {
              type: file.file.type,
            })
            formData.append('files', uniqueFile)
          } else {
            throw new Error('File is not selected')
          }
        })
      } else if (files.file) {
        const uniqueFile = new File([files.file], files.name, {
          type: files.file.type,
        })
        formData.append('files', uniqueFile)
      } else {
        throw new Error('File is not selected')
      }

      formData.append('pathKey', pathKey)

      try {
        const response = await api.post('/file/upload-multiple', formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
        })
        // Make sure your backend returns { urls: [] } or similar:
        return response.data
      } catch (error) {
        toast.error('File upload failed')
        throw error
      }
    },
  })
}

// 2) Hook for deleting files
const useDeleteFile = () => {
  return useMutation({
    mutationFn: async ({ pathKey, fileName }: { pathKey: string; fileName: string }) => {
      const response = await api.delete(`/file/delete?pathKey=${pathKey}&fileName=${fileName}`)
      return response.data
    },
  })
}

const FileUpload: React.FC<FileUploadProps> = ({
  onUpload,
  onDelete,
  title,
  pathKey,
  initialFiles = [],
  disabled,
  variant = 'full',
  useMobileTable,
  buttonText,
  uploadOnSelect = true,
  onUploadStart,
  onUploadEnd,
  className, // Destructure className
}) => {
  const { t } = useTranslation('fileUpload')
  const fileInputRef = useRef<HTMLInputElement>(null)
  const dropAreaRef = useRef<HTMLDivElement>(null)
  const uploadingRef = useRef(false)

  const [selectedFiles, setSelectedFiles] = useState<IUploadFile[]>([])
  const [uploadedFiles, setUploadedFiles] = useState<IUploadedFile[]>([])
  const [isDragging, setIsDragging] = useState(false)
  const [isUploading, setIsUploading] = useState(false)

  const uploadFilesMutation = useUploadFiles(pathKey)
  const deleteFileMutation = useDeleteFile()
  const downloadFile = useDownloadFile()

  // Reset the file input DOM element
  const resetFileInput = useCallback(() => {
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [])

  // 3) Main upload handler
  const handleUpload = useCallback(
    async (e?: React.MouseEvent) => {
      e?.preventDefault()
      e?.stopPropagation()

      if (selectedFiles.length === 0) {
        setIsUploading(false)
        uploadingRef.current = false
        return
      }

      // If we're already uploading, skip
      if (uploadingRef.current) return

      try {
        uploadingRef.current = true
        setIsUploading(true)
        onUploadStart && onUploadStart()

        const response = await uploadFilesMutation.mutateAsync(selectedFiles)

        // If something canceled the upload in the meantime:
        if (!uploadingRef.current) {
          setIsUploading(false)
          return
        }

        // Make sure the response has the shape you expect
        if (!response.urls || !Array.isArray(response.urls)) {
          console.error('[FileUpload] Unexpected response shape, missing `urls` array:', response)
          toast.error(t('uploadError'))
          return
        }

        const newUploadedFiles = selectedFiles.map((file, index) => ({
          name: file.name,
          size: file.size,
          url: response.urls[index],
        }))

        setUploadedFiles((prev) => [...prev, ...newUploadedFiles])

        // 4) Remove `await` so if onUpload() is slow, we don't get stuck:
        onUpload(newUploadedFiles)

        setSelectedFiles([])
        resetFileInput()
        toast.success(t('uploadSuccess'))
      } catch (error) {
        console.error('[FileUpload] Error uploading files:', error)
        toast.error(t('uploadError'))
      } finally {
        console.log("[FileUpload] Reached 'finally'. Stopping spinner.")
        uploadingRef.current = false
        setIsUploading(false)
        onUploadEnd?.()
      }
    },
    [selectedFiles, uploadFilesMutation, onUpload, resetFileInput, onUploadStart, onUploadEnd, t]
  )

  // 5) Handler for file input changes
  const handleFileChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files
      if (files) {
        const newFiles: IUploadFile[] = Array.from(files).map((file) => ({
          name: file.name,
          size: file.size,
          file: file,
        }))

        if (variant === 'simple') {
          // For "simple" variant, keep just the first file
          setSelectedFiles([newFiles[0]])
          if (uploadOnSelect) {
            // Immediately start uploading
            setIsUploading(true)
            setTimeout(() => {
              handleUpload().catch((err) => {
                console.error('[FileUpload] handleUpload error:', err)
                setIsUploading(false)
                uploadingRef.current = false
              })
            }, 0)
          }
        } else {
          // For "full" variant, queue them all
          setSelectedFiles((prevFiles) => [...prevFiles, ...newFiles])
        }
      }
    },
    [variant, uploadOnSelect, handleUpload]
  )

  // 6) Drag-n-drop handlers
  const handleDragEnter = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    event.stopPropagation()
    setIsDragging(true)
  }

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    event.stopPropagation()
    setIsDragging(false)
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    event.stopPropagation()
  }

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    event.stopPropagation()
    setIsDragging(false)
    const files = event.dataTransfer.files
    if (files) {
      const newFiles: IUploadFile[] = Array.from(files).map((file) => ({
        name: file.name,
        size: file.size,
        file: file,
      }))
      if (variant === 'simple') {
        setSelectedFiles([newFiles[0]])
      } else {
        setSelectedFiles((prevFiles) => [...prevFiles, ...newFiles])
      }
    }
  }

  // 7) Handle deletion from server
  const handleDelete = useCallback(
    async (file: IFile) => {
      try {
        await deleteFileMutation.mutateAsync({ pathKey, fileName: file.name })
        setUploadedFiles((prev) => prev.filter((f) => f.name !== file.name))
        setSelectedFiles((prev) => prev.filter((f) => f.name !== file.name))
        resetFileInput()
        onDelete(file.name)
        toast.success('File deleted successfully')
      } catch (error) {
        console.error('[FileUpload] Error deleting file:', error)
        toast.error('Failed to delete file')
      }
    },
    [deleteFileMutation, pathKey, onDelete, resetFileInput]
  )

  // 8) Handle downloads
  const handleDownload = useCallback(
    (file: IUploadedFile) => {
      if (file.url) {
        window.open(file.url, '_blank')
      } else {
        downloadFile(pathKey, file.name)
      }
    },
    [downloadFile, pathKey]
  )

  // If initialFiles changes, reset the upload states:
  useUpdateEffect(() => {
    setIsUploading(false)
    uploadingRef.current = false
    setUploadedFiles(initialFiles || [])
  }, [initialFiles])

  const columns = useMemo(
    () => [
      {
        title: t('fileName'),
        field: 'name',
        render: (rowData: File) => (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span style={{ marginRight: '8px' }}>{rowData.name}</span>
            {!useMobileTable && uploadedFiles.some((f) => f.name === rowData.name) && (
              <span
                style={{
                  backgroundColor: '#4CAF50',
                  color: 'white',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '12px',
                }}
              >
                {t('uploaded')}
              </span>
            )}
          </div>
        ),
      },
      {
        title: t('actions'),
        render: (rowData: IFile) => {
          const isUploaded = uploadedFiles.some((f) => f.name === rowData.name)
          return (
            <div style={{ display: 'flex', gap: '8px' }}>
              {isUploaded && (
                <WButton
                  onClick={() => handleDownload(rowData as IUploadedFile)}
                  startIcon={
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18">
                      <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" fill="currentColor" />
                    </svg>
                  }
                  size="small"
                  variant="outlined"
                >
                  {t('download')}
                </WButton>
              )}
              <WButton
                disabled={disabled}
                onClick={() => handleDelete(rowData)}
                startIcon={
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18">
                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z" fill="currentColor" />
                  </svg>
                }
                size="small"
                color="secondary"
                variant="outlined"
              >
                {t('delete')}
              </WButton>
            </div>
          )
        },
      },
    ],
    [uploadedFiles, handleDelete, handleDownload, t, disabled, useMobileTable]
  )

  // Merge selected and already uploaded
  const allFiles = useMemo(() => [...selectedFiles, ...uploadedFiles], [selectedFiles, uploadedFiles])

  // --- RENDER LOGIC ---

  // (A) Simple variant
  const renderSimpleVariant = () => {
    const isFileSelected = selectedFiles.length > 0
    const isFileUploaded = uploadedFiles.length > 0 && !isUploading

    const handleRemove = () => {
      if (isFileUploaded) {
        handleDelete(uploadedFiles[0])
      } else {
        setSelectedFiles([])
        resetFileInput()
      }
    }

    return (
      <WCard variant="outlined" className={`file-upload-card ${className || ''}`}>
        <WCardContent className="file-upload-card-content">
          <WTypography variant="h6" className="file-upload-title">
            {title || t('fileUploadTitle')}
          </WTypography>
          <div className="file-upload-simple-container">
            <WTextField
              value={isFileUploaded ? uploadedFiles[0]?.name : selectedFiles[0]?.name || ''}
              placeholder={t('noFileSelected')}
              fullWidth
              variant="outlined"
              className="file-upload-simple-textfield" // Added class
              InputProps={{
                readOnly: true,
                endAdornment: (isFileSelected || isFileUploaded) && (
                  <WButton onClick={handleRemove} size="small" className="file-upload-remove-button" style={{ marginRight: '-10px' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18">
                      <path
                        d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                        fill="currentColor"
                      />
                    </svg>
                  </WButton>
                ),
              }}
            />
            <WButton
              variant="contained"
              color="primary"
              onClick={isFileSelected ? handleUpload : () => fileInputRef.current?.click()}
              disabled={disabled || isUploading}
              className="file-upload-simple-button" // Added class
            >
              {isFileSelected ? (
                isUploading ? (
                  <WCircularProgress size={24} color="inherit" />
                ) : (
                  t('upload')
                )
              ) : buttonText ? (
                buttonText
              ) : (
                t('selectFile')
              )}
            </WButton>
          </div>
          <input type="file" onChange={handleFileChange} style={{ display: 'none' }} ref={fileInputRef} disabled={disabled} />
        </WCardContent>
      </WCard>
    )
  }

  // (B) Full variant
  const renderFullVariant = () => (
    <WCard variant="outlined" className={`file-upload-card ${className || ''}`}>
      <WCardContent className="file-upload-card-content">
        <WTypography variant="h6" className="file-upload-title">
          {title || t('fileUploadTitle')}
        </WTypography>
        {!disabled && (
          <div
            ref={dropAreaRef}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            className={`file-upload-drop-area ${isDragging ? 'dragging' : ''}`} // Added class and dynamic class
            onClick={() => fileInputRef.current?.click()}
          >
            <input type="file" multiple onChange={handleFileChange} style={{ display: 'none' }} ref={fileInputRef} disabled={disabled} />
            <WTypography variant="h6" color="primary" className="file-upload-drop-area-title">
              {' '}
              {/* Added class */}
              {t('dropFilesHere')}
            </WTypography>
            <WButton
              variant="outlined"
              color="primary"
              disabled={disabled}
              className="file-upload-drop-area-button" // Added class
              startIcon={
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                  <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" fill="currentColor" />
                </svg>
              }
            >
              {t('selectFiles')}
            </WButton>
            <WTypography variant="body2" className="file-upload-drop-area-text">
              {' '}
              {/* Added class */}
              {t('dragAndDropText')}
            </WTypography>
          </div>
        )}
        {allFiles.length > 0 ? (
          <DigiTable
            columns={columns} // Assumes columns are defined with appropriate classNames or styles
            data={allFiles}
            filtering={false}
            search={true}
            loading={isUploading}
            mobileConfig={{
              titleFields: ['name'],
            }}
            headerStyle={
              {
                // These might be overridden by CSS, but kept for direct style if needed
                // backgroundColor: '#f5f5f5',
                // fontWeight: 'bold',
                // padding: '12px 8px',
              }
            }
          />
        ) : isUploading ? (
          <div className="file-upload-loading-container">
            {' '}
            {/* Added class */}
            <WCircularProgress size={24} color="primary" />
            <WTypography>{t('uploading')}</WTypography>
          </div>
        ) : null}
      </WCardContent>

      {selectedFiles.length > 0 && !uploadOnSelect && variant === 'full' && (
        <WCardActions className="file-upload-actions-container">
          {' '}
          {/* Added class */}
          <WButton
            variant="contained"
            color="primary"
            onClick={handleUpload}
            disabled={disabled || isUploading}
            className="file-upload-action-button" // Added class
            startIcon={
              isUploading ? (
                <WCircularProgress size={20} color="inherit" />
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                  <path d="M5 4v2h14V4H5zm0 10h4v6h6v-6h4l-7-7-7 7z" fill="currentColor" />
                </svg>
              )
            }
          >
            {isUploading ? t('uploading') : buttonText ? buttonText : t('uploadSelectedFiles')}
          </WButton>
        </WCardActions>
      )}
    </WCard>
  )

  return variant === 'simple' ? renderSimpleVariant() : renderFullVariant()
}

export default FileUpload
