/* FileUpload.css */
:root {
  --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --primary-color: #5c2d91;
  /* Purple */
  --primary-color-dark: #45226d;
  --primary-color-light: #b39ddb;
  --primary-color-lightest: #f3e5f5;
  --primary-color-light-transparent: rgba(92, 45, 145, 0.25);
  --primary-color-contrast: #fff;

  --secondary-color: #6c757d;
  /* Grey */
  --secondary-color-dark: #5a6268;
  --secondary-color-light: #adb5bd;

  --success-color: #28a745;
  /* Green */
  --error-color: #dc3545;
  /* Red */
  --warning-color: #ffc107;
  /* Yellow */
  --info-color: #17a2b8;
  /* Teal */

  --text-color: #212529;
  --text-color-secondary: #6c757d;
  --text-color-muted: #868e96;
  --link-color: var(--primary-color);

  --input-bg: #fff;
  --input-border-color: #ced4da;
  --input-focus-border-color: var(--primary-color-light);
  --input-focus-box-shadow: 0 0 0 0.2rem var(--primary-color-light-transparent);
  --input-placeholder-color: #6c757d;
  --input-padding-y: 0.5rem;
  /* 8px */
  --input-padding-x: 0.75rem;
  /* 12px */

  --disabled-bg: #e9ecef;
  --disabled-color: #6c757d;
  --disabled-border-color: #ced4da;

  --border-radius-small: 0.25rem;
  /* 4px */
  --border-radius-medium: 0.375rem;
  /* 6px */
  --border-radius-large: 0.5rem;
  /* 8px */

  --font-size-base: 1rem;
  /* 16px */
  --font-size-small: 0.875rem;
  /* 14px */
  --font-size-large: 1.25rem;
  /* 20px */

  --spacing-xs: 0.25rem;
  /* 4px */
  --spacing-sm: 0.5rem;
  /* 8px */
  --spacing-md: 1rem;
  /* 16px */
  --spacing-lg: 1.5rem;
  /* 24px */
  --spacing-xl: 2rem;
  /* 32px */

  --transition-speed-fast: 0.15s;
  --transition-speed-normal: 0.2s;

  --card-border-color: #dee2e6;
  --card-bg: #fff;
  --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);

  --table-header-bg: #f8f9fa;
  --table-border-color: #dee2e6;
  --table-row-hover-bg: #f1f3f5;

  --button-padding-y: 0.375rem;
  --button-padding-x: 0.75rem;
}

.file-upload-card {
  border: 1px solid var(--card-border-color);
  border-radius: var(--border-radius-medium);
  background-color: var(--card-bg);
  box-shadow: var(--card-shadow);
  margin-bottom: var(--spacing-md);
  font-family: var(--font-family);
}

.file-upload-card-content {
  padding: var(--spacing-md);
}

.file-upload-title {
  font-size: var(--font-size-large);
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--spacing-md);
}

/* Simple Variant Specific Styles */
.file-upload-simple-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.file-upload-simple-textfield .WTextField-input {
  background-color: var(--input-bg);
  border-radius: var(--border-radius-small);
  color: var(--text-color);
  font-size: var(--font-size-base);
}

.file-upload-simple-textfield .WTextField-input:read-only {
  background-color: var(--disabled-bg);
  cursor: default;
}

.file-upload-simple-button {
  padding: var(--button-padding-y) var(--button-padding-x);
  font-size: var(--font-size-base);
  min-width: 120px;
  /* Ensure button has a decent width */
  height: auto;
  /* Adjust based on WButton's default height or content */
}

.file-upload-remove-button .WButton-startIcon {
  margin-right: 0;
  /* Remove margin if only icon */
}

.file-upload-remove-button svg {
  fill: var(--text-color-secondary);
  transition: fill var(--transition-speed-fast);
}

.file-upload-remove-button:hover svg {
  fill: var(--error-color);
}

/* Full Variant Specific Styles */
.file-upload-drop-area {
  border: 2px dashed var(--input-border-color);
  border-radius: var(--border-radius-medium);
  padding: var(--spacing-lg);
  text-align: center;
  margin-bottom: var(--spacing-md);
  cursor: pointer;
  background-color: var(--input-bg);
  transition:
    border-color var(--transition-speed-normal),
    background-color var(--transition-speed-normal);
}

.file-upload-drop-area.dragging {
  border-color: var(--primary-color);
  background-color: var(--primary-color-lightest);
}

.file-upload-drop-area-title {
  font-size: var(--font-size-large);
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.file-upload-drop-area-button {
  margin-bottom: var(--spacing-sm);
}

.file-upload-drop-area-text {
  font-size: var(--font-size-small);
  color: var(--text-color-secondary);
  margin-top: var(--spacing-sm);
}

.file-upload-table .DigiTable-headerCell {
  background-color: var(--table-header-bg);
  font-weight: 600;
  color: var(--text-color);
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 2px solid var(--table-border-color);
}

.file-upload-table .DigiTable-row:hover {
  background-color: var(--table-row-hover-bg);
}

.file-upload-table .DigiTable-cell {
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--table-border-color);
  color: var(--text-color);
}

.file-upload-table-filename {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.file-upload-table-status-uploaded {
  background-color: var(--success-color);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-small);
  font-size: var(--font-size-small);
  font-weight: 500;
}

.file-upload-actions-container {
  display: flex;
  justify-content: flex-end;
  padding: var(--spacing-md);
  border-top: 1px solid var(--card-border-color);
}

.file-upload-action-button {
  margin-left: var(--spacing-sm);
}

.file-upload-action-button .WButton-startIcon svg {
  fill: currentColor;
}

/* Loading state */
.file-upload-loading-container {
  padding: var(--spacing-lg);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-color-secondary);
}

/* General disabled state for buttons within FileUpload */
.file-upload-card .WButton:disabled {
  background-color: var(--disabled-bg) !important;
  /* Use important if WFace styles are too specific */
  color: var(--disabled-color) !important;
  border-color: var(--disabled-border-color) !important;
  cursor: not-allowed;
  opacity: 0.7;
}

.file-upload-card .WButton:disabled .WCircularProgress-root {
  color: var(--disabled-color) !important;
}

/* Ensure icons in buttons adapt to button state */
.file-upload-card .WButton-startIcon svg,
.file-upload-card .WButton-endIcon svg {
  transition: fill var(--transition-speed-fast);
}

.file-upload-card .WButton:disabled .WButton-startIcon svg,
.file-upload-card .WButton:disabled .WButton-endIcon svg {
  fill: var(--disabled-color);
}
