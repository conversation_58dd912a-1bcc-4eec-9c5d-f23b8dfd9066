/* Filter Components Enhanced Styling */

/* Filter Button Enhancements */
.filter-button-container {
  display: flex;
  align-items: center;
  gap: 4px;
}

.filter-button-icon {
  transition: all 0.2s ease-in-out;
}

.filter-button-icon:hover {
  transform: scale(1.1);
}

.filter-button-active {
  color: #5c2d91 !important;
  filter: drop-shadow(0 1px 2px rgba(92, 45, 145, 0.2));
}

.filter-button-inactive {
  color: #64748b !important;
  transition: color 0.2s ease-in-out;
}

.filter-button-inactive:hover {
  color: #5c2d91 !important;
}

/* Sort Button Enhancements */
.sort-button-active {
  color: #5c2d91 !important;
  filter: drop-shadow(0 1px 2px rgba(92, 45, 145, 0.2));
}

.sort-button-inactive {
  color: #94a3b8 !important;
  opacity: 0.7;
  transition: all 0.2s ease-in-out;
}

.sort-button-inactive:hover {
  color: #5c2d91 !important;
  opacity: 1;
}

/* Filter Popover Enhancements */
.filter-popover {
  border-radius: 12px !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 10px 30px rgba(92, 45, 145, 0.15) !important;
  overflow: visible !important;
}

.filter-popover::before {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  left: 14px;
  width: 12px;
  height: 12px;
  background-color: white;
  border: 1px solid #e2e8f0;
  border-right: none;
  border-bottom: none;
  transform: translate(-50%, -50%) rotate(45deg);
  z-index: 0;
}

.filter-popover-content {
  padding: 24px;
  width: 320px;
  border-radius: 12px;
  background: var(--digi-surface, #ffffff);
  position: relative;
  z-index: 1;
}

.filter-popover-title {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #0f172a !important;
  margin-bottom: 16px !important;
}

.filter-popover-label {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #374151 !important;
  margin-bottom: 8px !important;
}

/* Form Field Enhancements */
.filter-form-field .MuiOutlinedInput-root {
  border-radius: 6px !important;
  font-size: 14px !important;
}

.filter-form-field .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
  border-color: #5c2d91 !important;
}

.filter-form-field .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #5c2d91 !important;
  border-width: 2px !important;
}

.filter-form-field .MuiInputLabel-root.Mui-focused {
  color: #5c2d91 !important;
}

/* Multi-select Chip Styling */
.filter-form-field .MuiChip-root {
  background-color: #f3eef8 !important;
  color: #5c2d91 !important;
  font-weight: 500 !important;
}

.filter-form-field .MuiChip-deleteIcon {
  color: #5c2d91 !important;
}

.filter-form-field .MuiChip-deleteIcon:hover {
  color: #45226d !important;
}

/* Button Styling */
.filter-apply-button {
  background-color: #5c2d91 !important;
  color: #ffffff !important;
  padding: 8px 20px !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  box-shadow: 0 2px 4px rgba(92, 45, 145, 0.2) !important;
  transition: all 0.2s ease-in-out !important;
}

.filter-apply-button:hover {
  background-color: #45226d !important;
  box-shadow: 0 4px 12px rgba(92, 45, 145, 0.3) !important;
}

.filter-apply-button:disabled {
  background-color: #e2e8f0 !important;
  color: #94a3b8 !important;
  box-shadow: none !important;
}

.filter-cancel-button {
  color: #64748b !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  border: 1px solid #e2e8f0 !important;
  background-color: #ffffff !important;
  transition: all 0.2s ease-in-out !important;
}

.filter-cancel-button:hover {
  background-color: #f8fafc !important;
  border-color: #cbd5e1 !important;
}

.filter-clear-button {
  color: #64748b !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  background-color: transparent !important;
  transition: all 0.2s ease-in-out !important;
}

.filter-clear-button:hover {
  background-color: #f1f5f9 !important;
  color: #475569 !important;
}

/* Mobile Filter Button */
.mobile-filter-button {
  background-color: #ffffff !important;
  color: #5c2d91 !important;
  border: 1px solid #5c2d91 !important;
  box-shadow: 0 1px 3px rgba(92, 45, 145, 0.1) !important;
  transition: all 0.2s ease-in-out !important;
}

.mobile-filter-button:hover {
  background-color: #f3eef8 !important;
  box-shadow: 0 2px 6px rgba(92, 45, 145, 0.15) !important;
}

.mobile-filter-button-active {
  background-color: #5c2d91 !important;
  color: #ffffff !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(92, 45, 145, 0.2) !important;
}

.mobile-filter-button-active:hover {
  background-color: #45226d !important;
  box-shadow: 0 4px 12px rgba(92, 45, 145, 0.3) !important;
}

/* Mobile Filter Dialog */
.mobile-filter-dialog {
  padding: 24px !important;
  background-color: #f8fafc !important;
}

.mobile-filter-section {
  margin-bottom: 24px !important;
  background-color: #ffffff !important;
  padding: 16px 10px 8px 10px !important;
  border-radius: 8px !important;
  box-shadow: 0 1px 3px rgba(92, 45, 145, 0.1) !important;
  border: 1px solid #f3eef8 !important;
}

/* Filter Chip Styling */
.filter-chip {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  background-color: #f3eef8 !important;
  border-radius: 8px !important;
  color: #5c2d91 !important;
  border: 1px solid #e2d7f1 !important;
  transition: all 0.2s ease-in-out !important;
}

.filter-chip:hover {
  background-color: #e2d7f1 !important;
}

.filter-chip-content {
  flex: 1 !important;
  padding: 8px 12px !important;
  margin-right: 8px !important;
}

.filter-chip-value {
  color: #45226d !important;
  font-size: 13px !important;
  font-weight: 500 !important;
}

.filter-chip-type {
  color: #7c3aed !important;
  font-size: 11px !important;
}

.filter-chip-close {
  color: #7c3aed !important;
  cursor: pointer !important;
  padding: 4px 16px !important;
  border-radius: 50% !important;
  transition: all 0.2s ease-in-out !important;
}

.filter-chip-close:hover {
  background-color: #e2d7f1 !important;
  color: #45226d !important;
}

/* Text Visibility Fixes */
.filter-popover-content .MuiTypography-root {
  color: #374151 !important;
}

.filter-popover-content .MuiFormLabel-root {
  color: #374151 !important;
}

.filter-popover-content .MuiInputBase-input {
  color: #1f2937 !important;
}

.filter-popover-content .MuiSelect-select {
  color: #1f2937 !important;
}

/* Dark theme support */
[data-theme='dark'] .filter-popover-content {
  background-color: #1e293b !important;
  color: #f8fafc !important;
}

[data-theme='dark'] .filter-popover-content .MuiTypography-root {
  color: #f8fafc !important;
}

[data-theme='dark'] .filter-popover-content .MuiFormLabel-root {
  color: #cbd5e1 !important;
}

[data-theme='dark'] .filter-popover-content .MuiInputBase-input {
  color: #f8fafc !important;
}

[data-theme='dark'] .filter-chip {
  background-color: #3e1a5b !important;
  color: #e2d7f1 !important;
  border-color: #5c2d91 !important;
}

[data-theme='dark'] .filter-chip-value {
  color: #f3eef8 !important;
}

/* Enhanced Icon Styling for Better Visibility */
.filter-button-container .MuiSvgIcon-root {
  transition: all 0.2s ease-in-out !important;
}

/* Active Filter Icon Styling */
.filter-button-active .MuiSvgIcon-root {
  color: #f8fafc !important;
  filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.4)) brightness(1.2) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  background: linear-gradient(135deg, #5c2d91 0%, #7c3aed 100%) !important;
  border-radius: 3px !important;
  padding: 2px !important;
  box-shadow: 0 2px 4px rgba(92, 45, 145, 0.3) !important;
  font-size: 18px !important;
}

/* Inactive Filter Icon Styling */
.filter-button-inactive .MuiSvgIcon-root {
  color: #e2e8f0 !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2)) brightness(1.1) !important;
  font-size: 18px !important;
}

.filter-button-inactive .MuiSvgIcon-root:hover {
  color: #f8fafc !important;
  filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.3)) brightness(1.3) !important;
  transform: scale(1.05) !important;
}

/* Sort Button Icon Styling */
.sort-button-active .MuiSvgIcon-root {
  color: #f8fafc !important;
  filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.4)) brightness(1.2) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  background: linear-gradient(135deg, #5c2d91 0%, #7c3aed 100%) !important;
  border-radius: 3px !important;
  padding: 2px !important;
  box-shadow: 0 2px 4px rgba(92, 45, 145, 0.3) !important;
  font-size: 18px !important;
}

.sort-button-inactive .MuiSvgIcon-root {
  color: #cbd5e1 !important;
  opacity: 0.8 !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)) brightness(1.1) !important;
  font-size: 18px !important;
}

.sort-button-inactive .MuiSvgIcon-root:hover {
  color: #f8fafc !important;
  opacity: 1 !important;
  filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.3)) brightness(1.3) !important;
  transform: scale(1.05) !important;
}

/* Button Container Styling */
.MuiButton-root.filter-icon-button {
  padding: 4px !important;
  min-width: auto !important;
  border-radius: 4px !important;
  background-color: transparent !important;
}

.MuiButton-root.filter-icon-button:hover {
  background-color: #f3eef8 !important;
}

.MuiButton-root.sort-icon-button {
  padding: 4px !important;
  min-width: auto !important;
  margin-left: 2px !important;
  border-radius: 4px !important;
  background-color: transparent !important;
}

.MuiButton-root.sort-icon-button:hover {
  background-color: #f3eef8 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .filter-popover-content {
    width: 280px !important;
    padding: 20px !important;
  }

  .filter-popover-title {
    font-size: 14px !important;
  }

  .filter-popover-label {
    font-size: 13px !important;
  }

  .filter-button-active .MuiSvgIcon-root,
  .filter-button-inactive .MuiSvgIcon-root,
  .sort-button-active .MuiSvgIcon-root,
  .sort-button-inactive .MuiSvgIcon-root {
    font-size: 16px !important;
  }

  .MuiButton-root.filter-icon-button,
  .MuiButton-root.sort-icon-button {
    padding: 3px !important;
  }
}

/* Ensure proper z-index for popover */
.MuiPopover-root {
  z-index: 1300 !important;
}

.MuiDialog-root {
  z-index: 1400 !important;
}

/* Enhanced Shiny Effect for Icons */
.filter-icon-shiny {
  position: relative;
  overflow: hidden;
}

.filter-icon-shiny::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: rotate(45deg);
  transition: all 0.3s ease;
  opacity: 0;
}

.filter-icon-shiny:hover::before {
  opacity: 1;
  animation: shine 0.6s ease-in-out;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }

  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* Enhanced Filter Button Visibility */
.filter-container .MuiButton-root {
  position: relative;
  overflow: visible;
}

/* Glow effect for active icons */
.filter-button-active .MuiSvgIcon-root,
.sort-button-active .MuiSvgIcon-root {
  position: relative;
}

.filter-button-active .MuiSvgIcon-root::after,
.sort-button-active .MuiSvgIcon-root::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  filter: blur(4px);
  opacity: 0.6;
  z-index: -1;
}
