import React, { useState, useMemo, useCallback } from 'react'
import { WB<PERSON>, WButton, WDialog, WTypography } from 'wface'
import { FilterList, Close, Search } from '@mui/icons-material'
import { SelectBox } from '@/components/formElements'
import DigiTextField from '@/components/formElements/DigiTextField/DigiTextField'
import { IOption } from '@/types'
import { Column } from '@/types/WFaceTypes'
import { useTranslation } from 'react-i18next'
import uniq from 'lodash/uniq'
import '../FilterComponents.css'

export interface FilterState {
  columnId: string
  type: string
  value: any
}

interface MobileFilterButtonProps {
  columns: Column<any>[]
  data: any[]
  onApplyFilters: (filters: FilterState[]) => void
  onSearch: (searchTerm: string) => void
  onSort: (sortConfig: { columnId: string; direction: 'asc' | 'desc' | null }) => void
  title?: string
  currentFilters?: FilterState[]
}

export const MobileFilterButton: React.FC<MobileFilterButtonProps> = ({
  columns,
  data,
  onApplyFilters,
  onSearch,
  onSort,
  title = 'Filters',
  currentFilters = [],
}) => {
  const { t, i18n } = useTranslation('tableFilters')
  const [isOpen, setIsOpen] = useState(false)
  const [selectedColumn, setSelectedColumn] = useState<IOption | null>(null)
  const [filterType, setFilterType] = useState<IOption | null>(null)
  const [filterValue, setFilterValue] = useState<any>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [activeFilters, setActiveFilters] = useState<FilterState[]>(currentFilters)
  const [selectedValues, setSelectedValues] = useState<IOption[]>([])
  const [sortColumn, setSortColumn] = useState<IOption | null>(null)
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc' | null>(null)

  // Check if current filter configuration is valid
  const isFilterValid = useMemo(() => {
    if (!selectedColumn || !filterType) return false
    if (filterType.value === 'selectMultiple' && selectedValues?.length === 0) return false
    if (filterType.value !== 'selectMultiple' && !filterValue) return false
    return true
  }, [selectedColumn, filterType, filterValue, selectedValues])

  // Check if current sort configuration is valid
  const isSortValid = useMemo(() => {
    return sortColumn && sortDirection
  }, [sortColumn, sortDirection])

  // Check if there are any changes to apply
  const hasChanges = useMemo(() => {
    return isFilterValid || isSortValid || searchTerm.length > 0
  }, [isFilterValid, isSortValid, searchTerm])

  // Create column options for select boxes
  const columnOptions: IOption[] = useMemo(
    () =>
      columns
        .filter((col) => !col.hidden && col.field !== 'actions')
        .map((col) => {
          const label = getColumnLabel(col.title)
          return {
            value: col.field as string,
            label: label,
            labelEn: label,
          }
        }),
    [columns]
  )

  // Helper function to extract label from col.title
  function getColumnLabel(colTitle: any): string {
    if (typeof colTitle === 'string') {
      return colTitle
    } else if (React.isValidElement(colTitle)) {
      const children = (colTitle as React.ReactElement<{ children?: React.ReactNode }>)?.props?.children ?? undefined
      if (typeof children === 'string') {
        return children
      } else if (Array.isArray(children)) {
        for (const child of children) {
          const label = getColumnLabel(child)
          if (label) return label
        }
      } else if (React.isValidElement(children)) {
        const label = getColumnLabel(children)
        if (label) return label
      }
    }
    return ''
  }

  // Determine column type and available values
  const columnInfo = useMemo(() => {
    if (!selectedColumn) return { type: 'string', uniqueValues: [] }

    const column = columns.find((col) => col.field === selectedColumn.value)
    const values = data.map((row) => row[selectedColumn.value])
    const uniqueValues = uniq(values)
      .filter((value) => value != null)
      .map((value) => ({
        value: value,
        label: String(value),
        labelEn: String(value),
      }))
      .sort((a, b) => (i18n.language == 'tr' ? a.label.localeCompare(b.label) : a.labelEn.localeCompare(b.labelEn)))

    return {
      type: column?.dateSetting?.type || typeof values[0] || 'string',
      uniqueValues,
    }
  }, [selectedColumn, columns, data])

  // Generate filter type options based on column type
  const filterTypes: IOption[] = useMemo(() => {
    if (!selectedColumn) return []

    if (columnInfo.type === 'number') {
      return [
        { value: 'equals', label: t('equals'), labelEn: 'Equals' },
        { value: 'doesntEqual', label: t('doesntEqual'), labelEn: "Doesn't Equal" },
        { value: 'isLessThan', label: t('isLessThan'), labelEn: 'Is Less Than' },
        { value: 'isGreaterThan', label: t('isGreaterThan'), labelEn: 'Is Greater Than' },
        { value: 'selectMultiple', label: t('selectMultiple'), labelEn: 'Select Multiple Values' },
      ]
    } else if (columnInfo.type === 'date') {
      return [
        { value: 'on', label: t('on'), labelEn: 'On' },
        { value: 'before', label: t('before'), labelEn: 'Before' },
        { value: 'after', label: t('after'), labelEn: 'After' },
        { value: 'between', label: t('between'), labelEn: 'Between' },
      ]
    }

    return [
      { value: 'contains', label: t('contains'), labelEn: 'Contains' },
      { value: 'equals', label: t('equals'), labelEn: 'Equals' },
      { value: 'startsWith', label: t('startsWith'), labelEn: 'Starts With' },
      { value: 'endsWith', label: t('endsWith'), labelEn: 'Ends With' },
      { value: 'selectMultiple', label: t('selectMultiple'), labelEn: 'Select Multiple Values' },
    ]
  }, [columnInfo.type, selectedColumn, t])

  const handleApplyFilter = useCallback(() => {
    // Handle Filters
    if (isFilterValid) {
      const newFilter: FilterState = {
        columnId: selectedColumn!.value,
        type: filterType!.value,
        value: filterType!.value === 'selectMultiple' ? selectedValues.map((v) => v.value) : filterValue,
      }

      const updatedFilters = [...activeFilters.filter((f) => f.columnId !== selectedColumn!.value), newFilter]
      setActiveFilters(updatedFilters)
      onApplyFilters(updatedFilters)
    }

    // Handle Sorting
    if (isSortValid) {
      onSort({ columnId: sortColumn!.value, direction: sortDirection! })
    } else {
      onSort({ columnId: '', direction: null })
    }

    // Handle Search
    if (searchTerm) {
      onSearch(searchTerm)
    }

    setIsOpen(false)
  }, [
    isFilterValid,
    isSortValid,
    selectedColumn,
    filterType,
    filterValue,
    selectedValues,
    activeFilters,
    onApplyFilters,
    sortColumn,
    sortDirection,
    searchTerm,
    onSort,
    onSearch,
  ])

  const handleSearch = useCallback(
    (value: string) => {
      setSearchTerm(value)
      onSearch(value)
    },
    [onSearch]
  )

  const getFilterValueDisplay = useCallback(
    (filter: FilterState) => {
      if (filter.type === 'selectMultiple') {
        const selectedOptions = columnInfo.uniqueValues.filter((option) => (filter.value as any[]).includes(option.value))
        return selectedOptions.map((opt) => opt.label).join(', ')
      }

      if (filter.type === 'between') {
        return `${filter.value.start} - ${filter.value.end}`
      }

      return String(filter.value)
    },
    [columnInfo.uniqueValues]
  )

  const handleRemoveFilter = useCallback(
    (index: number) => {
      const filterToRemove = activeFilters[index]

      // Clear form fields if they match the removed filter
      if (selectedColumn?.value === filterToRemove.columnId) {
        setSelectedColumn(null)
        setFilterType(null)
        setFilterValue(null)
        setSelectedValues([])
      }

      const updatedFilters = activeFilters.filter((_, i) => i !== index)
      setActiveFilters(updatedFilters)
      onApplyFilters(updatedFilters)
    },
    [activeFilters, selectedColumn, onApplyFilters]
  )

  const handleClearAll = useCallback(() => {
    setActiveFilters([])
    onApplyFilters([])
    setSearchTerm('')
    onSearch('')
    setSelectedColumn(null)
    setFilterType(null)
    setFilterValue(null)
    setSelectedValues([])
    setSortColumn(null)
    setSortDirection(null)
    onSort({ columnId: '', direction: null })
  }, [onApplyFilters, onSearch, onSort])

  const styles = {
    dialog: {
      padding: '24px',
      backgroundColor: '#f8fafc',
    },
    header: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: '24px',
      borderBottom: '1px solid #e2e8f0',
      paddingBottom: '16px',
    },
    searchContainer: {
      marginBottom: '24px',
    },
    buttonContainer: {
      display: 'flex',
      justifyContent: 'space-between',
      marginTop: '24px',
      gap: '12px',
    },
    applyButton: {
      backgroundColor: '#5c2d91',
      color: '#ffffff',
      padding: '8px 24px',
      borderRadius: '6px',
      fontSize: '14px',
      fontWeight: 600,
      boxShadow: '0 2px 4px rgba(92, 45, 145, 0.2)',
    },
    clearButton: {
      backgroundColor: '#ffffff',
      color: '#64748b',
      padding: '8px 24px',
      borderRadius: '6px',
      fontSize: '14px',
      fontWeight: 600,
      border: '1px solid #e2e8f0',
    },
    sortContainer: {
      marginBottom: '16px',
    },

    filterChip: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: '#f3eef8',
      borderRadius: '8px',
      fontSize: '12px',
      color: '#5c2d91',
      width: '100%',
      border: '1px solid #e2d7f1',
    },
    filterChipContent: {
      flex: 1,
      padding: '8px 12px',
      marginRight: '8px',
    },
    filterChipHeader: {
      display: 'flex',
      alignItems: 'center',
      gap: '6px',
      marginBottom: '4px',
    },
    filterType: {
      color: '#7c3aed',
      fontSize: '11px',
    },
    filterChipValue: {
      color: '#45226d',
      fontSize: '13px',
      fontWeight: 500,
      wordBreak: 'break-all' as const,
    },
    closeIcon: {
      marginLeft: 4,
      cursor: 'pointer',
      fontSize: 16,
      color: '#7c3aed',
      padding: '4px 16px',
      borderRadius: '50%',
    },
    activeFilters: {
      display: 'flex',
      flexDirection: 'column' as const,
      gap: '8px',
      marginTop: '12px',
    },
    section: {
      marginBottom: '24px',
      backgroundColor: '#ffffff',
      padding: '16px 10px 8px 10px',
      borderRadius: '8px',
      boxShadow: '0 1px 3px rgba(92, 45, 145, 0.1)',
      border: '1px solid #f3eef8',
    },
  }

  return (
    <>
      <WButton
        onClick={() => setIsOpen(true)}
        variant="contained"
        className={activeFilters.length > 0 ? 'mobile-filter-button-active' : 'mobile-filter-button'}
        style={{
          backgroundColor: activeFilters.length > 0 ? '#5c2d91' : '#ffffff',
          color: activeFilters.length > 0 ? '#ffffff' : '#5c2d91',
          marginRight: -1,
          textAlign: 'right',
          border: activeFilters.length > 0 ? 'none' : '1px solid #5c2d91',
          boxShadow: activeFilters.length > 0 ? '0 2px 8px rgba(92, 45, 145, 0.2)' : '0 1px 3px rgba(92, 45, 145, 0.1)',
        }}
        sx={{
          '&:hover': {
            backgroundColor: activeFilters.length > 0 ? '#45226d' : '#f3eef8',
          },
        }}
      >
        <FilterList
          sx={{
            fontSize: '18px',
            color: activeFilters.length > 0 ? '#f8fafc' : '#e2e8f0',
            marginRight: activeFilters.length > 0 ? '8px' : '0',
            filter:
              activeFilters.length > 0
                ? 'drop-shadow(0 1px 3px rgba(0, 0, 0, 0.4)) brightness(1.2)'
                : 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2)) brightness(1.1)',
            textShadow: activeFilters.length > 0 ? '0 1px 2px rgba(0, 0, 0, 0.3)' : 'none',
            background: activeFilters.length > 0 ? 'linear-gradient(135deg, #5c2d91 0%, #7c3aed 100%)' : 'transparent',
            borderRadius: activeFilters.length > 0 ? '3px' : '0',
            padding: activeFilters.length > 0 ? '2px' : '0',
            boxShadow: activeFilters.length > 0 ? '0 2px 4px rgba(92, 45, 145, 0.3)' : 'none',
          }}
        />
        {activeFilters.length > 0 && ` (${activeFilters.length})`}
      </WButton>

      <WDialog
        open={isOpen}
        onClose={() => setIsOpen(false)}
        fullScreen
        style={{
          position: 'fixed',
          zIndex: 1000000,
        }}
      >
        <WBox style={styles.dialog}>
          <div style={styles.header}>
            <WTypography variant="h6">{title}</WTypography>
            <WButton
              onClick={() => setIsOpen(false)}
              style={{
                marginRight: -10,
              }}
            >
              <Close />
            </WButton>
          </div>

          <div style={styles.searchContainer}>
            <DigiTextField
              fullWidth
              placeholder={t('search')}
              value={searchTerm}
              onChange={(e) => handleSearch(e)}
              InputProps={{
                startAdornment: <Search />,
              }}
              label={t('search')}
            />
          </div>

          {activeFilters.length > 0 && (
            <div style={styles.section}>
              <WTypography variant="subtitle2" gutterBottom>
                {t('activeFilters')}
              </WTypography>
              <div style={styles.activeFilters}>
                {activeFilters.map((filter, index) => {
                  const columnLabel = columnOptions.find((col) => col.value === filter.columnId)?.label
                  const filterTypeLabel = filterTypes.find((type) => type.value === filter.type)?.label

                  return (
                    <div key={index} style={styles.filterChip}>
                      <div style={styles.filterChipContent}>
                        <div style={styles.filterChipHeader}>
                          <strong>{columnLabel}</strong>
                          <span style={styles.filterType}>({filterTypeLabel})</span>
                        </div>
                        <div style={styles.filterChipValue}>{getFilterValueDisplay(filter)}</div>
                      </div>
                      <Close
                        style={styles.closeIcon}
                        onClick={(e) => {
                          e.stopPropagation()
                          handleRemoveFilter(index)
                        }}
                      />
                    </div>
                  )
                })}
              </div>
            </div>
          )}

          {/* Filter Form */}
          <div style={styles.section}>
            <WTypography variant="subtitle2" mb={2} gutterBottom>
              {t('filterBy')}
            </WTypography>
            <SelectBox
              label={t('selectColumn')}
              value={selectedColumn}
              searchable
              options={columnOptions}
              onChange={(option) => {
                setSelectedColumn(option as IOption)
                setFilterType(null)
                setFilterValue(null)
                setSelectedValues([])
              }}
              fullWidth
            />
            {selectedColumn && (
              <SelectBox
                label={t('filterType')}
                value={filterType}
                searchable
                options={filterTypes}
                style={{ margin: '20px 0' }}
                onChange={(option) => {
                  setFilterType(option as IOption)
                  setFilterValue(null)
                  setSelectedValues([])
                }}
                fullWidth
              />
            )}

            {filterType && (
              <WBox my={2}>
                {filterType.value === 'selectMultiple' ? (
                  <SelectBox
                    label={t('selectValues')}
                    value={selectedValues}
                    options={columnInfo.uniqueValues}
                    onChange={(options) => setSelectedValues(options as IOption[])}
                    multiple
                    defaultText={null}
                    fullWidth
                  />
                ) : columnInfo.type === 'date' ? (
                  filterType.value === 'between' ? (
                    <WBox display="flex" gap={2} my={4}>
                      <DigiTextField
                        type="date"
                        label={t('startDate')}
                        value={filterValue?.start || ''}
                        onChange={(e) => setFilterValue({ ...filterValue, start: e })}
                        fullWidth
                      />
                      <DigiTextField
                        type="date"
                        label={t('endDate')}
                        value={filterValue?.end || ''}
                        onChange={(e) => setFilterValue({ ...filterValue, end: e })}
                        fullWidth
                      />
                    </WBox>
                  ) : (
                    <DigiTextField type="date" label={t('value')} value={filterValue || ''} onChange={(e) => setFilterValue(e)} fullWidth />
                  )
                ) : (
                  <DigiTextField
                    label={t('value')}
                    value={filterValue || ''}
                    onChange={(e) => setFilterValue(e)}
                    type={columnInfo.type === 'number' ? 'number' : 'text'}
                    fullWidth
                  />
                )}
              </WBox>
            )}
          </div>

          {/* Sorting Options */}
          <div style={styles.sortContainer}>
            <WTypography variant="subtitle2" mb={2} gutterBottom>
              {t('sortBy')}
            </WTypography>
            <SelectBox
              label={t('selectSortColumn')}
              value={sortColumn}
              options={columnOptions}
              onChange={(option) => {
                setSortColumn(option as IOption)
              }}
              fullWidth
            />
            <SelectBox
              label={t('sortDirection')}
              value={sortDirection ? { value: sortDirection, label: t(sortDirection), labelEn: t(sortDirection) } : null}
              options={[
                { value: 'asc', label: t('asc'), labelEn: t('asc') },
                { value: 'desc', label: t('desc'), labelEn: t('desc') },
              ]}
              style={{ marginTop: 16 }}
              onChange={(option: any) => setSortDirection(option ? (option.value as 'asc' | 'desc') : null)}
              fullWidth
            />
          </div>

          <WBox display="flex" justifyContent="space-between" mt={2}>
            <WButton
              onClick={handleClearAll}
              style={styles.clearButton}
              sx={{
                '&:hover': {
                  backgroundColor: '#f8fafc',
                  borderColor: '#cbd5e1',
                },
              }}
            >
              {t('clearAll')}
            </WButton>
            <WButton
              variant="contained"
              onClick={handleApplyFilter}
              disabled={!hasChanges}
              style={styles.applyButton}
              sx={{
                '&:hover': {
                  backgroundColor: '#45226d',
                  boxShadow: '0 4px 12px rgba(92, 45, 145, 0.3)',
                },
                '&:disabled': {
                  backgroundColor: '#e2e8f0',
                  color: '#94a3b8',
                  boxShadow: 'none',
                },
              }}
            >
              {t('apply')}
            </WButton>
          </WBox>
        </WBox>
      </WDialog>
    </>
  )
}

export default MobileFilterButton
