import { useMemo } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import NavigationButtons from './NavigateButtons'
import { useUpdateEffect } from '@/hooks'
import { isValidInteger } from '@/utils/helpers/validation'
import { useWebView } from '@/contexts/WebViewContext'
import './HeaderComponent.css'

function HeaderComponent() {
  const { isWebView } = useWebView()
  const navigate = useNavigate()
  const location = useLocation()
  const searchParams = useMemo(() => new URLSearchParams(location.search), [location.search])

  const loginId = useMemo(() => searchParams.get('loginId'), [searchParams])

  const setQueryParams = (newParam: any) => {
    const searchParams = new URLSearchParams(location.search)

    // Add new parameter
    Object.keys(newParam).forEach((key) => {
      searchParams.set(key, newParam[key])
    })

    navigate({ search: searchParams.toString() }, { replace: true })
  }

  const removeFromQueryParams = (param: string) => {
    const searchParams = new URLSearchParams(location.search)
    searchParams.delete(param)
    navigate({ search: searchParams.toString() }, { replace: true })
  }

  useUpdateEffect(() => {
    if (loginId && isValidInteger(loginId)) {
      setQueryParams({ loginId })
    } else {
      removeFromQueryParams('loginId')
    }
  }, [loginId])

  // Hide HeaderComponent completely in WebView (mobile app)
  // Show only in web browsers and mobile browsers
  if (isWebView) {
    return null
  }

  return (
    <div className="header-container">
      <div className="header-appbar">
        <NavigationButtons />
      </div>
    </div>
  )
}

export default HeaderComponent
