:root {
  --header-background-color: var(--digi-color-background-surface, #ffffff);
  --header-padding: var(--digi-spacing-medium, 16px) var(--digi-spacing-large, 24px);
  --header-border-radius: var(--digi-border-radius-large, 8px);
  --header-shadow: var(--digi-shadow-medium, 0 4px 12px rgba(0, 0, 0, 0.08));
  --appbar-background-color: var(--digi-color-primary-main, #5c2d91);
  /* Or a neutral color like --digi-color-background-neutral-subtle */
  --appbar-border-radius: var(--digi-border-radius-medium, 4px);
  --appbar-margin-bottom: var(--digi-spacing-small, 8px);
  --appbar-text-color: var(--digi-color-text-on-primary, #ffffff);
}

.header-container {
  display: flex;
  width: 100%;
  padding: var(--header-padding);
  flex-direction: column;
  margin-top: 50px;
  background-color: var(--header-background-color);
  border-radius: var(--header-border-radius);
  box-shadow: var(--header-shadow);
  box-sizing: border-box;
  /* Ensures padding doesn't add to width/height */
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .header-container {
    padding: 12px 16px;
    border-radius: 6px;
    margin: 8px;
    width: calc(100% - 16px);
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 8px 12px;
    border-radius: 4px;
    margin: 4px;
    width: calc(100% - 8px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.header-appbar {
  flex: 1;
  border-radius: var(--appbar-border-radius) !important;
  /* Important to override inline style if needed */
  margin-bottom: var(--appbar-margin-bottom) !important;
  /* Important to override inline style if needed */
  background-color: var(--appbar-background-color) !important;
  /* Ensure this takes precedence */
  color: var(--appbar-text-color) !important;
  padding: 0 var(--digi-spacing-medium, 16px);
  /* Add some padding to appbar content */
  box-shadow: var(--digi-shadow-small, 0 2px 4px rgba(0, 0, 0, 0.05));
  max-height: 48px;
  display: flex;
  align-items: center;
}

/* Mobile responsive styles for appbar */
@media (max-width: 768px) {
  .header-appbar {
    padding: 0 12px;
    min-height: 44px;
    border-radius: 4px !important;
    margin-bottom: 6px !important;
  }
}

@media (max-width: 480px) {
  .header-appbar {
    padding: 0 8px;
    min-height: 40px;
    border-radius: 3px !important;
    margin-bottom: 4px !important;
  }
}

/* Styling for NavigationButtons if it's a direct child or needs specific context */
.header-appbar .navigation-buttons-container {
  /* Assuming NavigationButtons has a root container with this class */
  display: flex;
  align-items: center;
  height: 100%;
}

/* Styling for UserChangeControl/UserChangeControlWorkflow if needed */
.user-change-control-wrapper {
  margin-top: var(--digi-spacing-medium, 16px);
  /* Add any specific layout or spacing styles for the user change component here */
}
