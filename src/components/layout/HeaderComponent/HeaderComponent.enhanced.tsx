import { useMemo } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import NavigationButtons from './NavigateButtons'
import { useUpdateEffect } from '@/hooks'
import { isValidInteger } from '@/utils/helpers/validation'
import { useWebView } from '@/contexts/WebViewContext'
import { Card } from '@/components/UI/Card/Card'
import { Bell, Settings, User, Search } from 'lucide-react'

function EnhancedHeaderComponent() {
  const { t } = useTranslation('common')
  const { isWebView } = useWebView()
  const navigate = useNavigate()
  const location = useLocation()
  const searchParams = useMemo(() => new URLSearchParams(location.search), [location.search])

  const loginId = useMemo(() => searchParams.get('loginId'), [searchParams])

  const setQueryParams = (newParam: any) => {
    const searchParams = new URLSearchParams(location.search)

    // Add new parameter
    Object.keys(newParam).forEach((key) => {
      searchParams.set(key, newParam[key])
    })

    navigate({ search: searchParams.toString() }, { replace: true })
  }

  const removeFromQueryParams = (param: string) => {
    const searchParams = new URLSearchParams(location.search)
    searchParams.delete(param)
    navigate({ search: searchParams.toString() }, { replace: true })
  }

  useUpdateEffect(() => {
    if (loginId && isValidInteger(loginId)) {
      setQueryParams({ loginId })
    } else {
      removeFromQueryParams('loginId')
    }
  }, [loginId])

  // Hide HeaderComponent completely in WebView (mobile app)
  if (isWebView) {
    return null
  }

  return (
    <div className="sticky top-0 z-50 w-full">
      {/* Main Header */}
      <Card variant="elevated" className="mx-4 mt-4 mb-2 rounded-xl shadow-lg border-0 bg-white/95 backdrop-blur-md">
        <div className="flex items-center justify-between p-4">
          {/* Logo/Brand Section */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-purple-600 to-purple-700 flex items-center justify-center">
                <span className="text-white font-bold text-lg">D</span>
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">{t('workflow_management_title')}</h1>
                <p className="text-sm text-gray-500">{t('workflow_management_subtitle')}</p>
              </div>
            </div>
          </div>

          {/* Search Bar - Hidden on mobile */}
          <div className="hidden md:flex flex-1 max-w-md mx-8">
            <div className="relative w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder={t('search_placeholder')}
                className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg bg-gray-50 focus:bg-white focus:border-purple-500 focus:ring-2 focus:ring-purple-200 focus:outline-none transition-all duration-200"
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            {/* Notifications */}
            <button
              className="relative p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-all duration-200"
              aria-label={t('notifications')}
            >
              <Bell className="w-5 h-5" />
              <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
            </button>

            {/* Settings */}
            <button
              className="p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-all duration-200"
              aria-label={t('settings')}
            >
              <Settings className="w-5 h-5" />
            </button>

            {/* User Menu */}
            <button
              className="flex items-center gap-2 p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-all duration-200"
              aria-label={t('user_menu')}
            >
              <div className="w-8 h-8 bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg flex items-center justify-center">
                <User className="w-4 h-4 text-white" />
              </div>
              <span className="hidden sm:block text-sm font-medium">John Doe</span>
            </button>
          </div>
        </div>

        {/* Navigation Section */}
        <div className="border-t border-gray-100 px-4 py-3">
          <NavigationButtons />
        </div>
      </Card>

      {/* Mobile Search - Shows below header on mobile */}
      <div className="md:hidden mx-4 mb-2">
        <Card variant="ghost" className="p-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder={t('search_placeholder_mobile')}
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg bg-gray-50 focus:bg-white focus:border-purple-500 focus:ring-2 focus:ring-purple-200 focus:outline-none transition-all duration-200"
            />
          </div>
        </Card>
      </div>
    </div>
  )
}

export default EnhancedHeaderComponent