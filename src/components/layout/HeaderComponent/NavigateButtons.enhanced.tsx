import { useState, useEffect, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import useMediaQuery from '@/hooks/useMediaQuery'
import useAppHelper from '@/services/wface/appHelper'
import {
  ChevronRight,
  Menu,
  X,
  Inbox,
  History,
  Pause,
  Settings,
  UserCheck,
  UserMinus,
  BarChart3,
  UserX
} from 'lucide-react'
import { Button } from '@/components/UI/Button/Button'

// Navigation item configuration with icons
const navigationItems = [
  {
    id: 'inbox',
    key: 'inbox',
    icon: Inbox,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50'
  },
  {
    id: 'history',
    key: 'history',
    icon: History,
    color: 'text-green-600',
    bgColor: 'bg-green-50'
  },
  {
    id: 'suspended-inbox',
    key: 'suspended_inbox',
    icon: Pause,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50'
  },
  {
    id: 'workflow-management',
    key: 'workflow_management',
    icon: Settings,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50'
  },
  {
    id: 'workflow',
    key: 'request_delegation',
    params: new URLSearchParams({ name: 'delegation' }),
    icon: UserCheck,
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-50'
  },
  {
    id: 'end-delegation',
    key: 'end_delegation',
    icon: UserMinus,
    color: 'text-red-600',
    bgColor: 'bg-red-50'
  },
  {
    id: 'workflow',
    key: 'request_monitoring',
    params: new URLSearchParams({ name: 'monitoring' }),
    icon: BarChart3,
    color: 'text-cyan-600',
    bgColor: 'bg-cyan-50'
  },
  {
    id: 'end-monitoring',
    key: 'end_monitoring',
    icon: UserX,
    color: 'text-gray-600',
    bgColor: 'bg-gray-50'
  },
]

const EnhancedNavigationButtons = () => {
  const { t } = useTranslation('common')
  const { openScreen } = useAppHelper()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isMobileView, setIsMobileView] = useState(false)

  const isLargeScreen = useMediaQuery('(min-width: 1400px)')
  const isMediumScreen = useMediaQuery('(min-width: 1200px)')
  const isSmallScreen = useMediaQuery('(min-width: 900px)')
  const isMobile = useMediaQuery('(max-width: 899px)')

  const handleOpenScreen = useCallback(
    (id: string, params?: URLSearchParams) => {
      openScreen(id, params)
    },
    [openScreen]
  )

  useEffect(() => {
    setIsMobileView(isMobile)
    setIsLoading(false)
  }, [isMobile])

  const handleOpenModal = () => setIsModalOpen(true)
  const handleCloseModal = () => setIsModalOpen(false)

  const getGridCols = () => {
    if (isLargeScreen) return 'grid-cols-8'
    if (isMediumScreen) return 'grid-cols-6'
    if (isSmallScreen) return 'grid-cols-4'
    return 'grid-cols-2'
  }

  const DesktopNavigation = () => (
    <div className={`grid ${getGridCols()} gap-3`}>
      {navigationItems.map((item: any, index) => {
        const IconComponent = item.icon

        return (
          <button
            key={index}
            onClick={() => handleOpenScreen(item.id, item.params)}
            className="group relative flex flex-col items-center p-3 rounded-lg border border-gray-100 bg-white hover:bg-gray-50 hover:border-gray-200 hover:shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-1"
          >
            {/* Icon with colored background */}
            <div className={`w-10 h-10 ${item.bgColor} rounded-lg flex items-center justify-center mb-2 group-hover:scale-110 transition-transform duration-200`}>
              <IconComponent className={`w-5 h-5 ${item.color}`} />
            </div>

            {/* Label */}
            <span className="text-xs font-medium text-gray-700 text-center leading-tight group-hover:text-gray-900 transition-colors duration-200">
              {t(item.key)}
            </span>

            {/* Hover effect indicator */}
            <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-purple-500/0 to-purple-600/0 group-hover:from-purple-500/5 group-hover:to-purple-600/5 transition-all duration-200" />
          </button>
        )
      })}
    </div>
  )

  const MobileNavigation = () => (
    <>
      <Button
        variant="ghost"
        size="sm"
        onClick={handleOpenModal}
        icon={<Menu className="w-5 h-5" />}
        className="bg-purple-50 text-purple-700 hover:bg-purple-100"
      >
        {t('menu')}
      </Button>

      {/* Full Screen Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 z-[9999] bg-black/20 backdrop-blur-sm">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-600 via-purple-700 to-indigo-800">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-white/10">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-lg bg-white/20 backdrop-blur-sm flex items-center justify-center">
                  <span className="text-white font-bold text-lg">D</span>
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-white">{t('navigation')}</h2>
                  <p className="text-purple-200 text-sm">{t('choose_destination')}</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCloseModal}
                icon={<X className="w-5 h-5" />}
                className="text-white hover:bg-white/10"
              />
            </div>

            {/* Navigation Grid */}
            <div className="p-6 pt-8">
              <div className="grid grid-cols-2 gap-4 max-w-md mx-auto">
                {navigationItems.map((item, index) => {
                  const IconComponent = item.icon

                  return (
                    <button
                      key={index}
                      onClick={() => {
                        handleOpenScreen(item.id, item.params)
                        handleCloseModal()
                      }}
                      className="group relative flex flex-col items-center p-6 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 hover:border-white/30 hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white/50"
                    >
                      {/* Icon */}
                      <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center mb-3 group-hover:bg-white/30 transition-all duration-300">
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>

                      {/* Label */}
                      <span className="text-sm font-medium text-white text-center leading-tight">
                        {t(item.key)}
                      </span>

                      {/* Arrow indicator */}
                      <ChevronRight className="w-4 h-4 text-white/60 mt-1 group-hover:text-white group-hover:translate-x-1 transition-all duration-300" />
                    </button>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="w-6 h-6 border-2 border-purple-600 border-t-transparent rounded-full animate-spin" />
      </div>
    )
  }

  return isMobileView ? <MobileNavigation /> : <DesktopNavigation />
}

export default EnhancedNavigationButtons