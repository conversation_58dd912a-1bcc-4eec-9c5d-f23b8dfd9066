:root {
  --footer-background-color: var(--digi-color-background-neutral-subtle, #f8f9fa);
  --footer-text-color: var(--digi-color-text-secondary, #6c757d);
  --footer-padding: var(--digi-spacing-medium, 16px);
  --footer-font-size: var(--digi-font-size-small, 0.875rem);
  --footer-border-top: 1px solid var(--digi-color-border-neutral-soft, #dee2e6);
}

.footer-container {
  background-color: var(--footer-background-color);
  color: var(--footer-text-color);
  padding: var(--footer-padding);
  text-align: center;
  font-size: var(--footer-font-size);
  border-top: var(--footer-border-top);
  /* Removed WGrid specific styles like margin, using padding on container instead */
}

.footer-content {
  /* If you need specific styling for the text div inside */
  display: inline-block;
  /* To center it properly if text-align: center is on parent */
}
