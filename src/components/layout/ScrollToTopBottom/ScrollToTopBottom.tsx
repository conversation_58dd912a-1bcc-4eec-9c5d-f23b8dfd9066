import React, { useState, useEffect, useCallback } from 'react'
import { ChevronUp, ChevronDown } from 'lucide-react'

const ScrollToTopBottom: React.FC = () => {
  const [isAtBottom, setIsAtBottom] = useState(false)
  const [isVisible, setIsVisible] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  const getContainer = () => {
    return document.querySelector('main > div:nth-child(2) > div:first-child')
  }

  const checkScrollPosition = useCallback(() => {
    const container = getContainer()
    if (!container) return

    try {
      const { scrollTop, scrollHeight, clientHeight } = container as HTMLElement

      // Only show button if there's enough content to scroll
      const hasScrollableContent = scrollHeight > clientHeight
      setIsVisible(hasScrollableContent)

      if (hasScrollableContent) {
        // Add some tolerance to prevent floating point issues
        const scrollThreshold = 50 // 50px from bottom
        const isBottom = scrollHeight - scrollTop - clientHeight <= scrollThreshold
        setIsAtBottom(isBottom)
      }
    } catch (error) {
      console.error('Error checking scroll position:', error)
      setIsVisible(false)
    }
  }, [])

  useEffect(() => {
    const container = getContainer()
    if (!container) return

    // Debounced scroll handler for better performance
    let scrollTimeout: NodeJS.Timeout
    const debouncedScroll = () => {
      if (scrollTimeout) clearTimeout(scrollTimeout)
      scrollTimeout = setTimeout(checkScrollPosition, 100)
    }

    // Initial check after a short delay to ensure container is properly rendered
    const initialCheckTimeout = setTimeout(checkScrollPosition, 200)

    // Add event listeners
    container.addEventListener('scroll', debouncedScroll, { passive: true })
    window.addEventListener('resize', debouncedScroll, { passive: true })

    // Clean up
    return () => {
      if (initialCheckTimeout) clearTimeout(initialCheckTimeout)
      if (scrollTimeout) clearTimeout(scrollTimeout)
      container.removeEventListener('scroll', debouncedScroll)
      window.removeEventListener('resize', debouncedScroll)
    }
  }, [checkScrollPosition])

  const handleClick = useCallback(() => {
    const container = getContainer()
    if (!container) return

    try {
      const targetScroll = isAtBottom ? 0 : (container as HTMLElement).scrollHeight - (container as HTMLElement).clientHeight

      container.scrollTo({
        top: targetScroll,
        behavior: 'smooth',
      })
    } catch (error) {
      console.error('Error scrolling:', error)
    }
  }, [isAtBottom])

  // Don't render if there's nothing to scroll
  if (!isVisible) return null

  const buttonStyles: React.CSSProperties = {
    minWidth: 'unset',
    width: '40px',
    height: '40px',
    borderRadius: '50%',
    padding: 0,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: isHovered ? '#012ea8' : '#01248a',
    boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
    transition: 'all 0.2s ease',
    transform: isHovered ? 'scale(1.05)' : 'scale(1)',
    border: 'none',
    cursor: 'pointer',
    color: '#ffffff',
  }

  const containerStyles: React.CSSProperties = {
    position: 'absolute',
    bottom: '24px',
    right: '24px',
    zIndex: 100000,
  }

  return (
    <div style={containerStyles}>
      <button
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        style={buttonStyles}
        aria-label={isAtBottom ? 'Scroll to top' : 'Scroll to bottom'}
      >
        {isAtBottom ? <ChevronUp size={24} /> : <ChevronDown size={24} />}
      </button>
    </div>
  )
}

export default ScrollToTopBottom
