import React from 'react'

export interface Localization {
  error?: React.ReactNode
  body?: {
    dateTimePickerLocalization?: object // The date-fns locale object applied to the datepickers
    emptyDataSourceMessage?: React.ReactNode
    filterRow?: {
      filterPlaceHolder?: React.ReactNode
      filterTooltip?: React.ReactNode
    }
    editRow?: {
      saveTooltip?: React.ReactNode
      cancelTooltip?: React.ReactNode
      deleteText?: React.ReactNode
    }
    addTooltip?: React.ReactNode
    deleteTooltip?: React.ReactNode
    editTooltip?: React.ReactNode
  }
  header?: {
    actions?: React.ReactNode
  }
  grouping?: {
    groupedBy?: React.ReactNode
    placeholder?: React.ReactNode
  }
  pagination?: {
    firstTooltip?: React.ReactNode
    firstAriaLabel?: string
    previousTooltip?: React.ReactNode
    previousAriaLabel?: string
    nextTooltip?: React.ReactNode
    nextAriaLabel?: string
    labelDisplayedRows?: React.ReactNode
    labelRowsPerPage?: React.ReactNode
    lastTooltip?: React.ReactNode
    lastAriaLabel?: string
    labelRowsSelect?: React.ReactNode
  }
  toolbar?: {
    addRemoveColumns?: React.ReactNode
    nRowsSelected?: React.ReactNode | ((rowCount: number) => React.ReactNode)
    showColumnsTitle?: React.ReactNode
    showColumnsAriaLabel?: string
    exportTitle?: React.ReactNode
    exportAriaLabel?: string
    exportCSVName?: React.ReactNode
    exportPDFName?: React.ReactNode
    searchTooltip?: React.ReactNode
    searchPlaceholder?: React.ReactNode
    searchAriaLabel?: string
    clearSearchAriaLabel?: string
  }
}
