import { Localization } from './LocalizationInterface' // Adjust the import path as needed

export const enLocalization: Localization = {
  error: 'An error occurred.',
  body: {
    // dateTimePickerLocalization: removed date-fns dependency
    emptyDataSourceMessage: 'No records to display',
    filterRow: {
      filterPlaceHolder: 'Filter',
      filterTooltip: 'Filter',
    },
    editRow: {
      saveTooltip: 'Save',
      cancelTooltip: 'Cancel',
      deleteText: 'Are you sure you want to delete this row?',
    },
    addTooltip: 'Add',
    deleteTooltip: 'Delete',
    editTooltip: 'Edit',
  },
  header: {
    actions: 'Actions',
  },
  grouping: {
    groupedBy: 'Grouped by:',
    placeholder: 'Drag a column header here to group by that column',
  },
  pagination: {
    firstTooltip: 'First Page',
    firstAriaLabel: 'first page',
    previousTooltip: 'Previous Page',
    previousAriaLabel: 'previous page',
    nextTooltip: 'Next Page',
    nextAriaLabel: 'next page',
    labelDisplayedRows: '{from}-{to} of {count}',
    labelRowsPerPage: 'Rows per page:',
    lastTooltip: 'Last Page',
    lastAriaLabel: 'last page',
    labelRowsSelect: 'rows',
  },
  toolbar: {
    addRemoveColumns: 'Add or remove columns',
    nRowsSelected: '{0} row(s) selected',
    showColumnsTitle: 'Show Columns',
    showColumnsAriaLabel: 'Show Columns',
    exportTitle: 'Export',
    exportAriaLabel: 'Export',
    exportCSVName: 'Export as CSV',
    exportPDFName: 'Export as PDF',
    searchTooltip: 'Search',
    searchPlaceholder: 'Search',
    searchAriaLabel: 'Search',
    clearSearchAriaLabel: 'Clear Search',
  },
}
