import { Localization } from './LocalizationInterface' // Adjust the import path as needed

export const trLocalization: Localization = {
  error: 'Bir hata oluştu.',
  body: {
    // dateTimePickerLocalization: removed date-fns dependency
    emptyDataSourceMessage: 'Gösterilecek kayıt bulunamadı',
    filterRow: {
      filterPlaceHolder: 'Filtrele',
      filterTooltip: 'Filtrele',
    },
    editRow: {
      saveTooltip: 'Kaydet',
      cancelTooltip: 'İptal',
      deleteText: 'Bu satırı silmek istediğinizden emin misiniz?',
    },
    addTooltip: 'Ekle',
    deleteTooltip: 'Sil',
    editTooltip: 'Düzenle',
  },
  header: {
    actions: '<PERSON><PERSON><PERSON>ler',
  },
  grouping: {
    groupedBy: 'Gruplandırıldı:',
    placeholder: 'Bu sütunu gruplamak için buraya sürükleyin',
  },
  pagination: {
    firstTooltip: 'İlk Sayfa',
    firstAriaLabel: 'ilk sayfa',
    previousTooltip: '<PERSON>nce<PERSON> Sayfa',
    previousAriaLabel: 'önceki sayfa',
    nextTooltip: 'Sonraki Sayfa',
    nextAriaLabel: 'sonraki sayfa',
    labelDisplayedRows: '{from}-{to} / {count}',
    labelRowsPerPage: 'Sayfa başına satır:',
    lastTooltip: 'Son Sayfa',
    lastAriaLabel: 'son sayfa',
    labelRowsSelect: 'satır',
  },
  toolbar: {
    addRemoveColumns: 'Sütun ekle veya çıkar',
    nRowsSelected: '{0} satır seçildi',
    showColumnsTitle: 'Sütunları Göster',
    showColumnsAriaLabel: 'Sütunları Göster',
    exportTitle: 'Dışa Aktar',
    exportAriaLabel: 'Dışa Aktar',
    exportCSVName: 'CSV olarak dışa aktar',
    exportPDFName: 'PDF olarak dışa aktar',
    searchTooltip: 'Ara',
    searchPlaceholder: 'Ara',
    searchAriaLabel: 'Ara',
    clearSearchAriaLabel: 'Aramayı temizle',
  },
}
