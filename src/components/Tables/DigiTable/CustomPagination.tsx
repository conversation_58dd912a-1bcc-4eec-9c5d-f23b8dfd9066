import React, { useCallback, useMemo } from 'react'
import { WBox, WButton, WIcon, WTypography } from 'wface'
import { IOption } from '@/types'
import { SelectBox } from '@/components/formElements'
import useMediaQuery from '@/hooks/useMediaQuery'
import { useTranslation } from 'react-i18next'

interface PaginationProps {
  page: number
  count: number
  rowsPerPage: number
  rowsPerPageOptions: number[]
  onPageChange: (event: unknown, newPage: number) => void
  onRowsPerPageChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  className?: string
  disabled?: boolean
  showFirstLastPageButtons?: boolean
  colSpan?: number
  isInTable?: boolean
}

export default function CustomPagination({
  page,
  count,
  rowsPerPage,
  onPageChange,
  onRowsPerPageChange,
  rowsPerPageOptions,
  className,
  disabled = false,
  showFirstLastPageButtons = true,
  colSpan,
  isInTable = false,
}: PaginationProps) {
  const isMobile = useMediaQuery('(max-width: 901px)')
  const { i18n } = useTranslation()
  const totalPages = Math.ceil(count / rowsPerPage)
  const currentPage = page + 1

  const handlePageChange = useCallback(
    (newPage: number) => {
      if (newPage >= 0 && newPage < totalPages) {
        onPageChange(null, newPage)
      }
    },
    [onPageChange, totalPages]
  )

  const handleRowsPerPageChange = useCallback(
    (option: IOption | IOption[] | null) => {
      if (option && !Array.isArray(option)) {
        const syntheticEvent = {
          target: {
            value: option.value,
          },
        } as React.ChangeEvent<HTMLInputElement>
        onRowsPerPageChange(syntheticEvent)
      }
    },
    [onRowsPerPageChange]
  )

  const rowsPerPageSelectOptions: IOption[] = useMemo(
    () =>
      rowsPerPageOptions.map((option) => ({
        value: option,
        label: option.toString(),
        labelEn: option.toString(),
      })),
    [rowsPerPageOptions]
  )

  const selectedRowsPerPage: IOption = useMemo(
    () => ({
      value: rowsPerPage,
      label: rowsPerPage.toString(),
      labelEn: rowsPerPage.toString(),
    }),
    [rowsPerPage]
  )

  const isOnFirstPage = page === 0
  const isOnLastPage = page >= totalPages - 1

  const getPageNumbers = useCallback((): (number | 'ellipsis')[] => {
    const pageNumbers: (number | 'ellipsis')[] = []

    if (totalPages <= 5) {
      return Array.from({ length: totalPages }, (_, i) => i + 1)
    }

    if (currentPage <= 3) {
      pageNumbers.push(1, 2, 3, 'ellipsis', totalPages)
    } else if (currentPage >= totalPages - 2) {
      pageNumbers.push(1, 'ellipsis', totalPages - 2, totalPages - 1, totalPages)
    } else {
      pageNumbers.push(1, 'ellipsis', currentPage, 'ellipsis', totalPages)
    }

    return pageNumbers
  }, [currentPage, totalPages])

  const styles = {
    container: {
      display: 'flex',
      flexDirection: isMobile ? ('column-reverse' as const) : ('row' as const),
      alignItems: isMobile ? 'stretch' : 'center',
      justifyContent: 'flex-end',
      padding: isMobile ? '12px' : '16px',
      gap: isMobile ? '16px' : '0',
      backgroundColor: '#ffffff',
      borderTop: '1px solid #e0e0e0',
      fontFamily: "'Roboto', sans-serif",
      position: 'sticky' as const,
      bottom: 0,
      left: 0,
      right: 0,
      zIndex: 2,
    },
    rows: {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 15,
      gap: '8px',
      minWidth: isMobile ? 'auto' : '100px',
      width: isMobile ? '100%' : '200px',
    },
    controls: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: isMobile ? 0 : '4px',
      margin: isMobile ? '0 auto' : '',
      width: isMobile ? 0 : 'auto',
    },
    numbers: {
      display: 'flex',
      alignItems: 'center',
      gap: isMobile ? '2px' : '4px',
      margin: isMobile ? '0 4px' : '0 8px',
    },
    button: {
      minWidth: isMobile ? '24px' : '32px',
      maxWidth: isMobile ? '24px' : undefined,
      height: isMobile ? '24px' : '32px',
      padding: '0',
      borderRadius: '4px',
    },
    ellipsis: {
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      width: isMobile ? '36px' : '32px',
      height: isMobile ? '36px' : '32px',
      color: '#757575',
      userSelect: 'none' as const,
    },
    pageInfo: {
      width: '100%',
      display: isMobile ? 'flex' : 'none',
      justifyContent: 'center',
      alignItems: 'center',
      fontSize: '13px',
      color: '#666',
      marginTop: '4px',
    },
  }

  const paginationContent = (
    <>
      <div style={styles.rows}>
        <WTypography variant="body2" width={isMobile ? 'auto' : 154} marginTop={isMobile ? 1 : 0} style={{ fontSize: isMobile ? '13px' : 'inherit' }}>
          {i18n.language === 'tr' ? 'Sayfa başına satır:' : 'Rows per page:'}
        </WTypography>
        <WBox width={isMobile ? 80 : 100} style={{ alignContent: 'flex-end' }}>
          <SelectBox
            size="small"
            label=""
            defaultText={null}
            value={selectedRowsPerPage}
            options={rowsPerPageSelectOptions}
            onChange={handleRowsPerPageChange}
            disabled={disabled}
            fullWidth={false}
            searchable={false}
            sx={{
              '& .MuiSelect-select': {
                padding: isMobile ? '4px 8px' : '8px 12px',
                fontSize: isMobile ? '13px' : '14px',
              },
            }}
          />
        </WBox>
      </div>
      <div style={styles.controls}>
        {showFirstLastPageButtons && (
          <WButton style={styles.button} onClick={() => handlePageChange(0)} disabled={isOnFirstPage || disabled}>
            <WIcon>first_page</WIcon>
          </WButton>
        )}

        <WButton style={styles.button} onClick={() => handlePageChange(page - 1)} disabled={isOnFirstPage || disabled}>
          <WIcon>chevron_left</WIcon>
        </WButton>

        <div style={styles.numbers}>
          {getPageNumbers().map((pageNum, index) => {
            if (pageNum === 'ellipsis') {
              return (
                <span key={`ellipsis-${index}`} style={styles.ellipsis}>
                  &hellip;
                </span>
              )
            }

            return (
              <WButton
                key={pageNum}
                style={{
                  ...styles.button,
                  backgroundColor: pageNum === currentPage ? '#01248a' : 'transparent',
                  color: pageNum === currentPage ? '#ffffff' : '#666',
                }}
                onClick={() => handlePageChange((pageNum as number) - 1)}
                disabled={disabled}
              >
                {pageNum}
              </WButton>
            )
          })}
        </div>

        <WButton style={styles.button} onClick={() => handlePageChange(page + 1)} disabled={isOnLastPage || disabled}>
          <WIcon>chevron_right</WIcon>
        </WButton>

        {showFirstLastPageButtons && (
          <WButton style={styles.button} onClick={() => handlePageChange(totalPages - 1)} disabled={isOnLastPage || disabled}>
            <WIcon>last_page</WIcon>
          </WButton>
        )}
      </div>
    </>
  )

  if (isInTable) {
    return (
      <td className={`${className} pagination-container`} style={styles.container} colSpan={colSpan}>
        {paginationContent}
      </td>
    )
  }

  return (
    <div className={`${className} pagination-container`} style={styles.container}>
      {paginationContent}
    </div>
  )
}
