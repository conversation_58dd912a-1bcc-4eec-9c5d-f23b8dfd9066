import React from 'react'

interface StatusBadgeProps {
  status: string
  variant?: 'status' | 'priority'
  size?: 'small' | 'medium' | 'large'
  className?: string
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status, variant = 'status', size = 'medium', className = '' }) => {
  const normalizedStatus = status?.toLowerCase()?.trim() || 'unknown'

  const getStatusClass = () => {
    if (variant === 'priority') {
      switch (normalizedStatus) {
        case 'high':
        case 'yüksek':
        case 'critical':
        case 'kritik':
          return 'digi-priority-badge digi-priority-high'
        case 'medium':
        case 'orta':
        case 'normal':
          return 'digi-priority-badge digi-priority-medium'
        case 'low':
        case 'düşük':
        case 'minor':
          return 'digi-priority-badge digi-priority-low'
        default:
          return 'digi-priority-badge digi-priority-medium'
      }
    } else {
      switch (normalizedStatus) {
        case 'pending':
        case 'bekliyor':
        case 'waiting':
        case 'açık':
        case 'open':
          return 'digi-status-badge digi-status-pending'
        case 'approved':
        case 'onaylandı':
        case 'accepted':
        case 'kabul':
        case 'active':
        case 'aktif':
          return 'digi-status-badge digi-status-approved'
        case 'rejected':
        case 'reddedildi':
        case 'denied':
        case 'red':
        case 'inactive':
        case 'pasif':
          return 'digi-status-badge digi-status-rejected'
        case 'completed':
        case 'tamamlandı':
        case 'finished':
        case 'done':
        case 'bitti':
        case 'closed':
        case 'kapalı':
          return 'digi-status-badge digi-status-completed'
        case 'draft':
        case 'taslak':
        case 'temporary':
        case 'geçici':
          return 'digi-status-badge digi-status-draft'
        case 'in-progress':
        case 'progress':
        case 'devam-ediyor':
        case 'işleniyor':
        case 'processing':
          return 'digi-status-badge digi-status-in-progress'
        default:
          return 'digi-status-badge digi-status-pending'
      }
    }
  }

  const getSizeClass = () => {
    switch (size) {
      case 'small':
        return 'digi-badge-small'
      case 'large':
        return 'digi-badge-large'
      default:
        return 'digi-badge-medium'
    }
  }

  return <span className={`${getStatusClass()} ${getSizeClass()} ${className}`}>{status}</span>
}

export default StatusBadge
