/* DigiDatePicker.css */
.digi-date-picker {
  position: relative;
  width: 100%;
  display: inline-block;
}

.digi-date-picker-input {
  position: relative;
  width: 100%;
}

.digi-date-picker-popper {
  /* Removed position: absolute to let the popper handle positioning */
  z-index: 1300;
  margin-top: 8px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  animation: datepickerFadeIn 0.2s ease-out;
}

@keyframes datepickerFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Size Variations */
.digi-date-picker.small .MuiInputBase-root {
  min-height: 32px;
  font-size: 12px;
}

.digi-date-picker.medium .MuiInputBase-root {
  min-height: 40px;
  font-size: 14px;
}

.digi-date-picker.large .MuiInputBase-root {
  min-height: 48px;
  font-size: 16px;
}

/* Custom Calendar Styles */
.digi-date-picker .MuiPickersDay-root {
  font-size: 0.875rem;
}

.digi-date-picker .MuiPickersDay-root.Mui-selected {
  background-color: var(--primary-color);
  color: white;
}

.digi-date-picker .MuiPickersDay-root:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.digi-date-picker .MuiPickersDay-root.Mui-selected:hover {
  background-color: var(--primary-color-dark);
}

/* Error States */
.digi-date-picker.error .MuiOutlinedInput-root {
  border-color: var(--error-color);
}

.digi-date-picker.error .MuiFormHelperText-root {
  color: var(--error-color);
}

/* Disabled States */
.digi-date-picker.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Focus States */
.digi-date-picker .MuiOutlinedInput-root.Mui-focused {
  border-color: var(--primary-color);
}

/* Mobile Responsive Styles */
@media (max-width: 600px) {
  .digi-date-picker-popper {
    position: fixed !important;
    top: auto !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    transform: none !important;
    max-height: 80vh;
    border-radius: 12px 12px 0 0;
    margin: 0 !important;
    animation: slideUpMobile 0.3s ease-out;
  }

  @keyframes slideUpMobile {
    from {
      transform: translateY(100%);
    }

    to {
      transform: translateY(0);
    }
  }
}

#digiTable,
#digiTableWithHeader {
  table-layout: fixed !important;
}

#digiTable table,
#digiTableWithHeader table {
  table-layout: fixed !important;
  width: 100% !important;
}

/* Ensure text doesn't overflow */
#digiTable th,
#digiTableWithHeader th,
#digiTable td,
#digiTableWithHeader td {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
