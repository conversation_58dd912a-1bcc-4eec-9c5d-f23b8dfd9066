/* Enhanced DigiTable Styles - Modern Design System */

/* CSS Custom Properties for Theme Support */
:root {
  /* Colors */
  --digi-primary: #5c2d91;
  --digi-primary-dark: #45226d;
  --digi-primary-light: #e2d7f1;
  --digi-secondary: #64748b;
  --digi-success: #10b981;
  --digi-warning: #f59e0b;
  --digi-error: #ef4444;
  --digi-info: #06b6d4;

  /* Surface Colors */
  --digi-surface: #ffffff;
  --digi-surface-variant: #f8fafc;
  --digi-surface-hover: #f1f5f9;
  --digi-surface-selected: #f3eef8;
  --digi-border: #e2e8f0;
  --digi-border-light: #f1f5f9;

  /* Text Colors */
  --digi-text-primary: #0f172a;
  --digi-text-secondary: #475569;
  --digi-text-muted: #94a3b8;
  --digi-text-inverse: #ffffff;
  /* Spacing */
  --digi-spacing-xs: 4px;
  --digi-spacing-sm: 8px;
  --digi-spacing-md: 16px;
  --digi-spacing-lg: 24px;
  --digi-spacing-xl: 32px;
  --digi-spacing-2xl: 48px;
  --digi-spacing-3xl: 64px;

  /* Numbered spacing for specific use cases */
  --digi-spacing-1: 4px;
  --digi-spacing-2: 8px;
  --digi-spacing-3: 12px;
  --digi-spacing-4: 16px;
  --digi-spacing-5: 20px;
  --digi-spacing-6: 24px;
  --digi-spacing-7: 28px;
  --digi-spacing-8: 32px;
  --digi-spacing-10: 40px;
  --digi-spacing-12: 48px;
  --digi-spacing-16: 64px;

  /* Border Radius */
  --digi-radius-sm: 6px;
  --digi-radius-md: 8px;
  --digi-radius-lg: 12px;
  --digi-radius-xl: 16px;

  /* Shadows */
  --digi-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --digi-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --digi-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --digi-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Typography */
  --digi-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --digi-font-size-xs: 0.75rem;
  --digi-font-size-sm: 0.875rem;
  --digi-font-size-base: 1rem;
  --digi-font-size-lg: 1.125rem;
  --digi-font-size-xl: 1.25rem;

  /* Transitions */
  --digi-transition-fast: 150ms ease-in-out;
  --digi-transition-base: 200ms ease-in-out;
  --digi-transition-slow: 300ms ease-in-out;
}

/* Dark Theme Support */
[data-theme='dark'] {
  --digi-surface: #1e293b;
  --digi-surface-variant: #334155;
  --digi-surface-hover: #475569;
  --digi-surface-selected: #3e1a5b;
  --digi-border: #475569;
  --digi-border-light: #334155;
  --digi-text-primary: #f8fafc;
  --digi-text-secondary: #cbd5e1;
  --digi-text-muted: #64748b;
}

/* Enhanced Table Container */
.digi-table-enhanced {
  font-family: var(--digi-font-family);
  background: var(--digi-surface);
  border-radius: var(--digi-radius-xl);
  box-shadow: var(--digi-shadow-lg);
  overflow: hidden;
  border: 1px solid var(--digi-border);
  transition: all var(--digi-transition-base);
}

.digi-table-enhanced:hover {
  box-shadow: var(--digi-shadow-xl);
}

/* Enhanced Table Header */
.digi-table-header {
  background: linear-gradient(135deg, var(--digi-surface-variant) 0%, var(--digi-surface) 100%);
  border-bottom: 2px solid var(--digi-border);
  padding: var(--digi-spacing-lg);
}

.digi-table-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--digi-spacing-md);
}

.digi-table-title h2 {
  font-size: var(--digi-font-size-xl);
  font-weight: 600;
  color: var(--digi-text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--digi-spacing-sm);
}

.digi-table-title-icon {
  padding: var(--digi-spacing-sm);
  background: var(--digi-primary-light);
  border-radius: var(--digi-radius-md);
  color: var(--digi-primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Enhanced Search and Filter Bar */
.digi-table-controls {
  display: flex;
  align-items: center;
  gap: var(--digi-spacing-md);
  flex-wrap: wrap;
}

.digi-search-container {
  position: relative;
  flex: 1;
  min-width: 280px;
}

.digi-search-input {
  width: 100%;
  padding: var(--digi-spacing-md) var(--digi-spacing-md) var(--digi-spacing-md) 3rem;
  border: 2px solid var(--digi-border);
  border-radius: var(--digi-radius-lg);
  background: var(--digi-surface);
  color: var(--digi-text-primary);
  font-size: var(--digi-font-size-base);
  transition: all var(--digi-transition-base);
  outline: none;
}

.digi-search-input:focus {
  border-color: var(--digi-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.digi-search-icon {
  position: absolute;
  left: var(--digi-spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--digi-text-muted);
  transition: color var(--digi-transition-base);
}

.digi-search-input:focus + .digi-search-icon {
  color: var(--digi-primary);
}

/* Enhanced Filter Buttons - Better Shape with Original Colors */
.digi-filter-controls {
  display: flex;
  align-items: center;
  gap: var(--digi-spacing-sm);
}

.digi-filter-button {
  padding: var(--digi-spacing-3) var(--digi-spacing-4);
  border: 2px solid var(--digi-border);
  border-radius: var(--digi-radius-lg);
  background: var(--digi-surface);
  color: var(--digi-text-secondary);
  font-size: var(--digi-font-size-sm);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--digi-transition-base);
  display: flex;
  align-items: center;
  gap: var(--digi-spacing-2);
  white-space: nowrap;
  box-shadow: var(--digi-shadow-sm);
  position: relative;
  overflow: hidden;
  min-height: 38px;
}

.digi-filter-button:hover {
  border-color: var(--digi-primary);
  color: var(--digi-primary);
  transform: translateY(-2px);
  box-shadow: var(--digi-shadow-lg);
}

.digi-filter-button.active {
  background: var(--digi-primary);
  border-color: var(--digi-primary);
  color: var(--digi-text-inverse);
  box-shadow: var(--digi-shadow-md);
  transform: translateY(-1px);
}

.digi-filter-button.active:hover {
  background: var(--digi-primary-dark);
  border-color: var(--digi-primary-dark);
  transform: translateY(-3px);
  box-shadow: var(--digi-shadow-xl);
}

/* Filter Button Icons */
.digi-filter-button .MuiSvgIcon-root {
  font-size: 18px;
  transition: transform var(--digi-transition-base);
}

.digi-filter-button:hover .MuiSvgIcon-root {
  transform: scale(1.1);
}

.digi-filter-button.active .MuiSvgIcon-root {
  transform: scale(1.05);
}

/* Column Filter Buttons (in table headers) */
.MuiTableCell-root .digi-filter-button {
  padding: var(--digi-spacing-1) var(--digi-spacing-2);
  min-height: 28px;
  font-size: var(--digi-font-size-xs);
  border-radius: var(--digi-radius-sm);
}

.MuiTableCell-root .digi-filter-button .MuiSvgIcon-root {
  font-size: 14px;
}

/* Enhanced Action Buttons */
.digi-action-buttons {
  display: flex;
  align-items: center;
  gap: var(--digi-spacing-sm);
}

.digi-action-button {
  padding: var(--digi-spacing-md);
  border: none;
  border-radius: var(--digi-radius-md);
  background: var(--digi-surface-variant);
  color: var(--digi-text-secondary);
  cursor: pointer;
  transition: all var(--digi-transition-base);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  height: 44px;
}

.digi-action-button:hover {
  background: var(--digi-primary-light);
  color: var(--digi-primary);
  transform: translateY(-1px);
  box-shadow: var(--digi-shadow-md);
}

.digi-action-button.primary {
  background: var(--digi-primary);
  color: var(--digi-text-inverse);
}

.digi-action-button.primary:hover {
  background: var(--digi-primary-dark);
}

/* Enhanced Table Styles */
.digi-table-container {
  overflow: hidden;
  position: relative;
}

.digi-table-wrapper {
  overflow-x: auto;
  overflow-y: hidden;
}

/* Custom Scrollbar */
.digi-table-wrapper::-webkit-scrollbar {
  height: 8px;
}

.digi-table-wrapper::-webkit-scrollbar-track {
  background: var(--digi-surface-variant);
  border-radius: var(--digi-radius-sm);
}

.digi-table-wrapper::-webkit-scrollbar-thumb {
  background: var(--digi-border);
  border-radius: var(--digi-radius-sm);
  transition: background var(--digi-transition-base);
}

.digi-table-wrapper::-webkit-scrollbar-thumb:hover {
  background: var(--digi-text-muted);
}

/* Enhanced Table Headers */
.digi-table-enhanced thead th {
  background: linear-gradient(135deg, var(--digi-surface-variant) 0%, var(--digi-surface) 100%);
  color: var(--digi-text-primary);
  font-weight: 600;
  font-size: var(--digi-font-size-sm);
  padding: var(--digi-spacing-lg) var(--digi-spacing-md);
  border-bottom: 2px solid var(--digi-border);
  text-align: left;
  position: sticky;
  top: 0;
  z-index: 10;
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* Improved Table Header Content Layout */
.digi-table-enhanced thead th .MuiTableSortLabel-root {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: var(--digi-spacing-sm);
  min-height: 24px;
}

.digi-table-enhanced thead th .MuiTableSortLabel-root .MuiTableSortLabel-icon {
  margin-left: var(--digi-spacing-xs);
  margin-right: 0;
}

/* Table Header Text Spacing */
.digi-table-enhanced thead th span {
  flex: 1;
  text-align: left;
  padding-right: var(--digi-spacing-sm);
  line-height: 1.4;
}

/* Column Header Filter Button Spacing */
.digi-table-container .MuiTableCell-head .MuiIconButton-root {
  background: var(--digi-surface-variant) !important;
  border: 1px solid var(--digi-border) !important;
  color: var(--digi-text-secondary) !important;
  width: 28px !important;
  height: 28px !important;
  padding: 4px !important;
  margin: 0 2px 0 6px !important;
  flex-shrink: 0;
}

/* Header Content Container */
.digi-table-enhanced thead th > div {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: var(--digi-spacing-sm);
}

/* Header Text Container */
.digi-table-enhanced thead th .header-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: var(--digi-spacing-sm);
}

/* Header Actions Container */
.digi-table-enhanced thead th .header-actions {
  display: flex;
  align-items: center;
  gap: var(--digi-spacing-xs);
  flex-shrink: 0;
}

/* Enhanced Table Cells */
.digi-table-enhanced tbody td {
  padding: var(--digi-spacing-lg) var(--digi-spacing-md);
  border-bottom: 1px solid var(--digi-border-light);
  color: var(--digi-text-primary);
  font-size: var(--digi-font-size-sm);
  vertical-align: middle;
  transition: all var(--digi-transition-fast);
}

/* Enhanced Row Hover Effects */
.digi-table-enhanced tbody tr {
  transition: all var(--digi-transition-fast);
  cursor: pointer;
}

.digi-table-enhanced tbody tr:hover {
  background: var(--digi-surface-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.digi-table-enhanced tbody tr:hover td {
  border-color: var(--digi-border);
}

/* Enhanced Row Selection */
.digi-table-enhanced tbody tr.selected {
  background: var(--digi-surface-selected);
  border-left: 4px solid var(--digi-primary);
}

.digi-table-enhanced tbody tr.selected td {
  border-color: var(--digi-primary-light);
}

/* Enhanced Status Badges */
.digi-status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--digi-spacing-xs) var(--digi-spacing-sm);
  border-radius: var(--digi-radius-sm);
  font-size: var(--digi-font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  min-width: 60px;
  justify-content: center;
}

.digi-status-badge.pending {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fbbf24;
}

.digi-status-badge.approved {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #10b981;
}

.digi-status-badge.rejected {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #ef4444;
}

.digi-status-badge.completed {
  background: #e2d7f1;
  color: #45226d;
  border: 1px solid #5c2d91;
}

/* Enhanced Priority Indicators */
.digi-priority-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--digi-spacing-xs);
  font-size: var(--digi-font-size-sm);
  font-weight: 500;
}

.digi-priority-indicator.high {
  color: var(--digi-error);
}

.digi-priority-indicator.medium {
  color: var(--digi-warning);
}

.digi-priority-indicator.low {
  color: var(--digi-success);
}

.digi-priority-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

/* Enhanced Pagination */
.digi-pagination-enhanced {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--digi-spacing-lg);
  border-top: 1px solid var(--digi-border);
  background: var(--digi-surface-variant);
}

.digi-pagination-info {
  color: var(--digi-text-secondary);
  font-size: var(--digi-font-size-sm);
  font-weight: 500;
}

.digi-pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--digi-spacing-sm);
}

.digi-pagination-button {
  padding: var(--digi-spacing-sm) var(--digi-spacing-md);
  border: 1px solid var(--digi-border);
  border-radius: var(--digi-radius-md);
  background: var(--digi-surface);
  color: var(--digi-text-secondary);
  cursor: pointer;
  transition: all var(--digi-transition-base);
  font-size: var(--digi-font-size-sm);
  font-weight: 500;
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.digi-pagination-button:hover:not(:disabled) {
  border-color: var(--digi-primary);
  color: var(--digi-primary);
  background: var(--digi-primary-light);
}

.digi-pagination-button.active {
  background: var(--digi-primary);
  border-color: var(--digi-primary);
  color: var(--digi-text-inverse);
}

.digi-pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Enhanced Loading States */
.digi-loading-skeleton {
  background: linear-gradient(90deg, var(--digi-surface-variant) 25%, var(--digi-surface-hover) 50%, var(--digi-surface-variant) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--digi-radius-sm);
  height: 20px;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}

.digi-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  backdrop-filter: blur(2px);
}

.digi-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--digi-border);
  border-top: 3px solid var(--digi-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Enhanced Empty State */
.digi-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--digi-spacing-xl);
  text-align: center;
  color: var(--digi-text-muted);
}

.digi-empty-state-icon {
  width: 64px;
  height: 64px;
  margin-bottom: var(--digi-spacing-lg);
  color: var(--digi-text-muted);
}

.digi-empty-state-title {
  font-size: var(--digi-font-size-lg);
  font-weight: 600;
  color: var(--digi-text-secondary);
  margin-bottom: var(--digi-spacing-sm);
}

.digi-empty-state-description {
  font-size: var(--digi-font-size-sm);
  color: var(--digi-text-muted);
  max-width: 400px;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
  .digi-table-header {
    padding: var(--digi-spacing-md);
  }

  .digi-table-title {
    flex-direction: column;
    align-items: stretch;
    gap: var(--digi-spacing-md);
  }

  .digi-table-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .digi-search-container {
    min-width: 100%;
  }

  .digi-filter-controls,
  .digi-action-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }

  .digi-pagination-enhanced {
    flex-direction: column;
    gap: var(--digi-spacing-md);
    text-align: center;
  }

  .digi-table-enhanced {
    border-radius: var(--digi-radius-md);
    margin: var(--digi-spacing-md);
  }
}

/* Mobile Table Enhancements */
.digi-mobile-container {
  display: flex;
  flex-direction: column;
  gap: var(--digi-spacing-4);
  padding: var(--digi-spacing-4);
  background-color: var(--digi-color-background);
  min-height: 100vh;
}

.digi-mobile-header {
  position: sticky;
  top: 0;
  z-index: var(--digi-z-index-sticky);
  background-color: var(--digi-color-background);
  padding: var(--digi-spacing-2) 0;
  border-bottom: 1px solid var(--digi-color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--digi-spacing-2);
}

.digi-mobile-title {
  font-size: var(--digi-font-size-lg);
  font-weight: var(--digi-font-weight-bold);
  color: var(--digi-text-primary);
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  line-height: 1.2;
}

.digi-mobile-card {
  background-color: var(--digi-color-surface);
  border-radius: var(--digi-border-radius);
  box-shadow: var(--digi-shadow-sm);
  border: 1px solid var(--digi-color-border);
  overflow: hidden;
  transition: all var(--digi-transition-fast);
  animation: var(--digi-animation-fade-in);
}

.digi-mobile-card:hover {
  box-shadow: var(--digi-shadow-md);
  transform: translateY(-1px);
}

.digi-mobile-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--digi-spacing-4);
  background-color: var(--digi-color-surface);
  border-bottom: 1px solid var(--digi-color-border);
  cursor: pointer;
  transition: background-color var(--digi-transition-fast);
}

.digi-mobile-card-header:hover {
  background-color: var(--digi-color-surface-hover);
}

.digi-mobile-main-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--digi-spacing-1);
}

.digi-mobile-card-title {
  font-size: var(--digi-font-size-base);
  font-weight: var(--digi-font-weight-semibold);
  color: var(--digi-color-text-primary);
  line-height: var(--digi-line-height-tight);
}

.digi-mobile-card-subtitle {
  font-size: var(--digi-font-size-sm);
  color: var(--digi-color-text-secondary);
  display: flex;
  align-items: center;
  gap: var(--digi-spacing-2);
  flex-wrap: wrap;
}

.digi-mobile-card-right {
  display: flex;
  align-items: center;
  gap: var(--digi-spacing-3);
}

.digi-mobile-card-right-text {
  font-size: var(--digi-font-size-sm);
  color: var(--digi-color-text-secondary);
  text-align: right;
  word-break: break-word;
  max-width: 120px;
}

.digi-mobile-expand-button {
  background: transparent;
  border: none;
  padding: var(--digi-spacing-2);
  cursor: pointer;
  color: var(--digi-color-text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--digi-border-radius-sm);
  transition: all var(--digi-transition-fast);
}

.digi-mobile-expand-button:hover {
  background-color: var(--digi-color-surface-hover);
  color: var(--digi-color-text-primary);
}

.digi-mobile-card-content {
  padding: var(--digi-spacing-4);
  display: flex;
  flex-direction: column;
  gap: var(--digi-spacing-3);
  background-color: var(--digi-color-background-subtle);
  animation: var(--digi-animation-slide-down);
}

.digi-mobile-field {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--digi-spacing-2) 0;
  border-bottom: 1px solid var(--digi-color-border-light);
}

.digi-mobile-field:last-child {
  border-bottom: none;
}

.digi-mobile-field-label {
  flex: 0 0 40%;
  font-size: var(--digi-font-size-sm);
  color: var(--digi-color-text-secondary);
  font-weight: var(--digi-font-weight-medium);
  line-height: var(--digi-line-height-tight);
}

.digi-mobile-field-value {
  flex: 0 0 60%;
  font-size: var(--digi-font-size-sm);
  color: var(--digi-color-text-primary);
  text-align: right;
  word-break: break-word;
  line-height: var(--digi-line-height-tight);
}

.digi-mobile-actions {
  display: flex;
  gap: var(--digi-spacing-2);
  padding: var(--digi-spacing-3) var(--digi-spacing-4);
  border-top: 1px solid var(--digi-color-border);
  background-color: var(--digi-color-background-subtle);
  flex-wrap: wrap;
}

.digi-mobile-action-button {
  flex: 1;
  min-width: 80px;
  padding: var(--digi-spacing-2) var(--digi-spacing-3);
  background-color: var(--digi-color-surface);
  border: 1px solid var(--digi-color-border);
  border-radius: var(--digi-border-radius-sm);
  font-size: var(--digi-font-size-sm);
  font-weight: var(--digi-font-weight-medium);
  color: var(--digi-color-text-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--digi-spacing-1);
  transition: all var(--digi-transition-fast);
}

.digi-mobile-action-button:hover {
  background-color: var(--digi-color-primary);
  color: var(--digi-color-surface);
  border-color: var(--digi-color-primary);
  transform: translateY(-1px);
  box-shadow: var(--digi-shadow-sm);
}

.digi-mobile-action-button:active {
  transform: translateY(0);
}

.digi-mobile-no-data {
  padding: var(--digi-spacing-8) var(--digi-spacing-4);
  text-align: center;
  color: var(--digi-color-text-secondary);
  font-size: var(--digi-font-size-base);
  background-color: var(--digi-color-surface);
  border-radius: var(--digi-border-radius);
  border: 1px solid var(--digi-color-border);
}

.digi-mobile-pagination {
  margin-top: var(--digi-spacing-4);
  padding: var(--digi-spacing-3);
  background-color: var(--digi-color-surface);
  border-radius: var(--digi-border-radius);
  box-shadow: var(--digi-shadow-sm);
  border: 1px solid var(--digi-color-border);
}

/* Mobile responsive adjustments */
@media (max-width: 500px) {
  .digi-mobile-container {
    padding: var(--digi-spacing-3);
  }

  .digi-mobile-card-title {
    font-size: var(--digi-font-size-sm);
  }

  .digi-mobile-card-subtitle {
    font-size: var(--digi-font-size-xs);
  }

  .digi-mobile-card-right-text {
    font-size: var(--digi-font-size-xs);
    max-width: 80px;
  }

  .digi-mobile-field-label,
  .digi-mobile-field-value {
    font-size: var(--digi-font-size-xs);
  }
}

/* Dark theme support for mobile */
[data-theme='dark'] .digi-mobile-container {
  background-color: var(--digi-color-background-dark);
}

[data-theme='dark'] .digi-mobile-card {
  background-color: var(--digi-color-surface-dark);
  border-color: var(--digi-color-border-dark);
}

[data-theme='dark'] .digi-mobile-card-header {
  background-color: var(--digi-color-surface-dark);
  border-color: var(--digi-color-border-dark);
}

[data-theme='dark'] .digi-mobile-card-header:hover {
  background-color: var(--digi-color-surface-hover-dark);
}

[data-theme='dark'] .digi-mobile-card-content {
  background-color: var(--digi-color-background-subtle-dark);
}

[data-theme='dark'] .digi-mobile-field {
  border-color: var(--digi-color-border-light-dark);
}

[data-theme='dark'] .digi-mobile-actions {
  background-color: var(--digi-color-background-subtle-dark);
  border-color: var(--digi-color-border-dark);
}

[data-theme='dark'] .digi-mobile-action-button {
  background-color: var(--digi-color-surface-dark);
  border-color: var(--digi-color-border-dark);
  color: var(--digi-color-text-primary-dark);
}

[data-theme='dark'] .digi-mobile-action-button:hover {
  background-color: var(--digi-color-primary-dark);
  border-color: var(--digi-color-primary-dark);
}

[data-theme='dark'] .digi-mobile-no-data {
  background-color: var(--digi-color-surface-dark);
  border-color: var(--digi-color-border-dark);
  color: var(--digi-color-text-secondary-dark);
}

[data-theme='dark'] .digi-mobile-pagination {
  background-color: var(--digi-color-surface-dark);
  border-color: var(--digi-color-border-dark);
}

/* Mobile loading states */
.digi-mobile-card.loading {
  opacity: 0.7;
  pointer-events: none;
}

.digi-mobile-card.loading .digi-mobile-card-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 1.5s infinite;
}

/* Animation for mobile cards */
.digi-mobile-card {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Print styles for mobile */
@media print {
  .digi-mobile-container {
    background: white;
    padding: 0;
  }

  .digi-mobile-card {
    box-shadow: none;
    border: 1px solid #ccc;
    break-inside: avoid;
    margin-bottom: var(--digi-spacing-4);
  }

  .digi-mobile-expand-button {
    display: none;
  }

  .digi-mobile-card-content {
    display: block !important;
    animation: none;
  }

  .digi-mobile-actions {
    display: none;
  }
}

/* Inbox and History Screen Enhancements */
.digi-inbox-container,
.digi-history-container {
  margin-top: 0 !important;
  padding-top: 0 !important;
  padding: var(--digi-spacing-6);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

/* Enhanced Inbox and History Sections */
.digi-inbox-container,
.digi-history-container {
  padding: var(--digi-spacing-6);
  background-color: var(--digi-color-background);
  min-height: 100vh;
}

.digi-inbox-section,
.digi-history-section {
  margin-bottom: var(--digi-spacing-12);
  background-color: var(--digi-color-surface);
  border-radius: var(--digi-border-radius-lg);
  box-shadow: var(--digi-shadow-lg);
  border: 1px solid var(--digi-color-border);
  overflow: hidden;
  transition: all var(--digi-transition-medium);
  animation: var(--digi-animation-fade-in);
  position: relative;
  z-index: 1;
}

/* First section gets extra top margin for better spacing */
.digi-inbox-section:first-child,
.digi-history-section:first-child {
  margin-top: var(--digi-spacing-6);
}

/* Additional spacing between sections for better visual separation */
.digi-inbox-section + .digi-inbox-section,
.digi-history-section + .digi-history-section {
  margin-top: var(--digi-spacing-8);
}

.digi-section-header {
  padding: var(--digi-spacing-6) var(--digi-spacing-8);
  background: linear-gradient(135deg, var(--digi-color-primary) 0%, var(--digi-color-primary-dark) 100%);
  border-bottom: 0;
  padding-bottom: 0;
  position: relative;
}

.digi-section-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.digi-section-title {
  color: var(--digi-text-primary);
  font-size: var(--digi-font-size-xl);
  font-weight: var(--digi-font-weight-bold);
  margin: 0 0 var(--digi-spacing-2) 0;
  position: relative;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.digi-section-description {
  color: var(--digi-text-secondary);
  font-size: var(--digi-font-size-sm);
  font-weight: var(--digi-font-weight-medium);
  position: relative;
  z-index: 1;
  margin: 0 0 20px 0;
  line-height: 1.4;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.6);
}

/* Perfect Section Dividers and Visual Hierarchy */
.digi-inbox-section::after,
.digi-history-section::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: calc(-1 * var(--digi-spacing-6));
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--digi-color-border) 20%,
    var(--digi-color-primary) 50%,
    var(--digi-color-border) 80%,
    transparent 100%
  );
  opacity: 0.4;
  z-index: 0;
}

.digi-inbox-section:last-child::after,
.digi-history-section:last-child::after {
  display: none;
}

/* Enhanced Section Content Padding */
.digi-inbox-section .MuiTableContainer-root,
.digi-history-section .MuiTableContainer-root,
.digi-inbox-section > div:not(.digi-section-header),
.digi-history-section > div:not(.digi-section-header) {
  padding: var(--digi-spacing-4) var(--digi-spacing-6) var(--digi-spacing-6);
}

/* Perfect Mobile Spacing */
@media (max-width: 768px) {
  .digi-inbox-section::after,
  .digi-history-section::after {
    bottom: calc(-1 * var(--digi-spacing-4));
  }

  .digi-inbox-section .MuiTableContainer-root,
  .digi-history-section .MuiTableContainer-root,
  .digi-inbox-section > div:not(.digi-section-header),
  .digi-history-section > div:not(.digi-section-header) {
    padding: var(--digi-spacing-3) var(--digi-spacing-4) var(--digi-spacing-4);
  }
}

/* Section Loading Animation */
@keyframes sectionSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.digi-inbox-section,
.digi-history-section {
  animation: sectionSlideIn 0.6s ease-out;
  animation-fill-mode: both;
}

/* Staggered Animation Timing */
.digi-inbox-section:nth-child(2) {
  animation-delay: 0.1s;
}

.digi-inbox-section:nth-child(3) {
  animation-delay: 0.2s;
}

.digi-inbox-section:nth-child(4) {
  animation-delay: 0.3s;
}

.digi-history-section:nth-child(2) {
  animation-delay: 0.1s;
}

.digi-history-section:nth-child(3) {
  animation-delay: 0.2s;
}

/* Enhanced Container Styling */
.digi-inbox-container,
.digi-history-container {
  padding: var(--digi-spacing-6) var(--digi-spacing-4);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: calc(100vh - 80px);
  position: relative;
}

.digi-inbox-container::before,
.digi-history-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* Dark Theme Section Enhancements */
[data-theme='dark'] .digi-inbox-container,
[data-theme='dark'] .digi-history-container {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

[data-theme='dark'] .digi-inbox-container::before,
[data-theme='dark'] .digi-history-container::before {
  background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.08) 0%, transparent 50%);
}

[data-theme='dark'] .digi-inbox-section,
[data-theme='dark'] .digi-history-section {
  background-color: var(--digi-color-surface-dark);
  border-color: var(--digi-color-border-dark);
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.3),
    0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

[data-theme='dark'] .digi-inbox-section::after,
[data-theme='dark'] .digi-history-section::after {
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--digi-color-border-dark) 20%,
    var(--digi-color-primary) 50%,
    var(--digi-color-border-dark) 80%,
    transparent 100%
  );
  opacity: 0.6;
}

[data-theme='dark'] .digi-section-header {
  background: linear-gradient(135deg, var(--digi-color-primary) 0%, #45226d 100%);
  border-bottom-color: var(--digi-color-border-dark);
}

[data-theme='dark'] .digi-section-header::before {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
}

/* Enhanced Focus States for Dark Theme */
[data-theme='dark'] .digi-inbox-section:hover,
[data-theme='dark'] .digi-history-section:hover {
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.4),
    0 10px 10px -5px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(59, 130, 246, 0.2);
  transform: translateY(-3px);
}

/* Print styles for inbox/history */
@media print {
  .digi-inbox-container,
  .digi-history-container {
    background: white;
    padding: 0;
  }

  .digi-inbox-section,
  .digi-history-section {
    box-shadow: none;
    border: 1px solid #ccc;
    break-inside: avoid;
    margin-bottom: var(--digi-spacing-4);
  }

  .digi-section-header {
    background: #f5f5f5;
    color: black;
  }

  .digi-section-title {
    color: black;
    text-shadow: none;
  }

  .digi-section-description {
    color: #666;
  }

  .digi-quick-actions {
    display: none;
  }
}

/* Enhanced Material-UI Button Overrides for DigiTable */
.digi-table-container .MuiButtonBase-root,
.digi-table-container .MuiButton-root,
.digi-table-container .MuiIconButton-root {
  border-radius: var(--digi-radius-md) !important;
  transition: all var(--digi-transition-base) !important;
  font-weight: 600 !important;
}

/* Table Header Layout Improvements */
.digi-table-container .MuiTableCell-head {
  padding: var(--digi-spacing-md) var(--digi-spacing-sm) !important;
  vertical-align: middle !important;
}

.digi-table-container .MuiTableCell-head > * {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  width: 100% !important;
  gap: var(--digi-spacing-sm) !important;
}

/* Header Text Specific Spacing */
.digi-table-container .MuiTableCell-head .MuiTableSortLabel-root {
  flex: 1 !important;
  justify-content: flex-start !important;
  margin-right: var(--digi-spacing-sm) !important;
}

.digi-table-container .MuiTableCell-head .MuiTableSortLabel-root span {
  padding-right: var(--digi-spacing-xs) !important;
}

/* Filter Button Container in Headers */
.digi-table-container .MuiTableCell-head .filter-button-container {
  display: flex !important;
  align-items: center !important;
  gap: var(--digi-spacing-xs) !important;
  flex-shrink: 0 !important;
  margin-left: auto !important;
}

/* Ensure proper text wrapping and spacing */
.digi-table-container .MuiTableCell-head .MuiTableSortLabel-root .MuiTableSortLabel-icon {
  margin-left: var(--digi-spacing-xs) !important;
}

/* Column resizing and text layout */
.digi-table-container .MuiTableCell-head {
  min-width: 120px !important;
  max-width: 300px !important;
}

.digi-table-container .MuiTableCell-head > span:first-child {
  flex: 1 !important;
  text-align: left !important;
  padding-right: var(--digi-spacing-sm) !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}
