#historyTable .fixedWidthTable > div > div > div:first-child {
  display: none;
  height: 0;
}

#historyTable .fixedWidthTable > div > div > div:nth-child(2) {
  font-size: 14px;
}

#historyTable > .fixedWidthTable > div > div > div:nth-child(2) > div > div > div > table > thead > tr > th {
  font-size: 15px;
}

#historyTable > .fixedWidthTable > div > div > div:nth-child(2) > div > div > div > table > tbody > tr {
  margin: 10px !important;
}

.form-grid {
  font-size: 14px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  padding: 16px;
  width: 500px;
  max-height: 600px;
}

.form-row {
  align-items: center;
}

.form-row > label {
  width: 200px;
}

.form-row > p {
  width: 100%;
  text-align: left;
}

.form-row label {
  margin-right: 16px;
  font-weight: bold;
}

.form-row p {
  margin: 0;
}

textarea {
  width: 100%;
  padding: 8px;
  resize: none;
}

.form-button {
  grid-column: span 2;
  display: flex;
  justify-content: flex-end;
}
