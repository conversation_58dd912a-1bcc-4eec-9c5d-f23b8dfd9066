// hooks/useFileUpload.ts
import { useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import { IFile, IUploadedFile } from '@/types'
import api from '@/api'
import toast from 'react-hot-toast'

export const useFileUpload = () => {
  const [selectedFiles, setSelectedFiles] = useState<IFile[]>([])
  const [uploadedFiles, setUploadedFiles] = useState<IUploadedFile[]>([])

  const uploadMutation = useMutation({
    mutationFn: async (files: IFile[]) => {
      const formData = new FormData()
      files.forEach((file) => {
        if (file.file) {
          const uniqueFile = new File([file.file], file.name, { type: file.file.type })
          formData.append('files', uniqueFile)
        }
      })
      formData.append('pathKey', 'SharePointActionPanelUploadFolder')

      const response = await api.post('/file/upload-multiple', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      })
      return response.data
    },
  })

  const handleUpload = async () => {
    if (selectedFiles.length === 0) return null

    try {
      const response = await uploadMutation.mutateAsync(selectedFiles)
      const newUploadedFiles = selectedFiles.map((file, index) => ({
        name: file.name,
        size: file.size,
        url: response.urls[index],
      }))

      setUploadedFiles((prev) => [...prev, ...newUploadedFiles])
      setSelectedFiles([])

      return response.urls
    } catch (error) {
      console.error('Error uploading files:', error)
      toast.error('Failed to upload files')
      return null
    }
  }

  return {
    selectedFiles,
    setSelectedFiles,
    uploadedFiles,
    setUploadedFiles,
    handleUpload,
    isUploading: uploadMutation.isPending,
  }
}
