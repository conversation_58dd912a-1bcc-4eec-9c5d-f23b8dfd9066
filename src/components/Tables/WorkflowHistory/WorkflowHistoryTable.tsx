import { WCircularProgress } from 'wface'
import { useTranslation } from 'react-i18next'
import { useGetWorkflowHistory } from '@/hooks'
import { IWorkflowHistory } from '@/types'
import useAppHelper from '@/services/wface/appHelper'
import './WorkflowHistoryTable.css'
import HistoryCommentUpdateModal from './WorkflowHistoryCommentUpdateModal'
import dayjs from 'dayjs'
import { useWorkflow } from '@/contexts/WorkflowContext'
import DigiTable from '../DigiTable/DigiTable'

type Props = {
  wfInstanceId: number
}

export default function WorkflowHistoryTable({ wfInstanceId }: Props) {
  const { isDaemonWorking } = useWorkflow()
  const workflowHistory = useGetWorkflowHistory({ instanceId: wfInstanceId, enabled: isDaemonWorking })
  const { getInitialScreenValue } = useAppHelper()
  const { t, i18n } = useTranslation('workflowHistory')

  const workflowHistoryColumns = [
    { title: t('state'), width: '15%', field: i18n.language == 'tr' ? 'state' : 'stateDesc' },
    {
      title: t('users'),
      field: 'users',
      width: '25%',
      render: (rowData: IWorkflowHistory) => {
        return <div dangerouslySetInnerHTML={{ __html: rowData.users ?? '' }} />
      },
    },
    {
      title: t('action'),
      field: i18n.language == 'tr' ? 'action' : 'actionEng',
      width: '15%',
      render: (rowData: IWorkflowHistory) => {
        return <div style={{ color: rowData.colors ?? undefined }}>{rowData.action}</div>
      },
    },
    {
      title: t('dates'),
      field: 'dates',
      width: '10%',
      render: (rowData: IWorkflowHistory) => dayjs(rowData.dates).format('YYYY-MM-DD HH:mm:ss'),
      dateSetting: {
        format: 'DD.MM.YYYY HH:mm',
        locale: i18n.language,
        type: 'date',
      },
    },
    {
      title: t('comments'),
      field: 'comments',
      width: '25%',
      render: (rowData: IWorkflowHistory) => {
        return <div dangerouslySetInnerHTML={{ __html: rowData.comments ?? '' }} />
      },
    },
    {
      title: t('editComment'),
      width: '5%',
      field: 'editCommentButton',
      render: (rowData: IWorkflowHistory) => {
        return rowData.actionLoginId == getInitialScreenValue('UserId') && rowData.colors != 'Orange' ? (
          <HistoryCommentUpdateModal data={rowData} refetch={() => workflowHistory.refetch({ instanceId: wfInstanceId, enabled: isDaemonWorking })} />
        ) : (
          ''
        )
      },
    },
  ]

  return (
    <div id="historyTable" style={{ position: 'relative', width: '100%' }}>
      {isDaemonWorking && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(255, 255, 255, 0.7)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1000,
          }}
        >
          <WCircularProgress size={60} />
        </div>
      )}
      <DigiTable
        data={workflowHistory?.data ?? []} // Pass workflowHistory data reactively
        columns={workflowHistoryColumns}
        toolbar={false}
        mobileConfig={{
          titleFields: ['users', 'state'],
          rightFields: ['dates'],
          subtitleFields: [i18n.language == 'tr' ? 'action' : 'actionEng'],
        }}
        style={{
          border: '1px solid #ccc',
          borderBottomLeftRadius: '5px',
          borderBottomRightRadius: '5px',
          margin: 0,
          marginBottom: 30,
          fontSize: '14px',
          padding: '5px',
          opacity: isDaemonWorking ? 0.3 : 1,
        }}
      />
    </div>
  )
}
