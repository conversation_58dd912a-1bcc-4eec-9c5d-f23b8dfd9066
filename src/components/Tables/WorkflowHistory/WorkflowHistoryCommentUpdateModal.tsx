import React, { useState, useCallback, useEffect, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { useQueryClient, useMutation } from '@tanstack/react-query'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ard, WCardContent, WTypography, WBox } from 'wface'
import { toast } from 'react-hot-toast'
import Modal from '@/components/Modal/Modal'
import { IWorkflowHistory } from '@/types'
import DigiTable from '@/components/Tables/DigiTable/DigiTable'
import DigiTextField from '@/components/formElements/DigiTextField/DigiTextField'
import config from '@/config'

interface IUploadedFile {
  name: string
  size: number
  url: string
}

interface IFile {
  name: string
  size: number
  file: File
}

interface Props {
  data: IWorkflowHistory
  refetch?: () => void
}

interface CommentUpdatePayload {
  comment: string
  uploadedFiles: string[]
}
interface IFileInfo {
  name: string
  url: string
}
let apiUrl = config.VITE_API_URL

if (window.location.hostname === 'digiflowtest') {
  apiUrl = config.VITE_API_URL.split('.digiturk.com.tr')[0] + '/api'
}
const userId = localStorage.getItem('UserId')
const language = localStorage.getItem('language') || 'en'
const headers = new Headers({})

headers.set('Content-Type', 'application/json')
headers.set('X-Requested-With', 'XMLHttpRequest')
headers.set('Accept-Language', language)

if (userId) {
  headers.set('X-Login-Id', userId)
}

const WorkflowHistoryCommentUpdateModal: React.FC<Props> = ({ data, refetch }) => {
  const queryClient = useQueryClient()
  const [isOpen, setIsOpen] = useState(false)
  const [newComment, setNewComment] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<IFile[]>([])
  const [uploadedFiles, setUploadedFiles] = useState<IUploadedFile[]>([])

  const { t, i18n } = useTranslation('workflowHistory')

  const getHeaders = (isFormData: boolean = false) => {
    const userId = localStorage.getItem('UserId')
    const language = localStorage.getItem('language') || 'en'
    const headers = new Headers()

    if (!isFormData) {
      headers.set('Content-Type', 'application/json')
    }

    headers.set('X-Requested-With', 'XMLHttpRequest')
    headers.set('Accept-Language', language)

    if (userId) {
      headers.set('X-Login-Id', String(Number(userId)))
    }

    const wfInstanceId = new URLSearchParams(window.location.search).get('wfInstanceId')
    if (wfInstanceId) {
      headers.set('X-Workflow-Instance-Id', wfInstanceId)
    }

    return headers
  }

  const parseCommentAndFiles = (commentHtml: string): { message: string; files: IFileInfo[] } => {
    // Handle case where comment starts with a dash (no message)
    const trimmedComment = commentHtml.trim()
    if (trimmedComment.startsWith('- <a')) {
      const message = ''
      const files: IFileInfo[] = []

      const parts = trimmedComment.split('<br/> -')
      parts.forEach((part) => {
        if (part.includes('href=')) {
          const urlStart = part.indexOf("href='") + 6
          const urlEnd = part.indexOf("'", urlStart)
          const url = part.substring(urlStart, urlEnd)

          const textStart = part.indexOf('>') + 1
          const textEnd = part.indexOf('</a>')
          const fileName = part.substring(textStart, textEnd)

          // Only add if file isn't already in the array
          if (!files.some((f) => f.name === fileName)) {
            files.push({
              name: fileName,
              url: url,
            })
          }
        }
      })

      return { message, files }
    }

    // Handle normal case with message
    const parts = commentHtml.split('<br/> -')
    const message = parts[0].replace(/^-\s*/, '').trim() // Remove leading dash if present
    const files: IFileInfo[] = []

    if (parts.length > 1) {
      parts.slice(1).forEach((part) => {
        if (part.includes('href=')) {
          const urlStart = part.indexOf("href='") + 6
          const urlEnd = part.indexOf("'", urlStart)
          const url = part.substring(urlStart, urlEnd)

          const textStart = part.indexOf('>') + 1
          const textEnd = part.indexOf('</a>')
          const fileName = part.substring(textStart, textEnd)

          // Only add if file isn't already in the array
          if (!files.some((f) => f.name === fileName)) {
            files.push({
              name: fileName,
              url: url,
            })
          }
        }
      })
    }

    return { message, files }
  }

  useEffect(() => {
    if (isOpen && data.comments) {
      const { message, files } = parseCommentAndFiles(data.comments)
      setNewComment(message)

      // Only add existing files to uploadedFiles
      const existingFiles = files.map((file) => ({
        name: file.name,
        size: 0,
        url: file.url,
      }))
      setUploadedFiles(existingFiles)

      // Clear selected files when opening modal
      setSelectedFiles([])
    }
  }, [isOpen, data.comments, data.action])

  const uploadFilesMutation = useMutation({
    mutationFn: async (files: IFile[]) => {
      const formData = new FormData()
      files.forEach((file) => {
        if (file.file) {
          const uniqueFile = new File([file.file], file.name, { type: file.file.type })
          formData.append('files', uniqueFile)
        }
      })
      formData.append('pathKey', 'SharePointActionPanelUploadFolder')

      const response = await fetch(apiUrl + '/api/file/upload-multiple', {
        method: 'POST',
        headers: getHeaders(true),
        credentials: 'include',
        body: formData,
      })

      if (!response.ok) {
        throw new Error('Upload failed')
      }

      return response.json()
    },
    onError: (error) => {
      console.error('File upload error:', error)
      toast.error(t('fileUploadError'))
    },
  })

  const updateCommentMutation = useMutation({
    mutationFn: async (payload: CommentUpdatePayload) => {
      const response = await fetch(`${apiUrl}/workflows/history/comment/${data.wfHistoryId}`, {
        method: 'PUT',
        headers: getHeaders(),
        credentials: 'include',
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        throw new Error('Update failed')
      }

      return response.json()
    },
    onSuccess: () => {
      toast.success(t('commentUpdated'))
      queryClient.invalidateQueries({ queryKey: ['workflowHistory'] })
      refetch?.()
      resetState()
      handleClose()
    },
    onError: (error) => {
      console.error('Comment update error:', error)
      toast.error(t('commentUpdateError'))
    },
  })

  const resetState = useCallback(() => {
    setNewComment('')
    setSelectedFiles([])
    setUploadedFiles([])
    setIsLoading(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [])

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    event.preventDefault()
    event.stopPropagation()

    const files = Array.from(event.target.files || []).map((file) => ({
      name: file.name,
      size: file.size,
      file,
    }))

    setSelectedFiles((prev) => [...prev, ...files])
  }, [])

  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    event.stopPropagation()
    setIsDragging(false)

    const files = Array.from(event.dataTransfer.files).map((file) => ({
      name: file.name,
      size: file.size,
      file,
    }))

    setSelectedFiles((prev) => [...prev, ...files])
  }, [])

  const handleDelete = useCallback((fileName: string) => {
    setUploadedFiles((prev) => prev.filter((file) => file.name !== fileName))
    setSelectedFiles((prev) => prev.filter((file) => file.name !== fileName))
  }, [])

  const handleUpload = async () => {
    if (selectedFiles.length === 0) return null

    try {
      setIsLoading(true)
      const response = await uploadFilesMutation.mutateAsync(selectedFiles)
      const newUploadedFiles = selectedFiles.map((file, index) => ({
        name: file.name,
        size: file.size,
        url: response.urls[index],
      }))

      setUploadedFiles((prev) => [...prev, ...newUploadedFiles])
      setSelectedFiles([])
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }

      return response.urls
    } catch (error) {
      console.error('Upload error:', error)
      toast.error(t('fileUploadError'))
      return null
    } finally {
      setIsLoading(false)
    }
  }

  const handleConfirm = async () => {
    if (newComment.trim() === '') {
      toast.error(t('commentEmpty'))
      return
    }

    try {
      setIsLoading(true)
      let fileNames: string[] = []

      if (data.action === 'Dosya Yüklendi') {
        // Handle new file uploads
        if (selectedFiles.length > 0) {
          const filesToUpload = selectedFiles.filter((file) => !uploadedFiles.some((uf) => uf.name === file.name))

          if (filesToUpload.length > 0) {
            const uploadedUrls = await handleUpload()
            if (!uploadedUrls) return

            const newFileNames = uploadedUrls.map((url: string) => {
              const parts = url.split('/')
              return parts[parts.length - 1]
            })
            fileNames.push(...newFileNames)
          }
        }

        // Include existing files
        uploadedFiles.forEach((file) => {
          const urlParts = file.url.split('/')
          fileNames.push(urlParts[urlParts.length - 1])
        })
      }

      await updateCommentMutation.mutateAsync({
        comment: newComment.trim(),
        uploadedFiles: fileNames,
      })
    } catch (error) {
      console.error('Confirm error:', error)
      toast.error(t('commentUpdateError'))
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = useCallback(() => {
    if (!isLoading && !uploadFilesMutation.isPending && !updateCommentMutation.isPending) {
      setIsOpen(false)
      resetState()
    }
  }, [isLoading, uploadFilesMutation.isPending, updateCommentMutation.isPending, resetState])

  const columns = [
    {
      title: t('fileName'),
      field: 'name',
      render: (rowData: (IFile | IUploadedFile) & { isExisting: boolean }) => {
        return (
          <div className="flex items-center gap-2">
            <span>{rowData.name}</span>
          </div>
        )
      },
    },
    {
      title: t('actions'),
      field: 'actions',
      render: (rowData: IFile | IUploadedFile) => (
        <WButton
          variant="text"
          color="secondary"
          onClick={() => handleDelete(rowData.name)}
          disabled={isLoading || uploadFilesMutation.isPending || updateCommentMutation.isPending}
        >
          {t('delete')}
        </WButton>
      ),
    },
  ]

  return (
    <>
      <WButton variant="contained" color="secondary" onClick={() => setIsOpen(true)} type="button">
        {t('editCommentButton')}
      </WButton>

      <Modal
        open={isOpen}
        title={t('editComment')}
        onClose={handleClose}
        onConfirm={handleConfirm}
        loading={isLoading || uploadFilesMutation.isPending || updateCommentMutation.isPending}
      >
        <WBox display="flex" flexDirection="column" fontSize={12} gap={3}>
          <WBox>
            <WBox className="form-row" mt={1}>
              <label style={{ fontWeight: 'bold', marginRight: '8px' }}>{t('user')}:</label>
              <p style={{ margin: 0 }}>{data.users}</p>
            </WBox>
            <WBox className="form-row" mt={1}>
              <label style={{ fontWeight: 'bold', marginRight: '8px' }}>{t('workflowNameNumber')}:</label>
              <p style={{ margin: 0 }}>
                {data.wfDefName} / {data.wfWorkflowInstanceId}
              </p>
            </WBox>
            <WBox className="form-row" mt={1}>
              <label style={{ fontWeight: 'bold', marginRight: '8px' }}>{t('stateAction')}:</label>
              <p style={{ margin: 0 }}>{i18n.language === 'tr' ? `${data.state}/${data.action}` : `${data.stateDesc}/${data.actionEng}`}</p>
            </WBox>
            <WBox className="form-row" mt={1}>
              <label style={{ fontWeight: 'bold', marginRight: '8px' }}>{t('oldComment')}:</label>
              <div
                dangerouslySetInnerHTML={{ __html: data.comments ?? '' }}
                style={{
                  overflowY: 'auto',
                  maxHeight: '150px',
                  padding: '10px',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  backgroundColor: '#f9f9f9',
                }}
              />
            </WBox>
          </WBox>

          <WBox>
            <DigiTextField
              label={t('newComment')}
              multiline
              value={newComment}
              onChange={(e) => setNewComment(e)}
              rows={5}
              name="newComment"
              disabled={isLoading || updateCommentMutation.isPending}
            />
          </WBox>

          {data.action === 'Dosya Yüklendi' && (
            <WBox>
              <label style={{ fontWeight: 'bold', marginBottom: '8px', display: 'inline-block' }}>{t('fileUpload')}</label>
              <WCard
                variant="outlined"
                style={{ marginTop: '10px', border: '1px dashed #ccc', backgroundColor: '#fff' }}
                onDragEnter={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  setIsDragging(true)
                }}
                onDragOver={(e) => e.preventDefault()}
                onDragLeave={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  setIsDragging(false)
                }}
                onDrop={handleDrop}
              >
                <WCardContent>
                  <WTypography variant="h6" style={{ marginBottom: '10px', fontWeight: 'bold' }}>
                    {t('fileUploadTitle')}
                  </WTypography>
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    onChange={handleFileSelect}
                    style={{ display: 'none' }}
                    onClick={(e) => e.stopPropagation()}
                    disabled={isLoading || updateCommentMutation.isPending || uploadFilesMutation.isPending}
                  />
                  <div
                    style={{
                      border: `2px dashed ${isDragging ? '#2196f3' : '#ccc'}`,
                      borderRadius: '4px',
                      padding: '20px',
                      textAlign: 'center',
                      cursor: 'pointer',
                      backgroundColor: isDragging ? 'rgba(33, 150, 243, 0.1)' : 'transparent',
                      transition: 'background-color 0.2s ease-in-out',
                    }}
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      fileInputRef.current?.click()
                    }}
                  >
                    <WTypography variant="body1" style={{ color: '#555' }}>
                      {t('dragAndDropText')}
                    </WTypography>
                  </div>
                </WCardContent>
              </WCard>

              {(selectedFiles.length > 0 || uploadedFiles.length > 0) && (
                <WBox mt={2}>
                  <WTypography variant="h6" style={{ marginBottom: '10px', fontWeight: 'bold' }}>
                    {t('uploadedFiles')}
                  </WTypography>
                  <DigiTable
                    data={[
                      ...uploadedFiles.map((file) => ({
                        ...file,
                        isExisting: true,
                      })),
                      ...selectedFiles
                        .filter((file) => !uploadedFiles.some((uf) => uf.name === file.name))
                        .map((file) => ({
                          ...file,
                          isExisting: false,
                        })),
                    ]}
                    columns={columns}
                    options={{
                      search: false,
                      paging: false,
                      toolbar: false,
                      sorting: false,
                    }}
                    style={{
                      boxShadow: 'none',
                      border: '1px solid #ddd',
                      borderRadius: '4px',
                      backgroundColor: '#fff',
                    }}
                  />
                </WBox>
              )}
            </WBox>
          )}
        </WBox>
      </Modal>
    </>
  )
}

export default WorkflowHistoryCommentUpdateModal
