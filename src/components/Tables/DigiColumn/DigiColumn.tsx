import React, { useState, useMemo, useEffect } from 'react'
import FilterListIcon from '@mui/icons-material/FilterList'
import <PERSON>U<PERSON>wardIcon from '@mui/icons-material/ArrowUpward'
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward'
import SortRoundedIcon from '@mui/icons-material/SortRounded'
import { useTranslation } from 'react-i18next'
import { WBox, WButton, WPopover, WTextField, WTypography } from 'wface'
import { IOption } from '@/types'
import { SelectBox } from '@/components/formElements'

interface DigiColumnProps {
  columnId: string
  title: string
  onApply: (filter: { type: string; value: any }) => void
  onClear: () => void
  onSort: (direction: 'asc' | 'desc' | null) => void
  columnType: string
  currentFilter: { type: string; value: any } | null
  currentSortDirection: 'asc' | 'desc' | null
  showFilter: boolean
  sorting?: boolean
  onSearch: (searchTerm: string) => void
}

const DigiColumn: React.FC<DigiColumnProps> = ({
  columnId,
  title,
  onApply,
  onClear,
  onSort,
  columnType,
  currentFilter,
  currentSortDirection,
  showFilter,
  sorting = true,
  onSearch,
}) => {
  const { t } = useTranslation('tableFilters')
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [filterType, setFilterType] = useState<IOption | null>(null)
  const [filterValue, setFilterValue] = useState<any>(currentFilter?.value || '')
  const [searchTerm, setSearchTerm] = useState<string>('')

  useEffect(() => {
    if (currentFilter) {
      setFilterType(filterTypes.find((type) => type.value === currentFilter.type) || null)
      setFilterValue(currentFilter.value)
    } else {
      setFilterType({
        value: '',
        label: t('selectFilterType'),
        labelEn: t('selectFilterType'),
      })
      setFilterValue('')
    }
  }, [currentFilter, t])

  const handleFilterClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation()
    setAnchorEl(event.currentTarget)
  }

  const handleClose = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation()
    setAnchorEl(null)
  }

  const handleApply = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation()
    if (filterType) {
      onApply({ type: filterType.value, value: filterValue })
    }
    handleClose(event)
  }

  const handleClear = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation()
    onClear()
    setFilterType({
      value: '',
      label: t('selectFilterType'),
      labelEn: t('selectFilterType'),
    })
    setFilterValue('')
    handleClose(event)
  }

  const handleSortClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation()
    const newSortDirection = currentSortDirection === 'asc' ? 'desc' : currentSortDirection === 'desc' ? null : 'asc'
    onSort(newSortDirection)
  }

  const open = Boolean(anchorEl)
  const id = open ? `filter-popover-${columnId}` : undefined

  const filterTypes: IOption[] = useMemo(() => {
    if (columnType === 'number') {
      return [
        { value: 'equals', label: t('equals'), labelEn: 'Equals' },
        { value: 'doesntEqual', label: t('doesntEqual'), labelEn: "Doesn't Equal" },
        { value: 'isLessThan', label: t('isLessThan'), labelEn: 'Is Less Than' },
        { value: 'isLessThanOrEqualTo', label: t('isLessThanOrEqualTo'), labelEn: 'Is Less Than or Equal To' },
        { value: 'isGreaterThan', label: t('isGreaterThan'), labelEn: 'Is Greater Than' },
        { value: 'isGreaterThanOrEqualTo', label: t('isGreaterThanOrEqualTo'), labelEn: 'Is Greater Than or Equal To' },
      ]
    } else if (columnType === 'date') {
      return [
        { value: 'on', label: t('on'), labelEn: 'On' },
        { value: 'before', label: t('before'), labelEn: 'Before' },
        { value: 'after', label: t('after'), labelEn: 'After' },
        { value: 'between', label: t('between'), labelEn: 'Between' },
      ]
    } else {
      return [
        { value: 'contains', label: t('contains'), labelEn: 'Contains' },
        { value: 'doesntContain', label: t('doesntContain'), labelEn: "Doesn't Contain" },
        { value: 'equals', label: t('equals'), labelEn: 'Equals' },
        { value: 'doesntEqual', label: t('doesntEqual'), labelEn: "Doesn't Equal" },
        { value: 'startsWith', label: t('startsWith'), labelEn: 'Starts With' },
        { value: 'endsWith', label: t('endsWith'), labelEn: 'Ends With' },
      ]
    }
  }, [columnType, t])

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchTerm(value)
    onSearch(value)
  }

  return (
    <WBox display="flex" flexDirection="column" width="100%">
      {/* First Line: Title and Sort Button */}
      <WBox display="flex" justifyContent="space-between" alignItems="center">
        <WTypography
          variant="subtitle2"
          onClick={sorting ? handleSortClick : undefined}
          style={{
            cursor: sorting ? 'pointer' : 'default',
            userSelect: 'none',
            fontWeight: 'bold',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          {title}
        </WTypography>
        {sorting && (
          <WButton size="small" onClick={handleSortClick} style={{ padding: 0, minWidth: 'auto' }}>
            {currentSortDirection === 'asc' && <ArrowUpwardIcon fontSize="small" color="action" />}
            {currentSortDirection === 'desc' && <ArrowDownwardIcon fontSize="small" color="action" />}
            {currentSortDirection === null && <SortRoundedIcon fontSize="small" color="primary" style={{ fontWeight: 'bold', opacity: 0.6 }} />}
          </WButton>
        )}
      </WBox>

      {/* Second Line: Search and Filter Button */}
      {showFilter ? (
        <WBox display="flex" justifyContent="space-between" alignItems="center" mt={1}>
          <WTextField
            variant="outlined"
            size="small"
            placeholder={t('searchPlaceholder')}
            value={searchTerm}
            onChange={handleSearchChange}
            style={{ flexGrow: 1, marginRight: 8, padding: 0, height: 10, fontSize: 8 }}
          />
          <WButton size="small" onClick={handleFilterClick} style={{ padding: 0, minWidth: 'auto' }}>
            {currentFilter?.type ? <FilterListIcon fontSize="small" color="error" /> : <FilterListIcon fontSize="small" color="action" />}
          </WButton>
        </WBox>
      ) : (
        <div>
          <br />
          <br />
        </div>
      )}

      {/* Popover for Advanced Filtering */}
      <WPopover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        onClick={(event) => event.stopPropagation()}
      >
        <WBox p={2} width={250}>
          <WTypography variant="subtitle1">{t('filter')}</WTypography>
          <WBox mt={2}>
            <WTypography variant="body2">{t('filterType')}</WTypography>
            <SelectBox
              label=""
              value={filterType}
              onChange={(option: IOption | IOption[] | null) => setFilterType(option as IOption)}
              options={filterTypes}
              defaultText={t('selectFilterType')}
              fullWidth
            />
          </WBox>
          <WBox mt={2}>
            <WTypography variant="body2">{t('value')}</WTypography>
            {columnType === 'date' && filterType?.value === 'between' ? (
              <WBox display="flex" justifyContent="space-between">
                <WTextField
                  type="date"
                  variant="outlined"
                  size="small"
                  value={filterValue?.start || ''}
                  onChange={(e) => setFilterValue({ ...filterValue, start: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                  style={{ marginRight: 4 }}
                />
                <WTextField
                  type="date"
                  variant="outlined"
                  size="small"
                  value={filterValue?.end || ''}
                  onChange={(e) => setFilterValue({ ...filterValue, end: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </WBox>
            ) : columnType === 'date' ? (
              <WTextField
                type="date"
                variant="outlined"
                size="small"
                value={filterValue || ''}
                onChange={(e) => setFilterValue(e.target.value)}
                fullWidth
                InputLabelProps={{ shrink: true }}
              />
            ) : (
              <WTextField variant="outlined" size="small" value={filterValue} onChange={(e) => setFilterValue(e.target.value)} fullWidth />
            )}
          </WBox>
          <WBox mt={2} display="flex" justifyContent="space-between">
            <WButton onClick={handleClear} color="secondary" size="small">
              {t('clear')}
            </WButton>
            <WBox>
              <WButton onClick={handleClose} color="secondary" size="small">
                {t('cancel')}
              </WButton>
              <WButton onClick={handleApply} color="primary" variant="contained" size="small" style={{ marginLeft: 8 }}>
                {t('apply')}
              </WButton>
            </WBox>
          </WBox>
        </WBox>
      </WPopover>
    </WBox>
  )
}

export default DigiColumn
