/* Loading.css */
:root {
  --loading-spinner-primary-color: var(--df-primary-500, #5c2d91);
  --loading-spinner-secondary-color: var(--df-primary-100, #e2d7f1);
  --loading-spinner-background-color: var(--df-primary-50, #f4effa);
  --loading-overlay-background-color: rgba(45, 55, 72, 0.6);

  --loading-spinner-size: 40px;
  --loading-spinner-border-width: 4px;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--loading-overlay-background-color);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050;
  backdrop-filter: blur(3px);
}

.loading-content {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px;
  background-color: #fff;
  border-radius: var(--df-radius, 6px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 100px;
  min-height: 100px;
}

.loading-inline {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  width: 100%;
}

.spinner {
  border: var(--loading-spinner-border-width) solid var(--loading-spinner-secondary-color);
  border-top-color: var(--loading-spinner-primary-color);
  border-radius: 50%;
  width: var(--loading-spinner-size);
  height: var(--loading-spinner-size);
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
