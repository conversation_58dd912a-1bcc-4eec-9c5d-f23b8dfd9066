import { FC } from 'react'
import './Loading.css'

export const Loading: FC<{ show: boolean; fullScreen?: boolean; height?: string | number }> = ({ show, fullScreen, height }) => {
  if (!show) return null

  const loadingContent = <div className="spinner"></div>

  if (fullScreen) {
    return (
      <div className="loading-overlay">
        <div className="loading-content">{loadingContent}</div>
      </div>
    )
  }

  const inlineStyle: React.CSSProperties = {}
  if (height) {
    inlineStyle.height = height
  }

  return (
    <div style={inlineStyle} className="loading-inline">
      {loadingContent}
    </div>
  )
}
