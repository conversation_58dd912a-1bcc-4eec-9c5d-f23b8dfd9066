﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiflowAPI.Infrastructure.Exceptions
{
    public class DigiflowException : Exception
    {
        // Default constructor with no message
        public DigiflowException()
        {
            // Example: Log the exception with a default message
            // Logger.LogException(this);
        }

        // Constructor with a custom error message
        public DigiflowException(string message) : base(message)
        {
            // Example: Log the exception with the custom message
            // Logger.LogException(this, message);
        }

        // Constructor with a custom message and inner exception
        public DigiflowException(string message, Exception innerException) : base(message, innerException)
        {
            // Example: Log the exception with the custom message and inner exception
            // Logger.LogException(this, message, innerException);
        }
    }

}
