﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiflowAPI.Infrastructure.Exceptions.Authentication
{
    public class NotAuthenticationException : Exception
    {
        public string NotAuthenticateSID { get; set; }
        public string NotAuthenticateLogin { get; set; }
        private Exception _InnerException;

        public Exception InnerException
        {
            get
            {
                return _InnerException;
            }
        }
        public NotAuthenticationException() { }
        public NotAuthenticationException(string message) : base(message) { }
        public NotAuthenticationException(string message, Exception innerException) : base(message, innerException) { }
        public NotAuthenticationException(string SID, string LoginId, Exception ex)
        {
            NotAuthenticateLogin = LoginId;
            NotAuthenticateSID = SID;
            _InnerException = ex;
        }
    }
}
