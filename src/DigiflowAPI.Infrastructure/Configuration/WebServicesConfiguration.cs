﻿using DigiflowAPI.Application.Interfaces.Configuration;
using Microsoft.Extensions.Configuration;

namespace DigiflowAPI.Infrastructure.Configuration
{
    public class WebServicesConfiguration(IConfiguration configuration) : IWebServicesConfiguration
    {
        public bool IsProxyUsing => bool.Parse(configuration["ServiceSettings:IsProxyUsing"] ?? "false");
        public bool IsCredentialUsing => bool.Parse(configuration["ServiceSettings:IsCredentialUsing"] ?? "false");
        public string UserName => configuration["ServiceSettings:UserName"] ?? string.Empty;
        public string Password => configuration["ServiceSettings:Password"] ?? string.Empty;
        public string Domain => configuration["ServiceSettings:Domain"] ?? string.Empty;
        public string ProxyServicesIp => configuration["ServiceSettings:ProxyServicesIp"] ?? string.Empty;
    }
}
