﻿using DigiflowAPI.Application.Interfaces.Configuration;
using Microsoft.Extensions.Configuration;

namespace DigiflowAPI.Infrastructure.Configuration
{
    public class SharePointConfiguration(IConfiguration configuration) : ISharePointConfiguration
    {
        public Dictionary<string, string> SharePointPaths { get; private set; } = new();

        public void LoadSharePointPaths()
        {
            SharePointPaths = new Dictionary<string, string>();
            var allSettings = configuration.GetSection("ServiceSettings:Sharepoint").GetChildren();

            foreach (var setting in allSettings)
            {
                SharePointPaths.Add(setting.Key, setting.Value ?? string.Empty);
            }
        }

        public string GetSharePointPath(string key)
        {
            if (SharePointPaths.TryGetValue(key, out string? path))
            {
                return path;
            }
            return string.Empty;
        }
    }
}
