﻿using DigiflowAPI.Domain.Entities;
using DigiflowAPI.Application.Interfaces.Services;
using System.Net;
using DigiflowAPI.Application.Interfaces.Configuration;

namespace DigiflowAPI.Infrastructure.Services.SharePoint
{
    public class SharePointService : ISharePointService
    {
        private readonly ISharePointConfiguration _sharePointConfiguration;
        private readonly IWebServicesConfiguration _webServicesConfiguration;

        public SharePointService(
            ISharePointConfiguration sharePointConfiguration,
            IWebServicesConfiguration webServicesConfiguration)
        {
            _sharePointConfiguration = sharePointConfiguration;
            _webServicesConfiguration = webServicesConfiguration;
            _sharePointConfiguration.LoadSharePointPaths();
        }

        public async Task<string> UploadFileToSharePointAsync(UploadedFile file, string pathKey)
        {
            string sharePointPath = _sharePointConfiguration.GetSharePointPath(pathKey);
            sharePointPath = Path.Combine(sharePointPath, file.FileName);

            WebResponse response = null;
            try
            {
                WebRequest request = WebRequest.Create(sharePointPath);
                request.Method = "PUT";

                if (_webServicesConfiguration.IsCredentialUsing)
                {
                    request.Credentials = new NetworkCredential(
                        _webServicesConfiguration.UserName,
                        _webServicesConfiguration.Password,
                        _webServicesConfiguration.Domain);
                }
                else
                {
                    request.Credentials = CredentialCache.DefaultCredentials;
                }

                byte[] buffer = new byte[1024];
                using (Stream stream = await request.GetRequestStreamAsync())
                using (MemoryStream ms = new MemoryStream(file.Content))
                {
                    int bytesRead;
                    while ((bytesRead = await ms.ReadAsync(buffer, 0, buffer.Length)) > 0)
                    {
                        await stream.WriteAsync(buffer, 0, bytesRead);
                    }
                }

                response = await request.GetResponseAsync();
                return sharePointPath;
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to upload file to SharePoint: {ex.Message}", ex);
            }
            finally
            {
                response?.Close();
            }
        }

        public async Task<bool> RemoveFileFromSharePointAsync(string pathKey, string fileName)
        {
            string sharePointPath = _sharePointConfiguration.GetSharePointPath(pathKey);
            sharePointPath = Path.Combine(sharePointPath, fileName);

            try
            {
                WebRequest request = WebRequest.Create(sharePointPath);
                request.Method = "DELETE";

                if (_webServicesConfiguration.IsCredentialUsing)
                {
                    request.Credentials = new NetworkCredential(
                        _webServicesConfiguration.UserName,
                        _webServicesConfiguration.Password,
                        _webServicesConfiguration.Domain);
                }
                else
                {
                    request.Credentials = CredentialCache.DefaultCredentials;
                }

                using (WebResponse response = await request.GetResponseAsync())
                {
                    return true;
                }
            }
            catch (WebException ex) when ((ex.Response as HttpWebResponse)?.StatusCode == HttpStatusCode.NotFound)
            {
                // File not found, consider it as successfully deleted
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to delete file from SharePoint: {ex.Message}", ex);
            }
        }

        public async Task<UploadedFile> GetFileFromSharePointAsync(string pathKey, string fileName)
        {
            string sharePointPath = _sharePointConfiguration.GetSharePointPath(pathKey);
            sharePointPath = Path.Combine(sharePointPath, fileName);

            try
            {
                WebRequest request = WebRequest.Create(sharePointPath);
                request.Method = "GET";

                if (_webServicesConfiguration.IsCredentialUsing)
                {
                    request.Credentials = new NetworkCredential(
                        _webServicesConfiguration.UserName,
                        _webServicesConfiguration.Password,
                        _webServicesConfiguration.Domain);
                }
                else
                {
                    request.Credentials = CredentialCache.DefaultCredentials;
                }

                using (WebResponse response = await request.GetResponseAsync())
                using (Stream responseStream = response.GetResponseStream())
                using (MemoryStream ms = new MemoryStream())
                {
                    await responseStream.CopyToAsync(ms);
                    return new UploadedFile
                    {
                        FileName = fileName,
                        Content = ms.ToArray(),
                        LibraryName = pathKey
                    };
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to retrieve file from SharePoint: {ex.Message}", ex);
            }
        }

        public string GetPathFromSharePointAsync(string pathKey)
        {
            return _sharePointConfiguration.GetSharePointPath(pathKey);
        }
    }
}
