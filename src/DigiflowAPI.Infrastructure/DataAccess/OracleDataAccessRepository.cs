﻿using Oracle.ManagedDataAccess.Client;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Reflection;
using DigiflowAPI.Application.Interfaces.DataAccess;
using DigiflowAPI.Domain.Attributes;

namespace DigiflowAPI.Infrustructure.DataAccess
{
    public class OracleDataAccessRepository(IOracleConnectionManager connectionManager, string connectionStringKey) : IOracleDataAccessRepository
    {
        private static readonly Dictionary<Type, List<PropertyInfo>> _propertyCache = new();

        public async Task<List<T>> ExecuteQueryAsync<T>(string sql, Dictionary<string, object>? parameters = null) where T : new()
        {
            return await connectionManager.UseConnectionAsync(async connection =>
            {
                var items = new List<T>();

                using var command = new OracleCommand(sql, connection);
                AddParameters(command, parameters);

                using var reader = await command.ExecuteReaderAsync();
                var properties = GetProperties<T>();
                var columnNames = Enumerable.Range(0, reader.FieldCount).Select(reader.GetName).ToList();

                while (await reader.ReadAsync())
                {
                    var item = new T();
                    foreach (var prop in properties)
                    {
                        var columnName = prop.GetCustomAttribute<ColumnAttribute>()?.Name;
                        if (columnName != null && columnNames.Contains(columnName) && !reader.IsDBNull(reader.GetOrdinal(columnName)))
                        {
                            var value = reader[columnName];
                            var propType = Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType;
                            prop.SetValue(item, Convert.ChangeType(value, propType));
                        }
                    }
                    items.Add(item);
                }

                return items;
            }, connectionStringKey);
        }

        private async Task<List<object>> ExecuteQueryAsync(Type entityType, OracleConnection connection, string sql, Dictionary<string, object>? parameters = null)
        {
            var items = new List<object>();

            using var command = new OracleCommand(sql, connection);
            AddParameters(command, parameters);

            using var reader = await command.ExecuteReaderAsync();
            var properties = GetProperties(entityType);
            var columnNames = Enumerable.Range(0, reader.FieldCount).Select(reader.GetName).ToList();

            while (await reader.ReadAsync())
            {
                var item = Activator.CreateInstance(entityType);
                foreach (var prop in properties)
                {
                    var columnName = prop.GetCustomAttribute<ColumnAttribute>()?.Name;
                    if (columnName != null && columnNames.Contains(columnName) && !reader.IsDBNull(reader.GetOrdinal(columnName)))
                    {
                        var value = reader[columnName];
                        var propType = Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType;
                        prop.SetValue(item, Convert.ChangeType(value, propType));
                    }
                }
                items.Add(item);
            }

            return items;
        }

        public async Task<List<T>> ExecuteQueryAsync<T>(string sql, Func<OracleDataReader, T> mapFunction, Dictionary<string, object>? parameters = null)
        {
            return await connectionManager.UseConnectionAsync(async connection =>
            {
                var items = new List<T>();

                using var command = new OracleCommand(sql, connection);
                AddParameters(command, parameters);

                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    items.Add(mapFunction(reader));
                }

                return items;
            }, connectionStringKey);
        }

        public async Task<T?> ExecuteSingleQueryAsync<T>(string sql, Dictionary<string, object>? parameters = null) where T : new()
        {
            return await connectionManager.UseConnectionAsync(async connection =>
            {
                using var command = new OracleCommand(sql, connection);
                AddParameters(command, parameters);

                using var reader = await command.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    var item = new T();
                    var properties = GetProperties<T>();
                    foreach (var prop in properties)
                    {
                        var columnName = prop.GetCustomAttribute<ColumnAttribute>()?.Name;
                        if (columnName != null && !reader.IsDBNull(reader.GetOrdinal(columnName)))
                        {
                            var value = reader[columnName];
                            var propType = Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType;
                            prop.SetValue(item, Convert.ChangeType(value, propType));
                        }
                    }
                    return item;
                }
                return default;
            }, connectionStringKey);
        }

        public async Task<T?> ExecuteSingleQueryAsync<T>(string sql, Func<OracleDataReader, T> mapFunction, Dictionary<string, object>? parameters = null)
        {
            return await connectionManager.UseConnectionAsync(async connection =>
            {
                using var command = new OracleCommand(sql, connection);
                AddParameters(command, parameters);

                using var reader = await command.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    return mapFunction(reader);
                }
                return default;
            }, connectionStringKey);
        }

        public async Task<T?> ExecuteScalarAsync<T>(string sql, Dictionary<string, object>? parameters = null)
        {
            return await connectionManager.UseConnectionAsync(async connection =>
            {
                using var command = new OracleCommand(sql, connection);
                AddParameters(command, parameters);
                try
                {
                    var result = await command.ExecuteScalarAsync();
                    if (result == null || result == DBNull.Value)
                    {
                        return default;
                    }
                    return (T)Convert.ChangeType(result, typeof(T));
                }
                catch (InvalidCastException ex)
                {
                    Console.WriteLine($"Error casting result: {ex.Message}");
                    throw new InvalidOperationException($"Failed to cast query result to type {typeof(T).Name}: {ex.Message}", ex);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error executing scalar query: {ex.Message}");
                    throw new InvalidOperationException($"Failed to execute scalar query: {ex.Message}", ex);
                }
            }, connectionStringKey);
        }

        public async Task ExecuteProcedureAsync(string procedureName, Dictionary<string, object>? parameters = null)
        {
            await connectionManager.UseConnectionAsync(async connection =>
            {
                using var command = new OracleCommand(procedureName, connection);
                command.CommandType = CommandType.StoredProcedure;

                if (parameters != null)
                {
                    foreach (var param in parameters)
                    {
                        var parameter = command.CreateParameter();
                        parameter.ParameterName = param.Key;

                        if (param.Value is OracleParameter oracleParam)
                        {
                            parameter = oracleParam;
                        }
                        else
                        {
                            parameter.Value = param.Value ?? DBNull.Value;
                            parameter.Direction = ParameterDirection.Input;
                        }

                        command.Parameters.Add(parameter);
                    }
                }

                await command.ExecuteNonQueryAsync();
            }, connectionStringKey);
        }

        public async Task<T?> ExecuteProcedureAsync<T>(string procedureName, Dictionary<string, object>? parameters = null)
        {
            return await connectionManager.UseConnectionAsync(async connection =>
            {
                using var command = new OracleCommand(procedureName, connection);
                command.CommandType = CommandType.StoredProcedure;

                var outParameters = new List<OracleParameter>();

                if (parameters != null)
                {
                    foreach (var param in parameters)
                    {
                        var parameter = command.CreateParameter();
                        parameter.ParameterName = param.Key;

                        if (param.Value is OracleParameter oracleParam)
                        {
                            parameter = oracleParam;
                        }
                        else
                        {
                            parameter.Value = param.Value ?? DBNull.Value;
                            parameter.Direction = ParameterDirection.Input;
                        }

                        if (parameter.Direction != ParameterDirection.Input)
                        {
                            outParameters.Add(parameter);
                        }

                        command.Parameters.Add(parameter);
                    }
                }

                // Add a return value parameter
                var returnParameter = new OracleParameter
                {
                    ParameterName = "ReturnValue",
                    Direction = ParameterDirection.ReturnValue,
                    OracleDbType = GetOracleDbType(typeof(T))
                };
                command.Parameters.Add(returnParameter);

                await command.ExecuteNonQueryAsync();

                // Handle return value
                if (returnParameter.Value != DBNull.Value)
                {
                    return (T?)Convert.ChangeType(returnParameter.Value, typeof(T));
                }

                // Handle out parameters
                if (typeof(T) == typeof(Dictionary<string, object>))
                {
                    var result = new Dictionary<string, object>();
                    foreach (var param in outParameters)
                    {
                        result[param.ParameterName] = param.Value != DBNull.Value ? param.Value : null;
                    }
                    return (T?)(object)result;
                }

                // If no value was returned, return default (which is null for reference types)
                return default;
            }, connectionStringKey);
        }

        private OracleDbType GetOracleDbType(Type type)
        {
            type = Nullable.GetUnderlyingType(type) ?? type; // Handle nullable types

            if (type == typeof(int))
                return OracleDbType.Int32;
            if (type == typeof(long))
                return OracleDbType.Int64;
            if (type == typeof(string))
                return OracleDbType.Varchar2;
            if (type == typeof(DateTime))
                return OracleDbType.Date;
            if (type == typeof(decimal))
                return OracleDbType.Decimal;
            if (type == typeof(bool))
                return OracleDbType.Boolean;
            // Add more type mappings as needed
            throw new ArgumentException($"Unsupported type: {type.Name}");
        }
        public async Task<T?> ExecuteScalarAsyncForValueType<T>(string sql, Dictionary<string, object>? parameters = null)
            where T : struct
        {
            return await connectionManager.UseConnectionAsync(async connection =>
            {
                using var command = new OracleCommand(sql, connection);
                AddParameters(command, parameters);
                try
                {
                    var result = await command.ExecuteScalarAsync();
                    if (result == null || result == DBNull.Value)
                    {
                        return default;
                    }
                    return (T)Convert.ChangeType(result, typeof(T));
                }
                catch (InvalidCastException ex)
                {
                    Console.WriteLine($"Error casting result: {ex.Message}");
                    throw new InvalidOperationException($"Failed to cast query result to type {typeof(T).Name}: {ex.Message}", ex);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error executing scalar query: {ex.Message}");
                    throw new InvalidOperationException($"Failed to execute scalar query: {ex.Message}", ex);
                }
            }, connectionStringKey);
        }

        public async Task<int> ExecuteUpdateAsync(string sql, Dictionary<string, object>? parameters = null)
        {
            return await connectionManager.UseConnectionAsync(async connection =>
            {
                using var command = new OracleCommand(sql, connection);
                AddParameters(command, parameters);
                var result = await command.ExecuteNonQueryAsync();
                return result;
            }, connectionStringKey);
        }

        public async Task<T?> ExecuteInsertAsync<T>(string sql, Dictionary<string, object>? parameters = null)
        {
            return await connectionManager.UseConnectionAsync(async connection =>
            {
                sql += " returning ID into :ID_NEW";
                using var command = new OracleCommand(sql, connection);
                AddParameters(command, parameters);
                command.Parameters.Add("ID_NEW", OracleDbType.Int64).Direction = ParameterDirection.Output;
                await command.ExecuteNonQueryAsync();
                string outputVal = command.Parameters["ID_NEW"].Value.ToString();
                return (T)Convert.ChangeType(outputVal, typeof(T));
            }, connectionStringKey);
        }

        public async Task<object?> GetEntityAsync(Type entityType, string keyField, object keyValue)
        {
            return await connectionManager.UseConnectionAsync(async connection =>
            {
                try
                {
                    TableAttribute tableAttribute = entityType.GetCustomAttribute<TableAttribute>();
                    if (tableAttribute == null)
                        throw new InvalidOperationException($"TableAttribute not found on type {entityType.Name}");

                    PropertyInfo keyProperty = entityType.GetProperties().FirstOrDefault(prop =>
                        prop.GetCustomAttribute<ColumnAttribute>()?.Name?.ToLower() == keyField.ToLower());
                    if (keyProperty == null)
                        throw new InvalidOperationException($"No property found matching the key field '{keyField}' on type {entityType.Name}");

                    PropertyInfo[] properties = entityType.GetProperties();
                    var fetchedEntities = new Dictionary<string, object>();

                    // Construct SQL for main entity
                    string mainQuery = $"SELECT * FROM {tableAttribute.Schema}.{tableAttribute.Name} WHERE {keyField} = :Key";
                    var parameters = new Dictionary<string, object> { { "Key", keyValue } };
                    var result = await ExecuteQueryAsync(entityType, connection, mainQuery, parameters);

                    var entity = result.FirstOrDefault();
                    if (entity == null) return null;

                    fetchedEntities.Add(tableAttribute.Name, entity);

                    // Recursively fetch related entities
                    await FetchRelatedEntitiesAsync(connection, entity, properties, fetchedEntities);

                    return entity;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error fetching entity: {ex.Message}");
                    return null;
                }
            }, connectionStringKey);
        }

        public async Task<T?> GetEntityAsync<T>(object key) where T : class, new()
        {
            return await connectionManager.UseConnectionAsync(async connection =>
            {
                try
                {
                    Type type = typeof(T);
                    TableAttribute tableAttribute = type.GetCustomAttribute<TableAttribute>();
                    if (tableAttribute == null) throw new InvalidOperationException($"TableAttribute not found on type {type.Name}");

                    PropertyInfo keyProperty = type.GetProperties().FirstOrDefault(prop => prop.GetCustomAttribute<KeyAttribute>() != null);
                    if (keyProperty == null) throw new InvalidOperationException($"No property marked with [Key] attribute found on type {type.Name}");

                    PropertyInfo[] properties = type.GetProperties();
                    var fetchedEntities = new Dictionary<string, object>();

                    // Construct SQL for main entity
                    string mainQuery = $"SELECT * FROM {tableAttribute.Schema}.{tableAttribute.Name} WHERE {keyProperty.GetCustomAttribute<ColumnAttribute>().Name} = :Key";
                    var parameters = new Dictionary<string, object> { { "Key", key } };
                    var result = await ExecuteQueryAsync(typeof(T), connection, mainQuery, parameters);
                    T entity = result.FirstOrDefault() as T;
                    if (entity == null) return default;

                    fetchedEntities.Add(tableAttribute.Name, entity);

                    // Recursively fetch related entities
                    await FetchRelatedEntitiesAsync(connection, entity, properties, fetchedEntities);

                    return entity;
                }
                catch (Exception ex)
                {
                    // Log the exception (implement your logging mechanism here)
                    Console.Error.WriteLine($"Error fetching entity: {ex.Message}");
                    return default;
                }
            }, connectionStringKey);
        }

        public async Task<T?> GetEntityAsync<T>(string keyField, object keyValue) where T : class, new()
        {
            return await connectionManager.UseConnectionAsync(async connection =>
            {
                try
                {
                    Type type = typeof(T);
                    TableAttribute tableAttribute = type.GetCustomAttribute<TableAttribute>();
                    if (tableAttribute == null) throw new InvalidOperationException($"TableAttribute not found on type {type.Name}");

                    PropertyInfo keyProperty = type.GetProperties().FirstOrDefault(prop =>
                        prop.GetCustomAttribute<ColumnAttribute>()?.Name?.ToLower() == keyField.ToLower());
                    if (keyProperty == null) throw new InvalidOperationException($"No property found matching the key field '{keyField}' on type {type.Name}");

                    PropertyInfo[] properties = type.GetProperties();
                    var fetchedEntities = new Dictionary<string, object>();

                    // Construct SQL for main entity
                    string mainQuery = $"SELECT * FROM {tableAttribute.Schema}.{tableAttribute.Name} WHERE {keyField} = :Key";
                    var parameters = new Dictionary<string, object> { { "Key", keyValue } };
                    var result = await ExecuteQueryAsync(typeof(T), connection, mainQuery, parameters);
                    T entity = result.FirstOrDefault() as T;
                    if (entity == null) return default;

                    fetchedEntities.Add(tableAttribute.Name, entity);

                    // Recursively fetch related entities
                    await FetchRelatedEntitiesAsync(connection, entity, properties, fetchedEntities);

                    return entity;
                }
                catch (Exception ex)
                {
                    // Log the exception (implement your logging mechanism here)
                    Console.Error.WriteLine($"Error fetching entity: {ex.Message}");
                    return default;
                }
            }, connectionStringKey);
        }


        public async Task<T?> GetInheritedEntityAsync<T>(object key) where T : class, new()
        {
            return await connectionManager.UseConnectionAsync(async connection =>
            {
                try
                {
                    Type type = typeof(T);
                    var tableHierarchy = GetTableHierarchy(type);
                    var fetchedEntities = new Dictionary<string, object>();

                    T? entity = null;
                    Dictionary<Type, object> hierarchyEntities = new Dictionary<Type, object>();

                    foreach (var (currentType, tableAttribute) in tableHierarchy)
                    {
                        PropertyInfo keyProperty = GetInheritedKeyProperty(currentType);
                        if (keyProperty == null) continue;

                        string mainQuery = $"SELECT * FROM {tableAttribute.Schema}.{tableAttribute.Name} WHERE {keyProperty.GetCustomAttribute<ColumnAttribute>()?.Name ?? keyProperty.Name} = :Key";
                        var parameters = new Dictionary<string, object> { { "Key", key } };
                        var result = await ExecuteQueryAsync(currentType, connection, mainQuery, parameters);

                        if (result.FirstOrDefault() is object currentEntity)
                        {
                            hierarchyEntities[currentType] = currentEntity;
                            fetchedEntities[tableAttribute.Name] = currentEntity;
                        }
                    }

                    if (hierarchyEntities.TryGetValue(type, out var finalEntity))
                    {
                        entity = finalEntity as T;

                        // Merge properties from base entities
                        foreach (var baseType in tableHierarchy.Select(t => t.Type).Where(t => t != type))
                        {
                            if (hierarchyEntities.TryGetValue(baseType, out var baseEntity))
                            {
                                MergeEntities(entity, baseEntity);
                            }
                        }
                    }

                    if (entity == null) return null;

                    // Fetch related entities
                    await FetchRelatedEntitiesAsync(connection, entity, type.GetProperties(), fetchedEntities);

                    return entity;
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Error fetching inherited entity: {ex.Message}");
                    return null;
                }
            }, connectionStringKey);
        }

        public async Task<List<T>> GetEntityListAsync<T>() where T : class, new()
        {
            return await connectionManager.UseConnectionAsync(async connection =>
            {
                try
                {
                    Type type = typeof(T);
                    TableAttribute tableAttribute = type.GetCustomAttribute<TableAttribute>();
                    if (tableAttribute == null)
                        throw new InvalidOperationException($"TableAttribute not found on type {type.Name}");

                    PropertyInfo[] properties = type.GetProperties();
                    var fetchedEntities = new Dictionary<string, object>();

                    // Construct SQL for main entity
                    string mainQuery = $"SELECT * FROM {tableAttribute.Schema}.{tableAttribute.Name}";
                    var result = await ExecuteQueryAsync(typeof(T), connection, mainQuery, null);

                    var entities = result.Cast<T>().ToList();
                    if (!entities.Any()) return new List<T>();

                    // For each entity, fetch related entities
                    foreach (var entity in entities)
                    {
                        await FetchRelatedEntitiesAsync(connection, entity, properties, new Dictionary<string, object>());
                    }

                    return entities;
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Error fetching entity list: {ex.Message}");
                    return new List<T>();
                }
            }, connectionStringKey);
        }

        public async Task<List<T>> GetEntityListAsync<T>(Dictionary<string, object> filters) where T : class, new()
        {
            return await connectionManager.UseConnectionAsync(async connection =>
            {
                try
                {
                    Type type = typeof(T);
                    TableAttribute tableAttribute = type.GetCustomAttribute<TableAttribute>();
                    if (tableAttribute == null)
                        throw new InvalidOperationException($"TableAttribute not found on type {type.Name}");

                    PropertyInfo[] properties = type.GetProperties();
                    var fetchedEntities = new Dictionary<string, object>();

                    // Build WHERE clause from filters
                    var whereConditions = new List<string>();
                    var parameters = new Dictionary<string, object>();

                    foreach (var filter in filters)
                    {
                        var prop = properties.FirstOrDefault(p =>
                            p.GetCustomAttribute<ColumnAttribute>()?.Name?.ToLower() == filter.Key.ToLower());

                        if (prop != null)
                        {
                            var columnName = prop.GetCustomAttribute<ColumnAttribute>().Name;
                            whereConditions.Add($"{columnName} = :{filter.Key}");
                            parameters.Add(filter.Key, filter.Value);
                        }
                    }

                    // Construct SQL for main entity
                    string mainQuery = $"SELECT * FROM {tableAttribute.Schema}.{tableAttribute.Name}";
                    if (whereConditions.Any())
                    {
                        mainQuery += $" WHERE {string.Join(" AND ", whereConditions)}";
                    }

                    var result = await ExecuteQueryAsync(typeof(T), connection, mainQuery, parameters);
                    var entities = result.Cast<T>().ToList();

                    if (!entities.Any()) return new List<T>();

                    // For each entity, fetch related entities
                    foreach (var entity in entities)
                    {
                        await FetchRelatedEntitiesAsync(connection, entity, properties, new Dictionary<string, object>());
                    }

                    return entities;
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Error fetching filtered entity list: {ex.Message}");
                    return new List<T>();
                }
            }, connectionStringKey);
        }

        public async Task<List<T>> GetInheritedEntityListAsync<T>() where T : class, new()
        {
            return await connectionManager.UseConnectionAsync(async connection =>
            {
                try
                {
                    Type type = typeof(T);
                    var tableHierarchy = GetTableHierarchy(type);
                    var entities = new List<T>();
                    var baseEntities = new Dictionary<object, Dictionary<Type, object>>();

                    // Get the primary key column name
                    PropertyInfo keyProperty = GetInheritedKeyProperty(type);
                    if (keyProperty == null) return new List<T>();

                    // Start with the most derived type
                    var mainTable = tableHierarchy.Last();
                    string mainQuery = $"SELECT * FROM {mainTable.Attribute.Schema}.{mainTable.Attribute.Name}";
                    var mainResults = await ExecuteQueryAsync(type, connection, mainQuery, null);

                    foreach (var mainEntity in mainResults)
                    {
                        var hierarchyEntities = new Dictionary<Type, object>();
                        hierarchyEntities[type] = mainEntity;

                        // Get the key value from the main entity
                        var keyValue = keyProperty.GetValue(mainEntity);
                        if (keyValue == null) continue;

                        // Fetch data from base tables
                        foreach (var (baseType, baseTableAttr) in tableHierarchy.Where(t => t.Type != type))
                        {
                            string baseQuery = $"SELECT * FROM {baseTableAttr.Schema}.{baseTableAttr.Name} WHERE {keyProperty.GetCustomAttribute<ColumnAttribute>()?.Name ?? keyProperty.Name} = :Key";
                            var parameters = new Dictionary<string, object> { { "Key", keyValue } };
                            var baseResults = await ExecuteQueryAsync(baseType, connection, baseQuery, parameters);

                            if (baseResults.FirstOrDefault() is object baseEntity)
                            {
                                hierarchyEntities[baseType] = baseEntity;
                            }
                        }

                        baseEntities[keyValue] = hierarchyEntities;

                        // Create the final entity by merging properties
                        T finalEntity = mainEntity as T;
                        foreach (var baseType in tableHierarchy.Select(t => t.Type).Where(t => t != type))
                        {
                            if (hierarchyEntities.TryGetValue(baseType, out var baseEntity))
                            {
                                MergeEntities(finalEntity, baseEntity);
                            }
                        }

                        // Fetch related entities
                        await FetchRelatedEntitiesAsync(connection, finalEntity, type.GetProperties(), new Dictionary<string, object>());
                        entities.Add(finalEntity);
                    }

                    return entities;
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Error fetching inherited entity list: {ex.Message}");
                    return new List<T>();
                }
            }, connectionStringKey);
        }

        private List<(Type Type, TableAttribute Attribute)> GetTableHierarchy(Type type)
        {
            var hierarchy = new List<(Type, TableAttribute)>();
            while (type != null && type != typeof(object))
            {
                var tableAttribute = type.GetCustomAttribute<TableAttribute>();
                if (tableAttribute != null)
                {
                    hierarchy.Add((type, tableAttribute));
                }
                type = type.GetCustomAttribute<TableInheritanceAttribute>()?.ParentType ?? type.BaseType;
            }
            hierarchy.Reverse(); // Start from the base class
            return hierarchy;
        }

        private void MergeEntities<T>(T target, object source) where T : class
        {
            var targetType = typeof(T);
            var sourceType = source.GetType();
            var targetProperties = targetType.GetProperties();
            var sourceProperties = sourceType.GetProperties();

            foreach (var sourceProp in sourceProperties)
            {
                var targetProp = targetProperties.FirstOrDefault(p => p.Name == sourceProp.Name && p.PropertyType == sourceProp.PropertyType);
                if (targetProp != null && targetProp.CanWrite)
                {
                    var value = sourceProp.GetValue(source);
                    if (value != null)
                    {
                        targetProp.SetValue(target, value);
                    }
                }
            }
        }

        private PropertyInfo GetInheritedKeyProperty(Type type)
        {
            return type.GetProperties().FirstOrDefault(p =>
                p.GetCustomAttribute<KeyAttribute>() != null ||
                p.Name.Equals("Id", StringComparison.OrdinalIgnoreCase) ||
                p.Name.Equals($"{type.Name}Id", StringComparison.OrdinalIgnoreCase));
        }

        private async Task FetchRelatedEntitiesAsync<T>(OracleConnection connection, T entity, PropertyInfo[] properties, Dictionary<string, object> fetchedEntities)
        {
            foreach (var property in properties)
            {
                var foreignKeyMappingAttr = property.GetCustomAttribute<ForeignKeyMappingAttribute>();

                if (foreignKeyMappingAttr != null)
                {
                    Type relatedType = foreignKeyMappingAttr.ForeignTable;
                    TableAttribute relatedTableAttr = relatedType.GetCustomAttribute<TableAttribute>();
                    if (relatedTableAttr == null) continue;

                    string relatedTableName = relatedTableAttr.Name;
                    if (!fetchedEntities.ContainsKey(relatedTableName))
                    {
                        PropertyInfo keyProperty = properties.FirstOrDefault(p => p.Name == foreignKeyMappingAttr.ForeignKey);
                        if (keyProperty == null) continue;
                        object foreignKeyValue = keyProperty.GetValue(entity);
                        if (foreignKeyValue == null) continue;

                        string relatedQuery = $"SELECT * FROM {relatedTableAttr.Schema}.{relatedTableName} WHERE {foreignKeyMappingAttr.ForeignField} = :ForeignKey";
                        var parameters = new Dictionary<string, object> { { "ForeignKey", foreignKeyValue } };

                        var query = await ExecuteQueryAsync(relatedType, connection, relatedQuery, parameters);
                        var relatedEntity = query.FirstOrDefault();
                        if (relatedEntity != null)
                        {
                            fetchedEntities.Add(relatedTableName, relatedEntity);

                            // Set the related entity for all matching properties
                            foreach (var prop in properties)
                            {
                                if (prop.PropertyType == relatedType)
                                {
                                    prop.SetValue(entity, relatedEntity);
                                }
                            }

                            PropertyInfo[] relatedProperties = relatedType.GetProperties();
                            await FetchRelatedEntitiesAsync(connection, relatedEntity, relatedProperties, fetchedEntities);
                        }
                    }
                    else
                    {
                        // If the related entity is already fetched, use it to populate all matching properties
                        var existingRelatedEntity = fetchedEntities[relatedTableName];
                        foreach (var prop in properties)
                        {
                            if (prop.PropertyType == relatedType)
                            {
                                prop.SetValue(entity, existingRelatedEntity);
                            }
                        }
                    }
                }
            }
        }
        private object PopulateEntityFromReader(Type entityType, OracleDataReader reader)
        {
            object entity = Activator.CreateInstance(entityType);
            PropertyInfo[] properties = entityType.GetProperties();

            foreach (var property in properties)
            {
                var columnAttr = property.GetCustomAttribute<ColumnAttribute>();
                if (columnAttr != null)
                {
                    string columnName = columnAttr.Name;
                    if (!reader.IsDBNull(reader.GetOrdinal(columnName)))
                    {
                        property.SetValue(entity, reader[columnName]);
                    }
                }
            }

            return entity;
        }

        private PropertyInfo GetKeyProperty(Type type)
        {
            return type.GetProperties().FirstOrDefault(p => Attribute.IsDefined(p, typeof(KeyAttribute)));
        }

        private TAttr GetCustomAttribute<TAttr>(Type type) where TAttr : Attribute
        {
            return (TAttr)type?.GetCustomAttributes(typeof(TAttr), false).FirstOrDefault();
        }

        private static void AddParameters(OracleCommand command, Dictionary<string, object>? parameters)
        {
            if (parameters != null)
            {
                command.BindByName = true;
                foreach (var param in parameters)
                {
                    command.Parameters.Add(new OracleParameter(param.Key, param.Value));
                }
            }
        }

        private List<PropertyInfo> GetProperties<T>()
        {
            if (!_propertyCache.TryGetValue(typeof(T), out var properties))
            {
                properties = typeof(T).GetProperties()
                    .Where(prop => prop.GetCustomAttributes(typeof(ColumnAttribute), false).Any() || prop.PropertyType.IsClass && !prop.PropertyType.Namespace.StartsWith("System"))
                    .ToList();
                _propertyCache[typeof(T)] = properties;
            }
            return properties;
        }

        private List<PropertyInfo> GetProperties(Type type)
        {
            if (!_propertyCache.TryGetValue(type, out var properties))
            {
                properties = type.GetProperties()
                    .Where(prop => prop.GetCustomAttributes(typeof(ColumnAttribute), false).Any() || prop.PropertyType.IsClass && !prop.PropertyType.Namespace.StartsWith("System"))
                    .ToList();
                _propertyCache[type] = properties;
            }
            return properties;
        }
    }
}