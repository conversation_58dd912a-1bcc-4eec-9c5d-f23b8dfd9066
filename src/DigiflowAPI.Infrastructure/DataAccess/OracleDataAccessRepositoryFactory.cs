﻿using DigiflowAPI.Application.Interfaces.DataAccess;
using Microsoft.Extensions.DependencyInjection;

namespace DigiflowAPI.Infrustructure.DataAccess
{
    public class OracleDataAccessRepositoryFactory(IServiceProvider serviceProvider) : IOracleDataAccessRepositoryFactory
    {
        private readonly Dictionary<string, IOracleDataAccessRepository> _cache = [];

        public IOracleDataAccessRepository Create(string connectionStringKey)
        {
            if (string.IsNullOrWhiteSpace(connectionStringKey))
                throw new ArgumentException("Connection string key must be provided.", nameof(connectionStringKey));

            try
            {
                if (!_cache.TryGetValue(connectionStringKey, out var repository))
                {
                    var manager = serviceProvider.GetRequiredService<IOracleConnectionManager>();
                    repository = new OracleDataAccessRepository(manager, connectionStringKey);
                    _cache[connectionStringKey] = repository;
                }
                return repository;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to create repository.", ex);
            }
        }
    }
}
