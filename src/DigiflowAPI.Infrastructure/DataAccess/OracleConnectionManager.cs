﻿using DigiflowAPI.Application.Interfaces.DataAccess;
using Microsoft.Extensions.Configuration;
using Oracle.ManagedDataAccess.Client;
using System.Configuration;
using System.Reflection;
using System.Xml;

namespace DigiflowAPI.Infrustructure.DataAccess
{
    public class OracleConnectionManager : IOracleConnectionManager
    {
        private readonly IConfiguration _configuration;
        private readonly XmlDocument _xmlDoc;

        public OracleConnectionManager(IConfiguration configuration)
        {
            _configuration = configuration;
            _xmlDoc = new XmlDocument();
            _xmlDoc.Load("./app.config");
        }

        public async Task<T> UseConnectionAsync<T>(Func<OracleConnection, Task<T>> func, string connectionStringKey)
        {
            string connectionString = ReturnConnStr(connectionStringKey);
            await using var connection = new OracleConnection(connectionString);
            await connection.OpenAsync();
            try
            {
                return await func(connection);
            }
            finally
            {
                await connection.CloseAsync();
            }
        }

        public async Task UseConnectionAsync(Func<OracleConnection, Task> func, string connectionStringKey)
        {
            string connectionString = ReturnConnStr(connectionStringKey);
            await using var connection = new OracleConnection(connectionString);
            await connection.OpenAsync();
            try
            {
                await func(connection);
            }
            finally
            {
                await connection.CloseAsync();
            }
        }

        private async Task<string> WebConfigConnectionString(string StrDataBase, string StrUser)
        {
            string FinderConnStr = "", ApplicationStr = "", UniqueKeyStr = "", StrPass = "", ReturnConnStr = "", WebConfigPass = "", PasswordBoxKullan = "", DBConnString = "";

            try
            {
                PasswordBoxKullan = GetAppSettingValue("ESSBDurum");
            }
            catch (Exception)
            {
                throw new ArgumentException("Config Dosyasında ESSBDurum key'i bulunamadı !");
            }

            try
            {
                FinderConnStr = GetConnectionString("connStrESSB");
            }
            catch (Exception)
            {
                throw new ArgumentException("Config Dosyasında connStrESSB bulunamadı !");
            }

            if (PasswordBoxKullan == "E")
            {
                try
                {
                    DBConnString = GetConnectionString(StrDataBase);
                }
                catch (Exception)
                {
                    throw new ArgumentException($"Config Dosyasında {StrDataBase} connection string'i bulunamadı !");
                }

                try
                {
                    ApplicationStr = GetAppSettingValue($"{StrDataBase}_{StrUser}_APPSTR");
                }
                catch (Exception)
                {
                    throw new ArgumentException($"Config Dosyasında {StrDataBase}_{StrUser}_APPSTR key'i bulunamadı !");
                }

                try
                {
                    UniqueKeyStr = GetAppSettingValue($"{StrDataBase}_{StrUser}_UNIQUE");
                }
                catch (Exception)
                {
                    throw new ArgumentException($"Config Dosyasında {StrDataBase}_{StrUser}_UNIQUE key'i bulunamadı !");
                }

                StrPass = await GetPasswordFromPasswordBoxAsync(FinderConnStr, StrDataBase, StrUser, ApplicationStr, UniqueKeyStr);
            }

            if (PasswordBoxKullan == "E")
            {
                if (!string.IsNullOrEmpty(StrPass))
                {
                    ReturnConnStr = string.Format(DBConnString, StrUser, StrPass);
                }
            }
            else
            {
                try
                {
                    WebConfigPass = GetAppSettingValue($"{StrDataBase}_{StrUser}");
                }
                catch (Exception)
                {
                    throw new ArgumentException($"Config Dosyasında {StrDataBase}_{StrUser}'i bulunamadı !");
                }

                ReturnConnStr = string.Format(FinderConnStr, StrUser, WebConfigPass);
            }

            if (string.IsNullOrEmpty(ReturnConnStr))
            {
                throw new ArgumentException($"WebConfigConnectionString fonksiyonundan connection string döndürülemedi {StrDataBase}_{StrUser}");
            }

            return ReturnConnStr;
        }

        private async Task<string> WebConfigConnectionString(string StrDataBase, string StrUser, string TestOrLive)
        {
            string FinderConnStr = "", ApplicationStr = "", UniqueKeyStr = "", StrPass = "", ReturnConnStr = "", WebConfigPass = "", PasswordBoxKullan = "", DBConnString = "";

            try
            {
                PasswordBoxKullan = GetAppSettingValue("ESSBDurum");
            }
            catch (Exception)
            {
                throw new ArgumentException("Config Dosyasında ESSBDurum key'i bulunamadı !");
            }

            try
            {
                FinderConnStr = GetConnectionString($"connStrESSB_{TestOrLive}");
            }
            catch (Exception)
            {
                throw new ArgumentException($"Config Dosyasında connStrESSB_{TestOrLive} key'i bulunamadı !");
            }

            if (PasswordBoxKullan == "E")
            {
                try
                {
                    DBConnString = GetConnectionString(StrDataBase);
                }
                catch (Exception)
                {
                    throw new ArgumentException($"Config Dosyasında {StrDataBase} connection string'i bulunamadı !");
                }

                try
                {
                    ApplicationStr = GetAppSettingValue($"{StrDataBase}_{StrUser}_APPSTR");
                }
                catch (Exception)
                {
                    throw new ArgumentException($"Config Dosyasında {StrDataBase}_{StrUser}_APPSTR key'i bulunamadı !");
                }

                try
                {
                    UniqueKeyStr = GetAppSettingValue($"{StrDataBase}_{StrUser}_UNIQUE");
                }
                catch (Exception)
                {
                    throw new ArgumentException($"Config Dosyasında {StrDataBase}_{StrUser}_UNIQUE key'i bulunamadı !");
                }

                StrPass = await GetPasswordFromPasswordBoxAsync(FinderConnStr, StrDataBase, StrUser, ApplicationStr, UniqueKeyStr);
            }

            if (PasswordBoxKullan == "E")
            {
                if (!string.IsNullOrEmpty(StrPass))
                {
                    ReturnConnStr = string.Format(DBConnString, StrUser, StrPass);
                }
            }
            else
            {
                try
                {
                    WebConfigPass = GetAppSettingValue($"{StrDataBase}_{StrUser}");
                }
                catch (Exception)
                {
                    throw new ArgumentException($"Config Dosyasında {StrDataBase}_{StrUser}'i bulunamadı !");
                }

                try
                {
                    DBConnString = GetConnectionString(StrDataBase);
                }
                catch (Exception)
                {
                    throw new ArgumentException($"Config Dosyasında {StrDataBase} connection string'i bulunamadı !");
                }

                ReturnConnStr = string.Format(DBConnString, StrUser, WebConfigPass);
            }

            if (string.IsNullOrEmpty(ReturnConnStr))
            {
                throw new ArgumentException($"WebConfigConnectionString fonksiyonundan connection string döndürülemedi {StrDataBase}_{StrUser}");
            }

            return ReturnConnStr;
        }

        private async Task<string> GetPasswordFromPasswordBoxAsync(string finderConnStr, string dataSource, string userId, string uygulamaAdi, string uygulamaKey)
        {
            //how to add System.Data.OracleClient as provider
            await using var connection = new OracleConnection(finderConnStr);
            await connection.OpenAsync();

            using var command = connection.CreateCommand();
            command.CommandText = "SELECT ESSB_USR.CLIENT_PKG.GET_DB_PWD(:dataSource, :userId, :uygulamaAdi, :uygulamaKey) AS PASSWORD FROM DUAL";
            command.Parameters.Add("dataSource", OracleDbType.Varchar2).Value = dataSource;
            command.Parameters.Add("userId", OracleDbType.Varchar2).Value = userId;
            command.Parameters.Add("uygulamaAdi", OracleDbType.Varchar2).Value = uygulamaAdi;
            command.Parameters.Add("uygulamaKey", OracleDbType.Varchar2).Value = uygulamaKey;

            var result = await command.ExecuteScalarAsync();
            return result?.ToString();
        }

        private string GetAppSettingValue(string key)
        {
            var appSettingsNode = _xmlDoc.SelectSingleNode("//appSettings");
            if (appSettingsNode != null)
            {
                foreach (XmlNode node in appSettingsNode.ChildNodes)
                {
                    if (node.Attributes != null && node.Attributes["key"]?.Value == key)
                    {
                        return node.Attributes["value"]?.Value;
                    }
                }
            }

            return _configuration[$"AppSettings:{key}"];
        }

        private string GetConnectionString(string key)
        {
            var connectionStringsNode = _xmlDoc.SelectSingleNode("//connectionStrings");
            if (connectionStringsNode != null)
            {
                foreach (XmlNode node in connectionStringsNode.ChildNodes)
                {
                    if (node.Attributes != null && node.Attributes["name"]?.Value == key)
                    {
                        return node.Attributes["connectionString"]?.Value;
                    }
                }
            }

            return _configuration.GetConnectionString(key);
        }

        public string ReturnConnStr(string ConnStr)
        {
            if (ConnStr == "DBSConnection")
            {
                var DBSConnection_settings = GetConnectionString("DBSConnection");
                var configElement = typeof(ConfigurationElement).GetField("_bReadOnly", BindingFlags.Instance | BindingFlags.NonPublic);
                configElement?.SetValue(DBSConnection_settings, false);

                if (GetAppSettingValue("Workflow.Mail.IsMailDebugMode") == "True")
                {
                    return WebConfigConnectionString("SUBSET15", "INQUIRY", "TEST").Result;
                }
                else
                {
                    return WebConfigConnectionString("DBSLIVE", "INQUIRY", "LIVE").Result;
                }
            }

            return _configuration.GetConnectionString(ConnStr);
        }
    }
}