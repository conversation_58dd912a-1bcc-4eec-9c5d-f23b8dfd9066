﻿using System.Security.Principal;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Domain.Entities.Framework;
using DigiflowAPI.Application.Interfaces.DataAccess;

namespace DigiflowAPI.Infrastructure.Helpers
{
    public class GlobalHelpers : IGlobalHelpers
    {
        private readonly IConfiguration configuration;
        private readonly IHttpContextAccessor httpContextAccessor;
        private readonly IOracleDataAccessRepositoryFactory repositoryFactory;

        public GlobalHelpers(IConfiguration _configuration, IHttpContextAccessor _httpContextAccessor, IOracleDataAccessRepositoryFactory _repositoryFactory)
        {
            configuration = _configuration;
            httpContextAccessor = _httpContextAccessor;
            repositoryFactory = _repositoryFactory;
        }

        public bool IsDebugModeActive()
        {
            string environmentVariable = configuration["ASPNETCORE_ENVIRONMENT"];
            return string.Equals(environmentVariable, "Development", StringComparison.OrdinalIgnoreCase);
        }
        public bool IsSystemAdmin(string? username = null)
        {
            string sysAdminUsers = configuration["SYSTEM_ADMIN_USERS"];
            if (!string.IsNullOrEmpty(sysAdminUsers))
            {
                string activeUserName = username ?? GetUserName();
                return sysAdminUsers.Split(";").Any(user => user.Trim().Equals(activeUserName, StringComparison.OrdinalIgnoreCase));
            }
            return false;
        }

        public List<string> SystemAdmins()
        {
            string sysAdminUsers = configuration["SYSTEM_ADMIN_USERS"];
            if (!string.IsNullOrEmpty(sysAdminUsers))
            {
                return new List<string>(sysAdminUsers.Split(";"));
            }
            return [];
        }

        public string CompatibilityApiUrl()
        {
            return configuration["COMPATIBILITY_LAYER_URL"];
        }

        public string GetUserName()
        {
            var httpContext = httpContextAccessor.HttpContext;
            if (httpContext == null)
                return "";

            // Check if this is a JWT authentication
            if (httpContext.Items.TryGetValue("JwtUsername", out var jwtUsername) && jwtUsername != null)
            {
                return jwtUsername.ToString();
            }

            // Check for Windows authentication even if IsAuthenticated is false
            // This handles the case where anonymous auth is enabled but Windows credentials are provided
            var windowsIdentity = httpContext.User?.Identity as WindowsIdentity;
            if (windowsIdentity != null && !string.IsNullOrEmpty(windowsIdentity.Name))
            {
                string username = windowsIdentity.Name;
                // Parse out the domain
                if (username.Contains('\\'))
                {
                    return username[(username.IndexOf('\\') + 1)..];
                }
                return username;
            }

            // Check for any authenticated identity (JWT or other claims-based)
            if (httpContext.User?.Identity?.IsAuthenticated == true && !string.IsNullOrEmpty(httpContext.User.Identity.Name))
            {
                return httpContext.User.Identity.Name;
            }

            // Additional check: Look for Windows authentication headers
            // When Windows auth negotiation is in progress, these headers might be present
            if (httpContext.Request.Headers.ContainsKey("Authorization"))
            {
                var authHeader = httpContext.Request.Headers["Authorization"].ToString();
                if (authHeader.StartsWith("Negotiate") || authHeader.StartsWith("NTLM"))
                {
                    // Windows auth is being attempted but not yet completed
                    // You might want to handle this case differently
                    return "";
                }
            }

            return "";
        }
        public async Task<long> GetUserId()
        {
            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var sql = @"SELECT * FROM FRAMEWORK.F_LOGIN WHERE LOGIN_NAME = :ActiveUserName";

            var currentUserName = GetUserName();
            var parameters = new Dictionary<string, object>
                {
                    { ":ActiveUserName", currentUserName.ToUpper() },
                };

            var result = await repository.ExecuteSingleQueryAsync<FLogin>(sql, parameters);
            if (result == null)
            {
                return 0;
            }
            return result.LoginId;
        }
    }
}