using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace DigiflowAPI.Infrastructure.Helpers
{
  /// <summary>
  /// Service for initializing workflow type information in the database
  /// </summary>
  public class WorkflowTypeInitializer : IHostedService
  {
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<WorkflowTypeInitializer> _logger;

    public WorkflowTypeInitializer(
        IServiceProvider serviceProvider,
        ILogger<WorkflowTypeInitializer> logger)
    {
      _serviceProvider = serviceProvider;
      _logger = logger;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
      _logger.LogInformation("Starting workflow type initialization...");

      try
      {
        using var scope = _serviceProvider.CreateScope();
        var workflowTypeHelper = scope.ServiceProvider.GetRequiredService<WorkflowTypeHelper>();

        _logger.LogInformation("Workflow type initialization completed successfully.");
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "An error occurred during workflow type initialization.");
      }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
      return Task.CompletedTask;
    }
  }
}
