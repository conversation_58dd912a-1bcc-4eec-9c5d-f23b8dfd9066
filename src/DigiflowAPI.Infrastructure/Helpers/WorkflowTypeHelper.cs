using DigiflowAPI.Application.DTOs.Workflow.Responses;
using DigiflowAPI.Application.Interfaces.DataAccess;
using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Domain.Entities.Workflow;
using Microsoft.Extensions.Caching.Memory;
using System.ComponentModel.DataAnnotations.Schema;

namespace DigiflowAPI.Infrastructure.Helpers
{
    /// <summary>
    /// Helper class for resolving workflow types, entities, and workflow definition IDs from database
    /// </summary>
    public class WorkflowTypeHelper : IWorkflowTypeHelper
    {
        private readonly IOracleDataAccessRepositoryFactory _repositoryFactory;
        private readonly IMemoryCache _memoryCache;
        private const string WorkflowDefsCacheKey = "WorkflowDefinitions";
        private static readonly TimeSpan CacheDuration = TimeSpan.FromHours(1);

        // Type mappings for resolving types at runtime
        private static readonly Dictionary<string, Type> _workflowTypeMapping = new()
        {
        };

        private static readonly Dictionary<string, Type> _workflowEntityMapping = new()
        {
        };

        private static readonly Dictionary<string, Type> _workflowServiceMapping = new()
        {
        };

        // Static list to hold seeded workflow definitions
        private static List<WorkflowDefinition> _staticWorkflowDefinitions = new List<WorkflowDefinition>();

        /// <summary>
        /// Constructor for dependency injection
        /// </summary>
        public WorkflowTypeHelper(IOracleDataAccessRepositoryFactory repositoryFactory, IMemoryCache memoryCache)
        {
            _repositoryFactory = repositoryFactory;
            _memoryCache = memoryCache;
        }

        /// <summary>
        /// DTO class to map the workflow definition query results
        /// </summary>
        [Table("WF_XML_DEFINITION")]
        private class WorkflowDefinitionDto
        {
            [Column("ROUTE")]
            public string? Route { get; set; }

            [Column("VALUE")]
            public string? Value { get; set; }

            [Column("FLOWNAME")]
            public string? FlowName { get; set; }

            [Column("TABLENAME")]
            public string? TableName { get; set; }

            [Column("FIELDNAME")]
            public string? FieldName { get; set; }

            [Column("RESPONSE_TYPE_NAME")]
            public string? ResponseTypeName { get; set; }

            [Column("ENTITY_TYPE_NAME")]
            public string? EntityTypeName { get; set; }

            [Column("SERVICE_TYPE_NAME")]
            public string? ServiceTypeName { get; set; }

            [Column("DIGIFLOW_ENTITY_NAME")]
            public string? DigiflowEntityName { get; set; }
        }

        /// <summary>
        /// Gets all workflow definitions from the database and caches them
        /// </summary>
        private async Task<Dictionary<string, WorkflowDefinition>> GetWorkflowDefinitionsAsync()
        {
            if (_memoryCache.TryGetValue(WorkflowDefsCacheKey, out object? cachedObj) && cachedObj is Dictionary<string, WorkflowDefinition> cachedDefs)
            {
                return cachedDefs;
            }

            var repository = _repositoryFactory.Create("DT_WORKFLOW");

            // Add a try-catch to handle missing columns gracefully
            string query;
            try
            {
                query = @"
                    SELECT
                        ROUTE,
                        VALUE,
                        FLOWNAME,
                        TABLENAME,
                        FIELDNAME,
                        RESPONSE_TYPE_NAME,
                        ENTITY_TYPE_NAME,
                        SERVICE_TYPE_NAME,
                        DIGIFLOW_ENTITY_NAME
                    FROM
                        DT_WORKFLOW.WF_XML_DEFINITION
                    WHERE
                        ROUTE IS NOT NULL";

                var workflowDefs = await repository.ExecuteQueryAsync<WorkflowDefinitionDto>(query);

                var result = new Dictionary<string, WorkflowDefinition>(StringComparer.OrdinalIgnoreCase);

                foreach (var def in workflowDefs)
                {
                    var route = def.Route?.ToLowerInvariant();
                    if (!string.IsNullOrEmpty(route) && !result.ContainsKey(route))
                    {
                        var definition = new WorkflowDefinition
                        {
                            Route = route,
                            DefinitionId = ParseDefinitionId(def.Value ?? ""),
                            FlowName = def.FlowName ?? "",
                            TableName = def.TableName ?? "",
                            FieldName = def.FieldName ?? "",
                            ResponseTypeName = def.ResponseTypeName,
                            EntityTypeName = def.EntityTypeName,
                            ServiceTypeName = def.ServiceTypeName
                        };

                        // Try to resolve the types from the database information if available
                        if (!string.IsNullOrEmpty(definition.ResponseTypeName) && !string.IsNullOrEmpty(definition.EntityTypeName))
                        {
                            try
                            {
                                // If the database contains valid type names, try to resolve them via reflection
                                definition.ResponseType = Type.GetType(definition.ResponseTypeName + ", " + definition.TypeAssembly) ??
                                                        ResolveTypeByName(definition.ResponseTypeName);

                                definition.EntityType = Type.GetType(definition.EntityTypeName + ", " + definition.TypeAssembly) ??
                                                      ResolveTypeByName(definition.EntityTypeName);

                                // Resolve service type if specified
                                if (!string.IsNullOrEmpty(definition.ServiceTypeName))
                                {
                                    definition.ServiceType = Type.GetType(definition.ServiceTypeName + ", " + definition.TypeAssembly) ??
                                                           ResolveTypeByName(definition.ServiceTypeName);
                                }

                                // If we successfully resolved the types, register them in the static dictionaries
                                if (definition.ResponseType != null && definition.EntityType != null)
                                {
                                    RegisterWorkflowType(route, definition.ResponseType, definition.EntityType, definition.ServiceType);
                                }
                            }
                            catch (Exception ex)
                            {
                                // Log the exception but continue processing other workflow definitions
                                Console.WriteLine($"Error resolving types for workflow '{route}': {ex.Message}");
                            }
                        }

                        result[route] = definition;
                    }
                }

                // Cache the results
                _memoryCache.Set(WorkflowDefsCacheKey, result, CacheDuration);

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting workflow definitions: {ex.Message}");

                // If we encounter an error (possibly due to missing columns), try with just the columns we know exist
                query = @"
                    SELECT
                        ROUTE,
                        VALUE,
                        FLOWNAME,
                        TABLENAME,
                        FIELDNAME
                    FROM
                        DT_WORKFLOW.WF_XML_DEFINITION
                    WHERE
                        ROUTE IS NOT NULL";

                var workflowDefs = await repository.ExecuteQueryAsync<WorkflowDefinitionDto>(query);

                var result = new Dictionary<string, WorkflowDefinition>(StringComparer.OrdinalIgnoreCase);

                foreach (var def in workflowDefs)
                {
                    var route = def.Route?.ToLowerInvariant();
                    if (!string.IsNullOrEmpty(route) && !result.ContainsKey(route))
                    {
                        var definition = new WorkflowDefinition
                        {
                            Route = route,
                            DefinitionId = ParseDefinitionId(def.Value ?? ""),
                            FlowName = def.FlowName ?? "",
                            TableName = def.TableName ?? "",
                            FieldName = def.FieldName ?? ""
                        };

                        // Try to get types from static mappings
                        if (_workflowTypeMapping.TryGetValue(route, out var responseType))
                        {
                            definition.ResponseType = responseType;
                        }

                        if (_workflowEntityMapping.TryGetValue(route, out var entityType))
                        {
                            definition.EntityType = entityType;
                        }

                        if (_workflowServiceMapping.TryGetValue(route, out var serviceType))
                        {
                            definition.ServiceType = serviceType;
                        }

                        result[route] = definition;
                    }
                }

                // Cache the results
                _memoryCache.Set(WorkflowDefsCacheKey, result, CacheDuration);

                return result;
            }
        }

        private long ParseDefinitionId(string value)
        {
            if (long.TryParse(value, out long definitionId))
            {
                return definitionId;
            }
            return -1;
        }

        /// <summary>
        /// Gets the workflow DTO type for the specified workflow name
        /// </summary>
        public async Task<Type> GetWorkflowTypeAsync(string workflowName)
        {
            workflowName = workflowName.ToLowerInvariant();

            var workflowDefs = await GetWorkflowDefinitionsAsync();

            if (workflowDefs.TryGetValue(workflowName, out var definition))
            {
                // First try to get the type from the database-loaded definition
                if (definition.ResponseType != null)
                {
                    return definition.ResponseType;
                }

                // Fall back to the static mapping if no type is resolved from the database
                if (_workflowTypeMapping.TryGetValue(workflowName, out var type))
                {
                    return type;
                }
            }

            throw new KeyNotFoundException($"No workflow type found for name: {workflowName}");
        }

        /// <summary>
        /// Gets the workflow type for a specified workflow name synchronously (for backwards compatibility)
        /// </summary>
        public Type GetWorkflowType(string workflowName)
        {
            workflowName = workflowName.ToLowerInvariant();

            try
            {
                // Try to get from cached workflow definitions first
                var definitions = GetWorkflowDefinitionsAsync().GetAwaiter().GetResult();
                if (definitions.TryGetValue(workflowName, out var definition) && definition.ResponseType != null)
                {
                    return definition.ResponseType;
                }
            }
            catch
            {
                // Fall back to static mappings if there's an error accessing the database
            }

            // Fall back to static mappings
            if (_workflowTypeMapping.TryGetValue(workflowName, out var type))
            {
                return type;
            }

            throw new KeyNotFoundException($"No workflow type found for name: {workflowName}");
        }

        /// <summary>
        /// Gets the workflow entity type for the specified workflow name
        /// </summary>
        public async Task<Type> GetWorkflowEntityAsync(string workflowName)
        {
            workflowName = workflowName.ToLowerInvariant();

            var workflowDefs = await GetWorkflowDefinitionsAsync();

            if (workflowDefs.TryGetValue(workflowName, out var definition))
            {
                // First try to get the type from the database-loaded definition
                if (definition.EntityType != null)
                {
                    return definition.EntityType;
                }

                // Fall back to the static mapping if no type is resolved from the database
                if (_workflowEntityMapping.TryGetValue(workflowName, out var type))
                {
                    return type;
                }
            }

            throw new KeyNotFoundException($"No workflow entity found for name: {workflowName}");
        }

        /// <summary>
        /// Gets the workflow entity for a specified workflow name synchronously (for backwards compatibility)
        /// </summary>
        public Type GetWorkflowEntity(string workflowName)
        {
            if (_workflowEntityMapping.TryGetValue(workflowName, out var entityType))
            {
                return entityType;
            }

            // Optionally, handle the case where the workflow name is not found.
            // For example, throw an exception or return a default type.
            throw new KeyNotFoundException($"Workflow entity for '{workflowName}' not found.");
        }

        /// <summary>
        /// Gets the workflow service type for the specified workflow name
        /// </summary>
        public async Task<Type> GetWorkflowServiceAsync(string workflowName)
        {
            workflowName = workflowName.ToLowerInvariant();

            var workflowDefs = await GetWorkflowDefinitionsAsync();

            if (workflowDefs.TryGetValue(workflowName, out var definition))
            {
                // First try to get the type from the database-loaded definition
                if (definition.ServiceType != null)
                {
                    return definition.ServiceType;
                }

                // Fall back to the static mapping if no type is resolved from the database
                if (_workflowServiceMapping.TryGetValue(workflowName, out var type))
                {
                    return type;
                }
            }

            throw new KeyNotFoundException($"No workflow service found for name: {workflowName}");
        }

        /// <summary>
        /// Gets the workflow service for a specified workflow name synchronously (for backwards compatibility)
        /// </summary>
        public Type GetWorkflowService(string workflowName)
        {
            workflowName = workflowName.ToLowerInvariant();

            try
            {
                // Try to get from cached workflow definitions first
                var definitions = GetWorkflowDefinitionsAsync().GetAwaiter().GetResult();
                if (definitions.TryGetValue(workflowName, out var definition) && definition.ServiceType != null)
                {
                    return definition.ServiceType;
                }
            }
            catch
            {
                // Fall back to static mappings if there's an error accessing the database
            }

            // Fall back to static mappings
            if (_workflowServiceMapping.TryGetValue(workflowName, out var type))
            {
                return type;
            }

            throw new KeyNotFoundException($"No workflow service found for name: {workflowName}");
        }

        /// <summary>
        /// Gets the workflow definition ID for the specified workflow name from the database
        /// </summary>
        public async Task<long> GetWorkflowDefinitionIdAsync(string workflowName)
        {
            workflowName = workflowName.ToLowerInvariant();

            var workflowDefs = await GetWorkflowDefinitionsAsync();

            if (workflowDefs.TryGetValue(workflowName, out var definition))
            {
                return definition.DefinitionId;
            }

            return -1;
        }

        /// <summary>
        /// Gets the workflow definition ID synchronously (for backwards compatibility)
        /// </summary>
        public long GetWorkflowDefinitionId(string workflowName)
        {
            // This is a fallback for compatibility purposes only
            // In a real scenario, we'd want to make this async too
            try
            {
                return GetWorkflowDefinitionsAsync().GetAwaiter().GetResult()
                    .TryGetValue(workflowName.ToLowerInvariant(), out var definition)
                        ? definition.DefinitionId
                        : -1;
            }
            catch
            {
                return -1;
            }
        }

        /// <summary>
        /// Gets all workflow definitions from the database
        /// </summary>
        public async Task<IEnumerable<WorkflowDefinition>> GetAllWorkflowDefinitionsAsync()
        {
            var defs = await GetWorkflowDefinitionsAsync();
            return defs.Values;
        }

        /// <summary>
        /// Register a new workflow type mapping in memory
        /// </summary>
        public static void RegisterWorkflowType(string route, Type responseType, Type entityType, Type? serviceType = null)
        {
            if (string.IsNullOrEmpty(route))
                throw new ArgumentException("Route cannot be null or empty", nameof(route));

            if (responseType == null)
                throw new ArgumentNullException(nameof(responseType));

            if (entityType == null)
                throw new ArgumentNullException(nameof(entityType));

            route = route.ToLowerInvariant();

            _workflowTypeMapping[route] = responseType;
            _workflowEntityMapping[route] = entityType;

            if (serviceType != null)
            {
                _workflowServiceMapping[route] = serviceType;
            }
        }

        /// <summary>
        /// Updates the workflow type information in the database
        /// </summary>
        public async Task UpdateWorkflowTypeInfoInDatabaseAsync(string route, Type responseType, Type entityType)
        {
            if (string.IsNullOrEmpty(route))
                throw new ArgumentException("Route cannot be null or empty", nameof(route));

            if (responseType == null)
                throw new ArgumentNullException(nameof(responseType));

            if (entityType == null)
                throw new ArgumentNullException(nameof(entityType));

            route = route.ToLowerInvariant();

            // Register the types in memory first
            RegisterWorkflowType(route, responseType, entityType);

            try
            {
                // Then update the database
                var repository = _repositoryFactory.Create("DT_WORKFLOW");
                const string updateQuery = @"
                    UPDATE DT_WORKFLOW.WF_XML_DEFINITION
                    SET
                        RESPONSE_TYPE_NAME = :responseTypeName,
                        ENTITY_TYPE_NAME = :entityTypeName
                    WHERE
                        ROUTE = :route";

                var parameters = new Dictionary<string, object>
                {
                    { "responseTypeName", responseType.FullName ?? responseType.Name },
                    { "entityTypeName", entityType.FullName ?? entityType.Name },
                    { "route", route }
                };

                await repository.ExecuteUpdateAsync(updateQuery, parameters);

                // Invalidate the cache to force a refresh on next access
                _memoryCache.Remove(WorkflowDefsCacheKey);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating workflow type info: {ex.Message}");
                // Continue execution instead of throwing - this might be due to missing columns
            }
        }

        /// <summary>
        /// Resolves a type by its name by searching through all loaded assemblies
        /// </summary>
        private Type? ResolveTypeByName(string? typeName)
        {
            if (string.IsNullOrEmpty(typeName))
                return null;

            // First try to resolve from the already registered types
            foreach (var mapping in _workflowTypeMapping)
            {
                var type = mapping.Value;
                if (type.FullName == typeName || type.Name == typeName)
                    return type;
            }

            foreach (var mapping in _workflowEntityMapping)
            {
                var type = mapping.Value;
                if (type.FullName == typeName || type.Name == typeName)
                    return type;
            }

            // Then try to find it in all loaded assemblies
            var assemblies = AppDomain.CurrentDomain.GetAssemblies();
            foreach (var assembly in assemblies)
            {
                try
                {
                    var type = assembly.GetType(typeName, false, true);
                    if (type != null)
                        return type;
                }
                catch
                {
                    // Ignore errors and continue searching
                }
            }

            // Try to find by the simple name (not fully qualified)
            string simpleName = typeName.Split('.').Last();
            foreach (var assembly in assemblies)
            {
                try
                {
                    foreach (var type in assembly.GetExportedTypes())
                    {
                        if (type.Name == simpleName)
                            return type;
                    }
                }
                catch
                {
                    // Ignore errors and continue searching
                }
            }

            return null;
        }        /// <summary>
                 /// Seeds the WorkflowTypeHelper with known service type mappings from previously hardcoded registrations.
                 /// This method should be called during application startup to initialize the static dictionaries.
                 /// </summary>
        public static void SeedKnownWorkflowTypes()
        {
            try
            {
                Console.WriteLine("🌱 Starting workflow type seeding...");
                _staticWorkflowDefinitions.Clear(); // Clear any previous static definitions

                // Define the known type mappings based on previously hardcoded registrations
                var knownMappings = new[]
                {
                    new { Route = "delegation", ResponseType = typeof(DelegationWorkflowResponseDto), EntityType = typeof(DelegationWorkflowEntity), ServiceType = "DelegationWorkflowService" },
                    new { Route = "bifikrimvar", ResponseType = typeof(BiFikrimVarWorkflowResponseDto), EntityType = typeof(BiFikrimVarEntity), ServiceType = "BiFikrimVarWorkflowService" },
                    new { Route = "contract", ResponseType = typeof(ContractWorkflowResponseDto), EntityType = typeof(ContractEntity), ServiceType = "ContractWorkflowService" },
                    new { Route = "jumptostate", ResponseType = typeof(JumpToStateWorkflowResponseDto), EntityType = typeof(JumpToStateEntity), ServiceType = "JumpToStateWorkflowService" },
                    new { Route = "monitoring", ResponseType = typeof(MonitoringRequestWorkflowResponseDto), EntityType = typeof(MonitoringRequestEntity), ServiceType = "MonitoringRequestWorkflowService" }
                };

                foreach (var mapping in knownMappings)
                {
                    try
                    {
                        // Try to find the service type from all loaded assemblies
                        Type? serviceType = null;
                        var assemblies = AppDomain.CurrentDomain.GetAssemblies();

                        foreach (var assembly in assemblies)
                        {
                            try
                            {
                                serviceType = assembly.GetTypes()
                                    .FirstOrDefault(t => t.Name == mapping.ServiceType && !t.IsAbstract && !t.IsInterface);

                                if (serviceType != null)
                                    break;
                            }
                            catch
                            {
                                // Continue searching in other assemblies
                            }
                        }

                        if (mapping.ResponseType != null && mapping.EntityType != null)
                        {
                            RegisterWorkflowType(mapping.Route, mapping.ResponseType, mapping.EntityType, serviceType); // This updates the dictionaries

                            // Add to the static list for early registration
                            _staticWorkflowDefinitions.Add(new WorkflowDefinition
                            {
                                Route = mapping.Route.ToLowerInvariant(),
                                ResponseType = mapping.ResponseType,
                                EntityType = mapping.EntityType,
                                ServiceType = serviceType,
                                ServiceTypeName = serviceType == null ? mapping.ServiceType : null // Store name if type couldn't be resolved yet
                            });

                            Console.WriteLine($"✅ Seeded workflow type: {mapping.Route} -> Response: {mapping.ResponseType.Name}, Entity: {mapping.EntityType.Name}, Service: {serviceType?.Name ?? mapping.ServiceType ?? "null"}");
                        }
                        else
                        {
                            Console.WriteLine($"⚠️ Could not find all types for workflow '{mapping.Route}': Response={mapping.ResponseType?.Name ?? "null"}, Entity={mapping.EntityType?.Name ?? "null"}, Service={serviceType?.Name ?? "null"}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"❌ Error seeding workflow type '{mapping.Route}': {ex.Message}");
                    }
                }

                Console.WriteLine($"🌱 Workflow type seeding completed for {knownMappings.Length} known mappings");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to seed known workflow types: {ex.Message}");
                // Don't throw - this is an initialization method that shouldn't crash the app
            }
        }

        /// <summary>
        /// Gets the statically seeded workflow definitions.
        /// Used for early service registration before the database might be available.
        /// </summary>
        public static IEnumerable<WorkflowDefinition> GetStaticWorkflowDefinitions()
        {
            return _staticWorkflowDefinitions;
        }

        // ...existing code...
    }

    /// <summary>
    /// Represents a workflow definition from the database
    /// </summary>
    public class WorkflowDefinition
    {
        public string Route { get; set; } = string.Empty;
        public long DefinitionId { get; set; }
        public string FlowName { get; set; } = string.Empty;
        public string TableName { get; set; } = string.Empty;
        public string FieldName { get; set; } = string.Empty;
        public string? ResponseTypeName { get; set; }
        public string? EntityTypeName { get; set; }
        public string? ServiceTypeName { get; set; }
        public string TypeAssembly { get; set; } = string.Empty;
        public Type? ResponseType { get; set; }
        public Type? EntityType { get; set; }
        public Type? ServiceType { get; set; }
    }
}
