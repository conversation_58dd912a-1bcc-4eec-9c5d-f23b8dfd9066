﻿using DigiflowAPI.Application.Interfaces.DataAccess;
using Microsoft.AspNetCore.Http;
using Microsoft.Win32.SafeHandles;
using System.Collections;
using System.Security.Principal;
using System.DirectoryServices;
using DigiflowAPI.Domain.Entities.Framework;

namespace DigiflowAPI.Infrastructure.Security.Authentication.Services
{
    public class DomainAuthService(IOracleDataAccessRepositoryFactory repositoryFactory, IHttpContextAccessor httpContextAccessor)
    {
        #region Kullanılan Fonksiyonlar
        public async Task<FLogin> GetLogin(long LoginId)
        {
            string sql = "SELECT * FROM FRAMEWORK.F_LOGIN WHERE FRAMEWORK.F_LOGIN.LOGIN_ID = :LoginId";
            var parameters = new Dictionary<string, object>
            {
                {"LoginId", LoginId}
            };

            try
            {
                var repository = repositoryFactory.Create("FrameworkConnection");
                var loginObject = await repository.ExecuteScalarAsync<FLogin>(sql, parameters);
                return loginObject;
            }
            catch (Exception ex)
            {
                string exMessage = "Failed to retrieve login details: " + ex.Message;
                if (ex.InnerException != null)
                {
                    exMessage += " | Inner Exception: " + ex.InnerException.Message;
                }
                throw new ApplicationException(exMessage, ex);
            }
        }

        /// <summary>
        ///  Helper Function Get Current User Login Information
        /// </summary>
        /// <param name="debugMode"> Application Mode </param>
        /// <param name="LoginId">  User Login Id </param>
        /// <returns></returns>
        public async Task<FLogin> GetCurrentLoginAsync(bool debugMode, long LoginId, string strlogin)
        {
            // SID ile login denetimi
            FLogin login = null;
            if (debugMode && LoginId != 0)
            {
                login = await GetLogin(LoginId);
            }
            else if ((login == null || LoginId == 0) && (strlogin.IndexOf("DIGIFLOW_SA") > -1 || strlogin.IndexOf("SPSMOSS_SA") > -1))
            {
                login = await GetLogin(123456789);
            }
            else if (login == null || LoginId == 0)
            {
                login = await GetLoginBySIDAsync(strlogin);
            }
            if (login != null)
            {
                return login;
            }
            return null;
        }

        /// <summary>
        /// SID bilgisini çeker
        /// </summary>
        /// <returns></returns>
        public async Task<FLogin> GetLoginBySIDAsync(string strLogin)
        {
            string str = string.Empty;

            int idx = strLogin.IndexOf('\\');
            if (idx == -1)
            {
                idx = strLogin.IndexOf('@');
            }
            string strDomain;
            string strName;
            if (idx != -1)
            {
                strDomain = strLogin.Substring(0, idx);
                strName = strLogin.Substring(idx + 1);
            }
            else
            {
                strDomain = Environment.MachineName;
                strName = strLogin;
            }
            FLogin LoginObj = await GetFLoginAsync(strName);
            string SID = SecurityIdentifierProperties().Value.ToString();
            if (System.Configuration.ConfigurationManager.AppSettings["IsSIDControls"] == "True")
            {
                if (SID == LoginObj.DomainUserSID)
                {
                    return LoginObj;
                }
                else
                {
                    //return null;
                    throw new UnauthorizedAccessException($"Authentication failed for SID: {SID}");
                }
            }
            else
            {
                return LoginObj;
            }
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="strLogin"></param>
        /// <returns></returns>
        public static string GetSid(string strLogin)
        {
            string str = string.Empty;
            //Parse the string to check if domain name is present.
            int idx = strLogin.IndexOf('\\');
            if (idx == -1)
            {
                idx = strLogin.IndexOf('@');
            }
            string strDomain;
            string strName;
            if (idx != -1)
            {
                strDomain = strLogin.Substring(0, idx);
                strName = strLogin.Substring(idx + 1);
            }
            else
            {
                strDomain = Environment.MachineName;
                strName = strLogin;
            }
            try
            {
                DirectoryEntry usr = new DirectoryEntry();
                usr = GetDirectoryEntry("domainuser", strName, strDomain, "LOCAL");
                string sid = new SecurityIdentifier((byte[])usr.Properties["objectSid"][0], 0).Value;
                str = sid;
                usr.Dispose();
            }
            catch (Exception ex)
            {
                throw new UnauthorizedAccessException($"Authentication failed for user: {strLogin}", ex);
            }
            return str;
        }

        /// <summary>
        /// DirectoryEntry Bilgisini Çeker
        /// </summary>
        /// <param name="objectClass"></param>
        /// <param name="objectName"></param>
        /// <param name="DC1"></param>
        /// <param name="DC2"></param>
        /// <returns></returns>
        public static DirectoryEntry GetDirectoryEntry(string objectClass, string objectName, String DC1, String DC2)
        {
            DirectoryEntry entry = new("LDAP://DC=" + DC1 + ", DC=" + DC2);
            DirectorySearcher searcher = new DirectorySearcher(entry);
            switch (objectClass)
            {
                case "user":
                    searcher.Filter = "(&(objectClass=user)(|(cn=" + objectName + ")(displayName=" + objectName + ")(sAMAccountName=" + objectName + ")))";
                    break;

                case "group":
                    searcher.Filter = "(&(objectClass=group)(|(cn=" + objectName + ")(dn=" + objectName + ")))";
                    break;

                case "computer":
                    searcher.Filter = "(&(objectClass=computer)(|(cn=" + objectName + ")))";
                    break;

                case "domainuser":
                    searcher.Filter = "(&(objectClass=user)(|(sAMAccountName=" + objectName + ")))";
                    break;
            }
            SearchResult result = searcher.FindOne();
            if (result != null)
            {
                DirectoryEntry directoryObject = result.GetDirectoryEntry();
                entry.Close();
                entry.Dispose();
                searcher.Dispose();
                return directoryObject;
            }
            else
                throw new NullReferenceException(objectClass + " şemasında belirtilen kayıt bulunamadı");
        }

        /// <summary>
        /// FLogin Bilgisini Döndüdür
        /// </summary>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public async Task<FLogin> GetFLoginAsync(string UserName)
        {
            string strDomain;
            string strName;
            int idx = UserName.IndexOf('\\');
            if (idx != -1)
            {
                strDomain = UserName.Substring(0, idx);
                strName = UserName.Substring(idx + 1);
            }
            else
            {
                strDomain = Environment.MachineName;
                strName = UserName;
            }
            string User = strName.ToUpper();
            User = User.Replace("İ", "I");
            User = User.Replace("Ö", "O");
            User = User.Replace("Ü", "U");
            string SQL = "Select * from FRAMEWORK.F_LOGIN where FRAMEWORK.F_LOGIN.DOMAIN_USER_NAME='" + User + "'";

            var repository = repositoryFactory.Create("FrameworkConnection");
            FLogin LoginObject = await repository.ExecuteScalarAsync<FLogin>(SQL);
            return LoginObject;
        }

        #endregion Kullanılan Fonksiyonlar

        #region ActiveDirectoryGroup Kontrol

        /// <summary>
        /// Bu Fonksiyon Active Directory deki Login Grupların içerisindeki kullanıcıları kontrol etmek için kullanılr
        /// </summary>
        /// <param name="LoginId"> Kontrol edilecek kullanıcının loginId si</param>
        /// <param name="LoginGroup">Kontrol edilecek Grubun adı</param>
        /// <returns></returns>
        public async Task<bool> CheckActiveDirectoryGroupAsync(long LoginId, string LoginGroup)
        {
            string SQL = "Select * from FRAMEWORK.F_LOGIN where FRAMEWORK.F_LOGIN.LOGIN_ID=" + LoginId + "";

            var repository = repositoryFactory.Create("FrameworkConnection");
            FLogin Login = await repository.ExecuteScalarAsync<FLogin>(SQL);
            return CheckActiveDirectoryGroup(Login.DomainUserName, Login.DomainName, LoginGroup);
        }

        /// <summary>
        /// Bu Fonksiyon Active Directory deki Login Grupların içerisindeki kullanıcıları kontrol etmek için kullanılr
        /// </summary>
        /// <param name="UserName"> Kontrol Edilecek Kullanıcının Adı</param>
        /// <param name="LoginGroup"> Kontrol Edilecek LogicalGroup </param>
        /// <returns></returns>
        public bool CheckActiveDirectoryGroup(string UserName, string DomainName, string LoginGroup)
        {
            var httpContext = httpContextAccessor.HttpContext;
            var user = httpContext.User;
            bool result = false;
            if (user.Identity.IsAuthenticated && user.IsInRole("ImpersonationRole"))
            {
                using (WindowsIdentity newIdentity = new(UserName))
                {
                    // Get the SafeAccessTokenHandle from the WindowsIdentity object
                    SafeAccessTokenHandle safeTokenHandle = newIdentity.AccessToken;

                    // Impersonate the user using the SafeAccessTokenHandle
#pragma warning disable CA1416 // Validate platform compatibility
                    WindowsIdentity.RunImpersonated(safeTokenHandle, () =>
                    {
                        ArrayList uyelistesi = new ArrayList();
                        DirectoryEntry giris = new DirectoryEntry();
                        try
                        {
                            giris = GetDirectoryEntry("user", UserName, DomainName, "LOCAL"); //uyeleri getirilir
                            object members = giris.Invoke("Groups", null);
                            //uyeleri listeye eklenir
                            foreach (object member in (IEnumerable)members)
                            {
                                using (DirectoryEntry x = new DirectoryEntry(member))
                                {
                                    if (x.Properties["SamAccountName"].Value != null)
                                    {
                                        if (x.Properties["SamAccountName"].Value.ToString() == LoginGroup)
                                        {
                                            result = true;
                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception) { }
                    });
#pragma warning restore CA1416 // Validate platform compatibility
                }
            }
            return result;
        }

        /// <summary>
        /// KUllanıcının dal grubunda olup olmadığını kontrol eder
        /// </summary>
        /// <param name="UserName"></param>
        /// <param name="LoginGroupDal"></param>
        /// <returns></returns>
        public static bool CheckDalActiveDirectory(string UserName, string LoginGroupDal)
        {
            try
            {
                DirectoryEntry sonuc = GetDirectoryEntryPrivate(LoginGroupDal, UserName, "DIGITURK", "LOCAL");
            }
            catch (Exception)
            {
                return false;
            }
            return true;
        }

        /// <summary>
        /// Aktif Directory kullanıcının Attribuite ini kontrol eder
        /// </summary>
        /// <param name="objectClass">LoginGrupAdı</param>
        /// <param name="objectName">Kullanıcı Adı</param>
        /// <param name="DC1"> Domain Parameteresi</param>
        /// <param name="DC2"> Domain Etki alanı parametresi</param>
        /// <returns></returns>
        public static DirectoryEntry GetDirectoryEntryPrivate(string objectClass, string objectName, String DC1, String DC2)
        {
#pragma warning disable CA1416 // Validate platform compatibility
            DirectoryEntry response = new DirectoryEntry();
            WindowsIdentity.RunImpersonated(WindowsIdentity.GetCurrent().AccessToken, () =>
            {
                DirectoryEntry entry = new DirectoryEntry("LDAP://DC=" + DC1 + ", DC=" + DC2);
                DirectorySearcher searcher = new DirectorySearcher(entry);
                switch (objectClass)
                {
                    case "user":
                        searcher.Filter = "(&(objectClass=user)(|(cn=" + objectName + ")(displayName=" + objectName + ")(sAMAccountName=" + objectName + ")))";
                        break;

                    case "group":
                        searcher.Filter = "(&(objectClass=group)(|(cn=" + objectName + ")(dn=" + objectName + ")))";
                        break;

                    case "computer":
                        searcher.Filter = "(&(objectClass=computer)(|(cn=" + objectName + ")))";
                        break;

                    case "domainuser":
                        searcher.Filter = "(&(objectClass=user)(|(sAMAccountName=" + objectName + ")))";
                        break;

                    case "group2":
                        searcher.Filter = "(&(objectClass=group)(|(cn=" + objectName + ")(dn=" + objectName + ")))";
                        break;

                    case "Stajyer Users":
                        searcher.Filter = "(&(objectClass=user)(&(department=Stajyer Users)(sAMAccountName=" + objectName + ")))";
                        break;
                }

                SearchResult result = null;
                result = searcher.FindOne();
                if (result != null)
                {
                    DirectoryEntry directoryObject = result.GetDirectoryEntry();
                    entry.Close();
                    entry.Dispose();
                    searcher.Dispose();
                    response = directoryObject;
                }
                else
                    throw new NullReferenceException(objectClass + " şemasında belirtilen bilgilerde kayıt bulunamadı");
            });
#pragma warning restore CA1416 // Validate platform compatibility
            return response;
        }

        /// <summary>
        /// SID geri alma işlemi
        /// </summary>
        public SecurityIdentifier SecurityIdentifierProperties()
        {
            WindowsIdentity identity = null;
            if (httpContextAccessor.HttpContext == null)
            {
                identity = WindowsIdentity.GetCurrent();
            }
            else
            {
                identity = httpContextAccessor.HttpContext.User.Identity as WindowsIdentity;
            }
            return identity.User;
        }

        #endregion ActiveDirectoryGroup Kontrol
    }
}
