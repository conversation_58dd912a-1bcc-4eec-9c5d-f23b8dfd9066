﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authentication.Negotiate;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Security.Principal;
using System.Text.Encodings.Web;

namespace DigiflowAPI.Infrastructure.Security.Authentication
{
    public class SmartAuthHandler(
        IOptionsMonitor<AuthenticationSchemeOptions> options,
        ILoggerFactory logger,
        UrlEncoder encoder,
        ISystemClock clock) : AuthenticationHandler<AuthenticationSchemeOptions>(options, logger, encoder, clock)
    {
        private readonly ILogger<SmartAuthHandler> _logger = logger.CreateLogger<SmartAuthHandler>();        protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            _logger.LogInformation("SmartAuthHandler HandleAuthenticateAsync called");

            // Get the authentication service
            var authService = Context.RequestServices.GetRequiredService<IAuthenticationService>();

            // First, check if Windows authentication has already authenticated the user
            var windowsAuthResult = await authService.AuthenticateAsync(Context, NegotiateDefaults.AuthenticationScheme);
            if (windowsAuthResult.Succeeded)
            {
                _logger.LogInformation("Windows authentication succeeded for user: {User}",
                    windowsAuthResult.Principal?.Identity?.Name);
                return AuthenticateResult.Success(windowsAuthResult.Ticket);
            }

            // Check if JWT token is present
            string authorization = Context.Request.Headers["Authorization"];
            if (!string.IsNullOrEmpty(authorization) && authorization.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogInformation("JWT Bearer token detected, forwarding to JWT handler");
                var jwtAuthResult = await authService.AuthenticateAsync(Context, "Bearer");
                if (jwtAuthResult.Succeeded)
                {
                    _logger.LogInformation("JWT authentication succeeded");
                    return AuthenticateResult.Success(jwtAuthResult.Ticket);
                }
            }

            // Check for mobile/WebView requests
            if (Context.Request.Headers.ContainsKey("X-From-Mobile-WebView") ||
                Context.Request.Headers.ContainsKey("X-Mobile-App") ||
                Context.Request.Headers.ContainsKey("X-Is-Mobile"))
            {
                _logger.LogInformation("Mobile/WebView request detected, forwarding to JWT handler");
                var mobileJwtResult = await authService.AuthenticateAsync(Context, "Bearer");
                if (mobileJwtResult.Succeeded)
                {
                    return AuthenticateResult.Success(mobileJwtResult.Ticket);
                }
            }

            // Check if JWT auth was already processed by our middleware
            if (Context.Items.ContainsKey("AuthType") &&
                Context.Items["AuthType"]?.ToString() == "JWT" &&
                Context.User?.Identity?.IsAuthenticated == true)
            {
                _logger.LogInformation("Using JWT authentication for user: {Username}",
                    Context.User.Identity.Name);

                // Fall back to the user principal set by the middleware
                return AuthenticateResult.Success(
                    new AuthenticationTicket(Context.User, "Smart"));
            }

            // If no authentication succeeded and this is a browser request,
            // we should trigger Windows auth challenge
            if (IsBrowserRequest(Context) && !IsApiRequest(Context) && !IsMobileOrReactClient(Context))
            {
                _logger.LogInformation("No authentication found for browser request, will trigger challenge");
                return AuthenticateResult.Fail("No authentication provided");
            }

            // If Windows auth failed but we have a Windows identity in the context
            // This can happen with anonymous auth enabled
            if (Context.User?.Identity is WindowsIdentity windowsIdentity &&
                !string.IsNullOrEmpty(windowsIdentity.Name))
            {
                _logger.LogInformation("Found Windows identity in context: {User}", windowsIdentity.Name);
                return AuthenticateResult.Success(
                    new AuthenticationTicket(Context.User, Scheme.Name));
            }

            _logger.LogInformation("No authentication method succeeded");
            return AuthenticateResult.NoResult();
        }protected override async Task HandleChallengeAsync(AuthenticationProperties properties)
        {
            _logger.LogInformation("SmartAuthHandler HandleChallengeAsync called");

            // Check if this is a browser request that should use Windows auth
            if (IsBrowserRequest(Context) && !IsApiRequest(Context) && !IsMobileOrReactClient(Context))
            {
                _logger.LogInformation("Browser request detected, triggering Windows authentication challenge");

                // Clear any existing authentication to force re-authentication
                Context.Response.Headers.Remove("WWW-Authenticate");

                // Delegate to Windows authentication challenge
                var authService = Context.RequestServices.GetRequiredService<IAuthenticationService>();
                await authService.ChallengeAsync(Context, NegotiateDefaults.AuthenticationScheme, properties);
                return;
            }

            // For API endpoints or mobile/React clients, return 401
            Context.Response.StatusCode = 401;
            Context.Response.Headers["WWW-Authenticate"] = "Bearer";
            await Context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(new
            {
                message = "Authentication required",
                authType = "Bearer"
            }));
        }

        private bool IsBrowserRequest(HttpContext context)
        {
            var userAgent = context.Request.Headers["User-Agent"].ToString();

            // Check for common browser user agents
            return !string.IsNullOrEmpty(userAgent) &&
                   (userAgent.Contains("Mozilla") ||
                    userAgent.Contains("Chrome") ||
                    userAgent.Contains("Safari") ||
                    userAgent.Contains("Edge")) &&
                   !userAgent.Contains("Postman") &&
                   !userAgent.Contains("axios");
        }

        private bool IsApiRequest(HttpContext context)
        {
            var path = context.Request.Path.Value?.ToLower() ?? "";
            return path.Contains("/api/") ||
                   path.Contains("/swagger") ||
                   context.Request.Headers.ContainsKey("X-Requested-With");
        }

        private bool IsMobileOrReactClient(HttpContext context)
        {
            return context.Request.Headers.ContainsKey("X-Mobile-App") ||
                   context.Request.Headers.ContainsKey("X-Is-Mobile") ||
                   context.Request.Headers.ContainsKey("X-From-Mobile-WebView") ||
                   context.Request.Headers.ContainsKey("X-DigiflowReact") ||
                   context.Request.Headers.ContainsKey("X-React-App") ||
                   context.Items.ContainsKey("IsReactClient");
        }
    }
}