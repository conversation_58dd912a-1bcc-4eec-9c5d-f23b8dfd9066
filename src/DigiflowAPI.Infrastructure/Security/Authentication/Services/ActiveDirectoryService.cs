﻿using System;
using System.DirectoryServices.AccountManagement;
using System.DirectoryServices;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;
using System.Security.Cryptography;
using System.Text;

namespace DigiflowAPI.Infrastructure.Security.Authentication
{
    public interface IActiveDirectoryService
    {
        Task<bool> ValidateCredentials(string username, string password);
        Task<string> GetPasswordHashAsync(string username);
        Task<bool> HasPasswordChanged(string username, DateTime lastVerifiedDate);
    }

    public class ActiveDirectoryService : IActiveDirectoryService
    {
        private readonly ILogger<ActiveDirectoryService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _domain;
        private readonly string _ldapPath;

        public ActiveDirectoryService(ILogger<ActiveDirectoryService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _domain = _configuration["ActiveDirectory:Domain"] ?? "DIGITURK";
            _ldapPath = _configuration["Ldap:Path"] ?? "LDAP://dtldap.digiturk.local";
        }

        public async Task<bool> ValidateCredentials(string username, string password)
        {
            try
            {
                return await Task.Run(() =>
                {
                    try
                    {
                        string cleanUsername = RemoveDomainPrefix(username);
                        using (var entry = new DirectoryEntry(_ldapPath, $"{_domain}\\{cleanUsername}", password))
                        {
                            var nativeObject = entry.NativeObject;
                            return true;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to validate credentials for user: {Username}", username);
                        return false;
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating credentials for {Username}", username);
                return false;
            }
        }

        public async Task<string> GetPasswordHashAsync(string username)
        {
            try
            {
                string cleanUsername = RemoveDomainPrefix(username);

                // In a real implementation, you'd use a secure method to create a hash
                // This is a simplified version that creates a deterministic hash based on 
                // password-related attributes that change when the password changes

                return await Task.Run(() =>
                {
                    try
                    {
                        using (var context = new PrincipalContext(ContextType.Domain, _domain))
                        {
                            var user = UserPrincipal.FindByIdentity(context, IdentityType.SamAccountName, cleanUsername);
                            if (user == null)
                            {
                                _logger.LogWarning("User not found: {Username}", username);
                                return null;
                            }

                            // Get AD properties related to password
                            using (var directoryEntry = user.GetUnderlyingObject() as DirectoryEntry)
                            {
                                if (directoryEntry != null)
                                {
                                    var pwdLastSet = directoryEntry.Properties["pwdLastSet"].Value;
                                    var userAccountControl = directoryEntry.Properties["userAccountControl"].Value;

                                    // Create a stable "hash" that changes when password changes
                                    var hashInput = $"{cleanUsername}:{_domain}:{pwdLastSet}:{userAccountControl}";
                                    return CalculateMD5Hash(hashInput);
                                }
                            }
                            return null;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error getting password hash for {Username}", username);
                        return null;
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting password hash for {Username}", username);
                return null;
            }
        }

        public async Task<bool> HasPasswordChanged(string username, DateTime lastVerifiedDate)
        {
            try
            {
                string cleanUsername = RemoveDomainPrefix(username);

                return await Task.Run(() =>
                {
                    try
                    {
                        using (var context = new PrincipalContext(ContextType.Domain, _domain))
                        {
                            var user = UserPrincipal.FindByIdentity(context, IdentityType.SamAccountName, cleanUsername);
                            if (user == null)
                            {
                                _logger.LogWarning("User not found: {Username}", username);
                                return true; // Assume changed if user not found
                            }

                            // Get password last set date
                            using (var directoryEntry = user.GetUnderlyingObject() as DirectoryEntry)
                            {
                                if (directoryEntry != null && directoryEntry.Properties.Contains("pwdLastSet"))
                                {
                                    var pwdLastSet = directoryEntry.Properties["pwdLastSet"].Value;

                                    if (pwdLastSet != null)
                                    {
                                        // Convert AD's pwdLastSet format to DateTime
                                        DateTime pwdLastSetDate = DateTime.FromFileTime((long)pwdLastSet);

                                        // If password was set after our last verification, it has changed
                                        bool changed = pwdLastSetDate > lastVerifiedDate;
                                        if (changed)
                                        {
                                            _logger.LogInformation("Password has changed for user {Username}. Last set: {PwdLastSet}, Last verified: {LastVerified}",
                                                username, pwdLastSetDate, lastVerifiedDate);
                                        }
                                        return changed;
                                    }
                                }
                            }

                            return false; // No password change detected
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error checking password change for {Username}", username);
                        return false;
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking password change for {Username}", username);
                return false;
            }
        }

        private string RemoveDomainPrefix(string username)
        {
            if (string.IsNullOrEmpty(username))
                return username;

            int idx = username.IndexOf('\\');
            if (idx != -1)
            {
                return username.Substring(idx + 1);
            }

            idx = username.IndexOf('@');
            if (idx != -1)
            {
                return username.Substring(0, idx);
            }

            return username;
        }

        private string CalculateMD5Hash(string input)
        {
            using (var md5 = MD5.Create())
            {
                var inputBytes = Encoding.UTF8.GetBytes(input);
                var hashBytes = md5.ComputeHash(inputBytes);

                var sb = new StringBuilder();
                foreach (var b in hashBytes)
                {
                    sb.Append(b.ToString("x2"));
                }

                return sb.ToString();
            }
        }
    }
}