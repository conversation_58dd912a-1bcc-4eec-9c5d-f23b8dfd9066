﻿using DigiflowAPI.Application.DTOs;
using DigiflowAPI.Application.DTOs.Workflow.Authorization;
using DigiflowAPI.Application.Interfaces.DataAccess;
using Microsoft.AspNetCore.Http;

namespace DigiflowAPI.Infrastructure.Security.Authorization.Services.Workflow.Abstract
{
    public interface IWorkflowAuthorizationService
    {
        Task<WorkflowAuthorizationResponseDto> ProcessWorkflowRequest(HttpContext context, long wfDefId);
        Task<WorkflowAuthorizationInfo> GetAuthorizationInfo(long wfDefId, long instanceId, long loginId, long? stateDefId = null);
        Task<string> GetWorkflowStatusAsync(long workflowInstanceId);
        Task<WorkflowAuthorizationInfo> GetWorkflowAuthorizationInfoAsync(long workflowInstanceId,
            long workflowDefinitionId,
            long stateDefinitionId,
            long loginId,
            bool isFlowAdmin,
            bool isActionFlow,
            bool isSystemAdmin,
            bool formDelegation,
            long assignedUserId,
            long assignToId);
    }
}