﻿using DigiflowAPI.Application.DTOs.Workflow.Authorization;
using DigiflowAPI.Domain.Entities.Framework;
using DigiflowAPI.Application.Interfaces.DataAccess;
using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Application.Interfaces.Services.Workflow;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using DigiflowAPI.Application.DTOs;
using DigiflowAPI.Infrastructure.Security.Authorization.Services.Workflow.Abstract;
using System.Linq;
using Digiturk.Workflow.Digiflow.Authorization;

namespace DigiflowAPI.Infrastructure.Security.Authorization.Services.Workflow.Concrete
{
    public class WorkflowAuthorizationService(
        IConfiguration configuration,
        IOracleDataAccessRepositoryFactory repositoryFactory,
        ITabVisibilityService tabVisibilityService,
        IActionPermissionService actionPermissionService,
        ILogicalGroupService logicalGroupService,
        IUserService userService,
        IGlobalHelpers globalHelpers,
        IWorkflowHelperService workflowHelperService) : IWorkflowAuthorizationService
    {
        public async Task<WorkflowAuthorizationResponseDto> ProcessWorkflowRequest(HttpContext context, long wfDefId)
        {
            var loginId = await userService.GetUserInfo();
            var userInfo = await userService.GetByIdAsync(loginId);
            var isAdmin = globalHelpers.SystemAdmins().Contains(userInfo.Username);
            var isFlowAdmin = await workflowHelperService.IsWfAdmin(wfDefId);

            long instanceId = GetHeaderValue(context, "X-Workflow-Instance-Id");
            long copyInstanceId = GetHeaderValue(context, "X-Workflow-Copy-Instance-Id");
            var formDelegation = await workflowHelperService.FormDelegation(instanceId);

            var isSuspended = await workflowHelperService.FlowIsSuspended(instanceId);
            var stateDefId = await GetStateDefId(instanceId);
            var isAssignedUser = await workflowHelperService.DelegeAssignUser(instanceId, loginId);
            var assignToId = await workflowHelperService.AssignToId(instanceId, loginId);
            try
            {
                var authInfo = await GetWorkflowAuthorizationInfoAsync(instanceId, wfDefId, stateDefId, loginId, isFlowAdmin, await workflowHelperService.IsActionFlow(instanceId), isAdmin, formDelegation, isAssignedUser, assignToId);

                var authResponse = new WorkflowAuthorizationResponseDto
                {
                    ActionPermissions = authInfo.Permissions,
                    TabVisibility = new TabVisibility(),
                    IsWorkflowSuspended = isSuspended
                };

                if (instanceId > 0)
                {
                    //DelegeHasConditionAcceptCorrection will be implemented
                    var currentActionTaskInstance = await workflowHelperService.CurrentActionTaskInstance(instanceId);
                    var wfInstance = await workflowHelperService.CurrentWfIns();

                    if (currentActionTaskInstance == null)
                    {
                        await HandleCompletedWorkflow(authResponse, authInfo, instanceId, wfDefId, loginId, isAdmin, isFlowAdmin);
                    }
                    else
                    {
                        await HandleActiveWorkflow(authResponse, authInfo, wfInstance, instanceId, wfDefId, loginId, isAdmin, isFlowAdmin, currentActionTaskInstance);
                    }

                    if (authInfo.IsOwnDelegated)
                    {
                        authResponse.TabVisibility.SetAllTabsInvisible();
                        //authResponse.Message = "You can not operate on this flow because your authority is delegated";
                        authResponse.Message = "Yetkilerinizi delege ettiğiniz için bu akış üzerinde işlem yapamazsınız";
                    }

                }
                else
                {
                    authResponse.TabVisibility.NewRequestTabVisible = authInfo.Permissions.CanCreate;
                    authResponse.ActionPermissions = authInfo.Permissions;
                }
                return authResponse;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public bool ConditionalResult(bool ActionAuthority, bool AssignedAuthority)
        {
            bool result;
            if (ActionAuthority == true)
            {
                result = true;
            }
            else
            {
                result = AssignedAuthority;
            }
            return result;
        }

        public async Task<WorkflowAuthorizationInfo> GetWorkflowAuthorizationInfoAsync(
            long workflowInstanceId,
            long workflowDefinitionId,
            long stateDefinitionId,
            long loginId,
            bool isFlowAdmin,
            bool isActionFlow,
            bool isSystemAdmin,
            bool formDelegation,
            long assignedUserId,
            long assignToId)
        {
            var permissions = await AuthoInfoAsync(workflowDefinitionId, stateDefinitionId, loginId);
            if (formDelegation)
            {
                var newPermissions = await AuthoInfoAsync(workflowDefinitionId, stateDefinitionId, assignedUserId);
                permissions.CanCreate = ConditionalResult(permissions.CanCreate, newPermissions.CanCreate);
                permissions.CanApproval = ConditionalResult(permissions.CanApproval, newPermissions.CanApproval);
                permissions.CanReject = ConditionalResult(permissions.CanReject, newPermissions.CanReject);
                permissions.CanForward = ConditionalResult(permissions.CanForward, newPermissions.CanForward);
                permissions.CanSendtoCommend = ConditionalResult(permissions.CanSendtoCommend, newPermissions.CanSendtoCommend);
                permissions.CanSendRequestToComment = ConditionalResult(permissions.CanSendRequestToComment, newPermissions.CanSendRequestToComment);
                permissions.CanSuspend = ConditionalResult(permissions.CanSuspend, newPermissions.CanSuspend);
                permissions.CanResume = ConditionalResult(permissions.CanResume, newPermissions.CanResume);
                permissions.CanCancel = ConditionalResult(permissions.CanCancel, newPermissions.CanCancel);
                permissions.CanFinalize = ConditionalResult(permissions.CanFinalize, newPermissions.CanFinalize);
                permissions.CanSendTask = ConditionalResult(permissions.CanSendTask, newPermissions.CanSendTask);
                permissions.CanConditionalAccept = ConditionalResult(permissions.CanConditionalAccept, newPermissions.CanConditionalAccept);
                permissions.CanCorrection = ConditionalResult(permissions.CanCorrection, newPermissions.CanCorrection);
                permissions.CanAddToComment = ConditionalResult(permissions.CanAddToComment, newPermissions.CanAddToComment);
                permissions.CanRollBack = ConditionalResult(permissions.CanRollBack, newPermissions.CanRollBack);
                permissions.CanSendBack = ConditionalResult(permissions.CanSendBack, newPermissions.CanSendBack);
                permissions.CanFileUpload = ConditionalResult(permissions.CanFileUpload, newPermissions.CanFileUpload);
                if (permissions.CanSendTask)
                {
                    permissions.SendTaskLogicalGroupId = newPermissions.SendTaskLogicalGroupId;
                }
                if (permissions.CanForward)
                {
                    permissions.ForwardLogicalGroupId = newPermissions.ForwardLogicalGroupId;
                }
                if (permissions.CanSendtoCommend)
                {
                    permissions.SendCommentLogicalGroupId = newPermissions.SendCommentLogicalGroupId;
                }
            }

            if (isFlowAdmin || isSystemAdmin)
            {
                var newPermissions = await AuthoInfoAsync(workflowDefinitionId, stateDefinitionId, assignToId);
                permissions.CanCreate = ConditionalResult(permissions.CanCreate, newPermissions.CanCreate);
                permissions.CanApproval = ConditionalResult(permissions.CanApproval, newPermissions.CanApproval);
                permissions.CanReject = ConditionalResult(permissions.CanReject, newPermissions.CanReject);
                permissions.CanForward = ConditionalResult(permissions.CanForward, newPermissions.CanForward);
                permissions.CanSendtoCommend = ConditionalResult(permissions.CanSendtoCommend, newPermissions.CanSendtoCommend);
                permissions.CanSendRequestToComment = ConditionalResult(permissions.CanSendRequestToComment, newPermissions.CanSendRequestToComment);
                permissions.CanSuspend = ConditionalResult(permissions.CanSuspend, newPermissions.CanSuspend);
                permissions.CanResume = ConditionalResult(permissions.CanResume, newPermissions.CanResume);
                permissions.CanCancel = ConditionalResult(permissions.CanCancel, newPermissions.CanCancel);
                permissions.CanFinalize = ConditionalResult(permissions.CanFinalize, newPermissions.CanFinalize);
                permissions.CanSendTask = ConditionalResult(permissions.CanSendTask, newPermissions.CanSendTask);
                permissions.CanConditionalAccept = ConditionalResult(permissions.CanConditionalAccept, newPermissions.CanConditionalAccept);
                permissions.CanCorrection = ConditionalResult(permissions.CanCorrection, newPermissions.CanCorrection);
                permissions.CanAddToComment = ConditionalResult(permissions.CanAddToComment, newPermissions.CanAddToComment);
                permissions.CanRollBack = ConditionalResult(permissions.CanRollBack, newPermissions.CanRollBack);
                permissions.CanSendBack = ConditionalResult(permissions.CanSendBack, newPermissions.CanSendBack);
                permissions.CanFileUpload = ConditionalResult(permissions.CanFileUpload, newPermissions.CanFileUpload);
                if (permissions.CanSendTask)
                {
                    permissions.SendTaskLogicalGroupId = newPermissions.SendTaskLogicalGroupId;
                }
                if (permissions.CanForward)
                {
                    permissions.ForwardLogicalGroupId = newPermissions.ForwardLogicalGroupId;
                }
                if (permissions.CanSendtoCommend)
                {
                    permissions.SendCommentLogicalGroupId = newPermissions.SendCommentLogicalGroupId;
                }
            }


            var authInfo = new WorkflowAuthorizationInfo
            {
                Permissions = permissions,
                IsFlowAdmin = isFlowAdmin,
                IsActionFlow = isActionFlow,
                IsOwner = await IsWorkflowOwnerAsync(workflowInstanceId, loginId),
                IsAssigned = await IsAssignToId(workflowInstanceId, loginId),
                IsLastActor = await IsLastActorAsync(workflowInstanceId, loginId),
                IsCommentedTo = await IsCommentedToUserAsync(workflowInstanceId, loginId),
                IsOwnDelegated = await IsOwnDelegation(workflowInstanceId),
                WorkflowStatus = await GetWorkflowStatusAsync(workflowInstanceId)
            };

            return authInfo;
        }

        private async Task<Permission> AuthoInfoAsync(long wfDefId, long? stateDefId, long loginId)
        {
            var permissions = new Permission();
            permissions.SetPermissions(false);

            var resultList = await CanResultOfActionAsync(wfDefId, stateDefId, loginId);
            foreach (var item in resultList)
            {
                switch (item.ActionId)
                {
                    case 1: permissions.CanCreate = true; break;
                    case 2: permissions.CanApproval = true; break;
                    case 3: permissions.CanReject = true; break;
                    case 4: permissions.CanSendBack = true; break;
                    case 5: permissions.CanRollBack = true; break;
                    case 6:
                        permissions.CanForward = true;
                        permissions.ForwardLogicalGroupId = await ToLogicalGroup(wfDefId, stateDefId, loginId, 6);
                        break;
                    case 7:
                        permissions.CanSendtoCommend = true;
                        permissions.SendCommentLogicalGroupId = await ToLogicalGroup(wfDefId, stateDefId, loginId, 7);
                        break;
                    case 8: permissions.CanSendRequestToComment = true; break;
                    case 9: permissions.CanAddToComment = true; break;
                    case 10: permissions.CanCancel = true; break;
                    case 11: permissions.CanSuspend = true; break;
                    case 12: permissions.CanResume = true; break;
                    case 16:
                        permissions.CanSendTask = true;
                        permissions.SendTaskLogicalGroupId = await ToLogicalGroup(wfDefId, stateDefId, loginId, 16);
                        break;
                    case 17: permissions.CanConditionalAccept = true; break;
                    case 18: permissions.CanCorrection = true; break;
                    case 19: permissions.CanFileUpload = true; break;
                }
            }
            return permissions;
        }

        private async Task<IEnumerable<CanResultActionDto>> CanResultOfActionAsync(long wfDefId, long? stateDefId, long loginId)
        {

            string sql = @"
                SELECT DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.ACTION_ID FROM DT_WORKFLOW.YYS_ACTION_AUTHORIZATION
            INNER JOIN DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS on DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.SOURCE_ID=DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGICAL_GROUP_ID
            WHERE DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.WF_DEF_ID=:WF_DEF_ID and (DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.STATE_DEF_ID=:StateDefID or :StateDefID=0) and (DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGIN_ID =-1 or DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGIN_ID=:LOGIN_ID)
            AND DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.IS_ACTIVE=1
            ";

            var parameters = new Dictionary<string, object>
            {
                {"WF_DEF_ID", wfDefId},
                {"StateDefID", stateDefId},
                {"LOGIN_ID", loginId}
            };

            try
            {
                var repository = repositoryFactory.Create("DefaultConnection");
                return await repository.ExecuteQueryAsync<CanResultActionDto>(sql, parameters);
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error in CanResultOfActionAsync: {ex.Message}");
                // Depending on your error handling strategy, you might want to throw or return an empty list
                return Enumerable.Empty<CanResultActionDto>();
            }
        }

        private async Task<long> ToLogicalGroup(long wfDefId, long? stateDefId, long loginId, int actionId)
        {
            string sql = @"
                SELECT
            TOMEMBERLIST.LOGIN_ID,
            TOMEMBERLIST.LOGICAL_GROUP_ID as TO_GROUP_ID
             FROM    DT_WORKFLOW.YYS_ACTION_AUTHORIZATION
            INNER JOIN DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS ON DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.SOURCE_ID   =DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGICAL_GROUP_ID
            INNER JOIN DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS ToMemberList On DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.TO_GROUP_ID=TOMEMBERLIST.LOGICAL_GROUP_ID
            WHERE DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.WF_DEF_ID = :WF_DEF_ID
            AND DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.STATE_DEF_ID = :StateDefID
            AND DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.ACTION_ID = :ACTION_ID
            AND (DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGIN_ID = -1 OR DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGIN_ID = :LOGIN_ID)";

            var parameters = new Dictionary<string, object>
            {
                {"WF_DEF_ID", wfDefId},
                {"StateDefID", stateDefId},
                {"ACTION_ID", actionId},
                {"LOGIN_ID", loginId}
            };

            var repository = repositoryFactory.Create("FrameworkConnection");
            var result = await repository.ExecuteSingleQueryAsync<ToLogicalGroupDto>(sql, parameters);

            return result?.LoginId > 0 ? result.LogicalGroupId : -1;
        }

        private async Task<bool> IsWorkflowOwnerAsync(long workflowInstanceId, long loginId)
        {
            var repository = repositoryFactory.Create("DefaultConnection");
            string sql = @"SELECT COUNT(*)
                            FROM FRAMEWORK.F_WF_WORKFLOW_INSTANCE
                            WHERE FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID = :WorkflowInstanceId
                            AND OWNER_LOGIN_ID = :LoginId";
            var parameters = new Dictionary<string, object>
            {
                {"WorkflowInstanceId", workflowInstanceId},
                {"LoginId", loginId}
            };

            var result = await repository.ExecuteScalarAsync<int>(sql, parameters);
            return result > 0;
        }

        private async Task<bool> IsAssignToId(long workflowInstanceId, long loginId)
        {
            return await workflowHelperService.AssignToId(workflowInstanceId, loginId) == loginId;
        }

        private async Task<bool> IsLastActorAsync(long workflowInstanceId, long loginId)
        {
            return await workflowHelperService.IsLastSendTaskLoginUser(workflowInstanceId);
        }

        private async Task<bool> IsCommentedToUserAsync(long workflowInstanceId, long loginId)
        {
            var commentToIds = await workflowHelperService.CommendToIdLongList(workflowInstanceId);
            return commentToIds.Contains(loginId);
        }

        private async Task<bool> IsOwnDelegation(long workflowInstanceId)
        {
            return await workflowHelperService.OwnDelegation(workflowInstanceId);
        }

        public async Task<string> GetWorkflowStatusAsync(long workflowInstanceId)
        {
            if (workflowInstanceId == 0)
            {
                return "UNKNOWN";
            }

            var repository = repositoryFactory.Create("DefaultConnection");
            if (repository == null)
            {
                throw new NullReferenceException("Repository creation failed.");
            }

            var wfInstance = await repository.GetEntityAsync<FWfWorkflowInstance>(workflowInstanceId);
            if (wfInstance == null)
            {
                throw new NullReferenceException($"Workflow instance with ID {workflowInstanceId} not found.");
            }

            if (wfInstance.WfWorkflowStatusType == null)
            {
                throw new NullReferenceException($"Workflow status type for instance ID {workflowInstanceId} is null.");
            }

            return wfInstance.WfWorkflowStatusType.WfWorkflowStatusTypeCd;
        }

        private long GetHeaderValue(HttpContext context, string headerName)
        {
            if (context.Request.Headers.TryGetValue(headerName, out var headerValue))
            {
                return long.TryParse(headerValue, out var result) ? result : 0;
            }
            return 0;
        }

        private async Task<long> GetStateDefId(long instanceId)
        {
            if (instanceId == 0) return 0;
            var wfInstance = await workflowHelperService.CurrentWfIns();
            return wfInstance?.WfCurrentState?.WfStateDef?.WfStateDefId ?? 0;
        }

        public async Task<WorkflowAuthorizationInfo> GetAuthorizationInfo(
            long wfDefId,
            long instanceId,
            long loginId,
            long? stateDefId = null)
        {
            if (!stateDefId.HasValue)
            {
                var wfInstance = await workflowHelperService.CurrentWfIns();
                if (wfInstance != null && wfInstance.WfCurrentState != null)
                {
                    stateDefId = wfInstance.WfCurrentState.WfStateDef.WfStateDefId;
                }
            }
            var authoInfo = await AuthoInfoAsync(wfDefId, stateDefId, loginId);
            var userInfo = await userService.GetByIdAsync(loginId);
            var sendTaskLogicalGroupItems = await logicalGroupService.GetPersonelList(authoInfo.SendTaskLogicalGroupId);
            var forwardLogicalGroupItems = await logicalGroupService.GetPersonelList(authoInfo.ForwardLogicalGroupId);
            var sendCommentLogicalGroupItems = await logicalGroupService.GetPersonelList(authoInfo.SendCommentLogicalGroupId);

            return new WorkflowAuthorizationInfo
            {
                Permissions = new Permission(
                    authoInfo.CanCreate,
                    authoInfo.CanApproval,
                    authoInfo.CanReject,
                    authoInfo.CanForward,
                    authoInfo.CanSendtoCommend,
                    authoInfo.CanSendRequestToComment,
                    authoInfo.CanSuspend,
                    authoInfo.CanResume,
                    authoInfo.CanCancel,
                    authoInfo.CanFinalize,
                    authoInfo.CanSendTask,
                    authoInfo.CanConditionalAccept,
                    authoInfo.CanCorrection,
                    authoInfo.CanAddToComment,
                    authoInfo.CanRollBack,
                    authoInfo.CanSendBack,
                    authoInfo.ForwardLogicalGroupId,
                    authoInfo.SendCommentLogicalGroupId,
                    sendTaskLogicalGroupItems,
                    forwardLogicalGroupItems,
                    sendCommentLogicalGroupItems,
                    authoInfo.SendTaskLogicalGroupId,
                    authoInfo.CanFileUpload
                ),
                IsActionFlow = await workflowHelperService.IsActionFlow(instanceId),
                IsViewUser = await workflowHelperService.IsViewUser(loginId, instanceId, wfDefId),
                IsReportAdmin = await workflowHelperService.IsReportAdmin(wfDefId, loginId),
                IsFlowAdmin = await workflowHelperService.IsWfAdmin(wfDefId),
                IsSystemAdmin = globalHelpers.SystemAdmins().Contains(userInfo.Username),
                WorkflowStatus = instanceId == 0 ? "UNKNOWN" : await GetWorkflowStatusAsync(instanceId)
            };
        }

        private async Task HandleCompletedWorkflow(WorkflowAuthorizationResponseDto authResponse, WorkflowAuthorizationInfo authInfo, long instanceId, long wfDefId, long loginId, bool isSystemAdmin, bool isFlowAdmin)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");
            bool isLogical = await logicalGroupService.IsDefExistLogicalGroup(GetLogDefGroupIdAction(), wfDefId, "0");
            var wfInstance = await workflowHelperService.CurrentWfIns();


            if (isLogical && (isFlowAdmin || authInfo.IsOwner || authInfo.IsOwnDelegated || isSystemAdmin))
            {
                // Get the last state instance
                var stateInsParams = new Dictionary<string, object>
                {
                    { "InstanceId", instanceId }
                };
                FWfStateInstance lastStateInstance = await repository.ExecuteSingleQueryAsync<FWfStateInstance>("SELECT * FROM FRAMEWORK.F_WF_STATE_INSTANCE WHERE FRAMEWORK.F_WF_STATE_INSTANCE.WF_WORKFLOW_INSTANCE_ID = :InstanceId ORDER BY FRAMEWORK.F_WF_STATE_INSTANCE.WF_STATE_INSTANCE_ID DESC", stateInsParams);
                // Get the updated authorization info
                var updatedAuthInfo = await GetAuthorizationInfo(
                    wfDefId,
                    instanceId,
                    loginId,
                    lastStateInstance.WfStateDefId
                );

                // Set the visibility of the tabs
                authResponse.TabVisibility.AbortTabVisible = true;
                authResponse.TabVisibility.AddCommentTabVisible = true;

                // Set the permissions for the actions
                authResponse.ActionPermissions = updatedAuthInfo.Permissions;
            }
            else if (authInfo.IsActionFlow || authInfo.IsViewUser || authInfo.IsReportAdmin || isFlowAdmin || isSystemAdmin)
            {
                // If a interacted with the workflow, then I will have the View permission
                authResponse.TabVisibility.SetAllTabsInvisible();
                authResponse.ActionPermissions.SetPermissions(false);
            }
            else
            {
                authResponse.CanSeeWorkflow = false;
            }


        }

        private async Task HandleActiveWorkflow(WorkflowAuthorizationResponseDto authResponse, WorkflowAuthorizationInfo authInfo, FWfWorkflowInstance wfInstance, long instanceId, long wfDefId, long loginId, bool isAdmin, bool isFlowAdmin, FWfActionTaskInstance currentActionTaskInstance)
        {
            long logicalN = IsDebugMode ? 384 : 418;
            long logicalND = IsDebugMode ? 333 : 416;

            bool isLogicalN = await logicalGroupService.IsExistLogicalGroup(logicalN, loginId);
            bool isLogicalND = await logicalGroupService.IsExistLogicalGroup(logicalND, loginId);

            var isViewUser = await workflowHelperService.IsViewUser(loginId, instanceId, wfDefId);
            var isReportAdmin = await workflowHelperService.IsReportAdmin(wfDefId, loginId);
            var isLastActor = await workflowHelperService.IsLastSendTaskLoginUser(instanceId);
            var formDelegation = await workflowHelperService.FormDelegation(instanceId);
            var assignToId = await workflowHelperService.AssignToId(instanceId, loginId);
            var isOwner = wfInstance.OwnerLogin.LoginId == loginId;
            var isSuspended = instanceId > 0 && currentActionTaskInstance != null && currentActionTaskInstance.StartTime > DateTime.Now;

            var isAssignToLoginIdCheck = (await workflowHelperService.AssignToIdListLong(loginId)).Contains(loginId);
            var authoInfo = await AuthoInfoAsync(wfDefId, wfInstance.WfCurrentState.WfStateDef.WfStateDefId, loginId);
            if (authoInfo.CanSendBack)
            {
                var sendTaskLogicalGroupItems = await logicalGroupService.GetPersonelList(authInfo.Permissions.SendTaskLogicalGroupId);
                authResponse.ActionPermissions.CanSendBack = true;
                authResponse.ActionPermissions.SendTaskLogicalGroupItems = sendTaskLogicalGroupItems;
            }

            if (authoInfo.CanForward && authInfo.Permissions.ForwardLogicalGroupId != -1)
            {
                var forwardLogicalGroupItems = await logicalGroupService.GetPersonelList(authInfo.Permissions.ForwardLogicalGroupId);
                authResponse.ActionPermissions.ForwardLogicalGroupUserList = forwardLogicalGroupItems;
            }

            if (authoInfo.CanSendRequestToComment && authInfo.Permissions.SendCommentLogicalGroupId != -1)
            {
                var sendCommentLogicalGroupItems = await logicalGroupService.GetPersonelList(authInfo.Permissions.SendCommentLogicalGroupId);
                authResponse.ActionPermissions.SendCommentLogicalGroupUserList = sendCommentLogicalGroupItems;
            }

            authResponse.TabVisibility = await tabVisibilityService.GetTabVisibility(authResponse, authInfo, wfInstance, instanceId, loginId, assignToId, isAdmin, isFlowAdmin, authInfo.IsActionFlow, isViewUser, isReportAdmin, isLastActor, isOwner, isSuspended, formDelegation, isAssignToLoginIdCheck);
            //authResponse.ActionPermissions = actionPermissionService.GetActionPermissions(authInfo, isAdmin, isFlowAdmin, wfDefId, wfInstance.WfCurrentState.WfStateDefId);
            authResponse.TabVisibility = await tabVisibilityService.CheckTabAuthorizeVisibility(
                authResponse.TabVisibility,
                authInfo.Permissions,
                wfInstance,
                loginId
            );

            if (wfInstance.WfWorkflowDef.WfWorkflowDefId == 1551 && isLogicalN && !isLogicalND && wfInstance.WfCurrentState.WfStateDef.WfStateDefId == 1959)
            {
                authResponse.TabVisibility.AbortTabVisible = true;
                authResponse.TabVisibility.AddCommentTabVisible = true;
                authResponse.TabVisibility.ApproveRejectTabVisible = false;
                authResponse.TabVisibility.ForwardTabVisible = false;
                authResponse.TabVisibility.NewRequestTabVisible = false;
                authResponse.TabVisibility.RollbackTabVisible = false;
                authResponse.TabVisibility.SuspendResumeTabVisible = false;
            }


        }

        private long GetLogDefGroupIdAction() => IsDebugMode ? 659 : 659;

        private bool IsDebugMode => configuration.GetValue<bool>("EmailSettings:IsMailDebugMode");
    }
}