﻿using DigiflowAPI.Application.DTOs;
using DigiflowAPI.Application.DTOs.Workflow.Authorization;
using DigiflowAPI.Domain.Entities.Framework;
using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Application.Interfaces.Services.Workflow;
using Microsoft.Extensions.Configuration;

namespace DigiflowAPI.Infrastructure.Security.Authorization.Services.Workflow.Concrete
{
    public class TabVisibilityService : ITabVisibilityService
{
    private readonly IWorkflowHelperService _workflowHelperService;
    private readonly ILogicalGroupService _logicalGroupService;
    private readonly IGlobalHelpers _globalHelpers;
    private readonly IConfiguration _configuration;

    public TabVisibilityService(
        IWorkflowHelperService workflowHelperService,
        ILogicalGroupService logicalGroupService,
        IGlobalHelpers globalHelpers,
        IConfiguration configuration)
    {
        _workflowHelperService = workflowHelperService;
        _logicalGroupService = logicalGroupService;
        _globalHelpers = globalHelpers;
        _configuration = configuration;
    }

    public async Task<TabVisibility> GetTabVisibility(
        WorkflowAuthorizationResponseDto authResponse,
        WorkflowAuthorizationInfo authInfo,
        FWfWorkflowInstance currentWfIns,
        long instanceId,
        long loginId,
        long assignToId,
        bool isSystemAdmin,
        bool isFlowAdmin,
        bool isActionFlow,
        bool isViewUser,
        bool isReportAdmin,
        bool isLastActor,
        bool isOwner,
        bool isSuspended,
        bool formDelegation,
        bool isAssignToLoginIdCheck)
    {
        var tabVisibility = new TabVisibility();
        var lastSendTaskLoginId = await _workflowHelperService.LastSendTaskLoginId(instanceId);
        var lastSendTaskLoginIdList = await _workflowHelperService.LastSendTaskLoginIdList(instanceId);
        var isRollBack = await _workflowHelperService.IsRolledBack(instanceId);

        tabVisibility.SetAllTabsInvisible();


        // Check if FileUpload visible
 /*       if (authInfo.Permissions.CanFileUpload)
        {
            tabVisibility.FileUploadTabVisible = true;
        }
*/
        // Apply Netsis specific logic first (from new requirements)
        if (await HandleSpecificWorkflowRules(tabVisibility, currentWfIns, loginId))
        {
            return tabVisibility;
        }


        // Handle suspended state
        if (isSuspended)
        {
            if (isFlowAdmin || isSystemAdmin || formDelegation || authInfo.IsAssigned || isAssignToLoginIdCheck)
            {
                tabVisibility.SuspendResumeTabVisible = true;
                tabVisibility.AddCommentTabVisible = true;
                tabVisibility.FileUploadTabVisible = authInfo.Permissions.CanFileUpload;
                return tabVisibility;
            }
        }

        // Handle workflow status checks
        if (authInfo.WorkflowStatus != "STARTED")
        {
            if (isActionFlow || isViewUser || isReportAdmin)
            {
                //tabVisibility.AddCommentTabVisible = true;
                return tabVisibility;
            }
        }

        // System/Flow Admin privileges
        if (isSystemAdmin || isFlowAdmin)
        {
            tabVisibility.ApproveRejectTabVisible = true;
            tabVisibility.ForwardTabVisible = true;
            tabVisibility.SendToCommentTabVisible = true;
            tabVisibility.SuspendResumeTabVisible = true;
            tabVisibility.AbortTabVisible = true;
            tabVisibility.AddCommentTabVisible = true;
            tabVisibility.RollbackTabVisible = isRollBack;
            tabVisibility.FileUploadTabVisible = authInfo.Permissions.CanFileUpload;
            return tabVisibility;
        }

        // Role-based permissions
        if (isAssignToLoginIdCheck && isOwner)
        {
            tabVisibility.ApproveRejectTabVisible = true;
            tabVisibility.ForwardTabVisible = true;
            tabVisibility.SuspendResumeTabVisible = true;
            tabVisibility.AddCommentTabVisible = true;
            tabVisibility.AbortTabVisible = true;
            tabVisibility.FileUploadTabVisible = authInfo.Permissions.CanFileUpload;
        }
        else if (formDelegation || assignToId == loginId || isAssignToLoginIdCheck)
        {
            tabVisibility.ApproveRejectTabVisible = true;
            tabVisibility.ForwardTabVisible = true;
            tabVisibility.SendToCommentTabVisible = true;
            tabVisibility.SuspendResumeTabVisible = true;
            tabVisibility.AddCommentTabVisible = true;
            tabVisibility.FileUploadTabVisible = authInfo.Permissions.CanFileUpload;
        }
        else if (isOwner && (lastSendTaskLoginId == loginId || lastSendTaskLoginIdList.Contains(loginId.ToString())))
        {
            tabVisibility.AbortTabVisible = true;
            tabVisibility.AddCommentTabVisible = true;
            tabVisibility.RollbackTabVisible = isRollBack;
            tabVisibility.FileUploadTabVisible = authInfo.Permissions.CanFileUpload;
        }
        else if (isOwner)
        {
            tabVisibility.AddCommentTabVisible = true;
            tabVisibility.AbortTabVisible = true;
            tabVisibility.FileUploadTabVisible = authInfo.Permissions.CanFileUpload;
        }
        else if (lastSendTaskLoginId == loginId || lastSendTaskLoginIdList.Contains(loginId.ToString()))
        {
            tabVisibility.AddCommentTabVisible = true;
            tabVisibility.RollbackTabVisible = isRollBack;
            tabVisibility.FileUploadTabVisible = authInfo.Permissions.CanFileUpload;
        }
        else if (authInfo.IsCommentedTo || isActionFlow || isViewUser || isReportAdmin)
        {
            tabVisibility.AddCommentTabVisible = true;
        }
        else
        {
            authResponse.CanSeeWorkflow = false;
        }

        // Apply permission-based visibility if no tabs are visible yet
        if (!tabVisibility.CheckIfAnyTabVisible())
        {
            ApplyPermissionBasedVisibility(tabVisibility, authInfo.Permissions, isRollBack);
        }

        // Special case for new requests
        if (instanceId == 0)
        {
            tabVisibility.SetAllTabsInvisible();
            tabVisibility.NewRequestTabVisible = authInfo.Permissions.CanCreate;
        }

        return tabVisibility;
    }

    private async Task<bool> HandleSpecificWorkflowRules(TabVisibility tabVisibility, FWfWorkflowInstance wfInstance, long loginId)
    {
        if (wfInstance?.WfWorkflowDef?.WfWorkflowDefId == 1551)
        {
            long logicalN = IsDebugMode ? 384 : 418;
            long logicalND = IsDebugMode ? 333 : 416;

            bool isLogicalN = await _logicalGroupService.IsExistLogicalGroup(logicalN, loginId);
            bool isLogicalND = await _logicalGroupService.IsExistLogicalGroup(logicalND, loginId);

            if (isLogicalN && !isLogicalND && wfInstance.WfCurrentState?.WfStateDef?.WfStateDefId == 1959)
            {
                tabVisibility.AbortTabVisible = true;
                tabVisibility.AddCommentTabVisible = true;
                tabVisibility.ApproveRejectTabVisible = false;
                tabVisibility.ForwardTabVisible = false;
                tabVisibility.NewRequestTabVisible = false;
                tabVisibility.RollbackTabVisible = false;
                tabVisibility.SuspendResumeTabVisible = false;
                return true;
            }
        }
        return false;
    }

    private void ApplyPermissionBasedVisibility(TabVisibility tabVisibility, Permission permissions, bool isRollBack)
    {
        if (permissions.CanCreate)
        {
            tabVisibility.NewRequestTabVisible = true;
        }

        if (permissions.CanApproval || permissions.CanReject ||
            permissions.CanCorrection || permissions.CanConditionalAccept)
        {
            tabVisibility.ApproveRejectTabVisible = true;
        }

        if (permissions.CanForward)
        {
            tabVisibility.ForwardTabVisible = true;
        }

        if (permissions.CanSendtoCommend)
        {
            tabVisibility.SendToCommentTabVisible = true;
        }

        if (permissions.CanSuspend || permissions.CanResume)
        {
            tabVisibility.SuspendResumeTabVisible = true;
        }

        if (permissions.CanCancel)
        {
            tabVisibility.AbortTabVisible = true;
        }

        if (permissions.CanAddToComment || permissions.CanSendRequestToComment)
        {
            tabVisibility.AddCommentTabVisible = true;
        }

        if (permissions.CanRollBack && isRollBack)
        {
            tabVisibility.RollbackTabVisible = true;
        }

        if (permissions.CanFileUpload)
        {
            tabVisibility.FileUploadTabVisible = true;
        }
    }

    public async Task<TabVisibility> CheckTabAuthorizeVisibility(
        TabVisibility tabVisibility,
        Permission permissions,
        FWfWorkflowInstance currentWfIns,
        long loginId)
    {
        // Check Create permission
        if (tabVisibility.NewRequestTabVisible)
        {
            if (!permissions.CanCreate)
            {
                tabVisibility.NewRequestTabVisible = false;
            }
        }

        // Check Approve/Reject permission
        if (tabVisibility.ApproveRejectTabVisible)
        {
            if (!permissions.CanApproval && !permissions.CanReject &&
                !permissions.CanCorrection && !permissions.CanConditionalAccept)
            {
                tabVisibility.ApproveRejectTabVisible = false;
            }
        }

        // Check Forward permission
        if (tabVisibility.ForwardTabVisible)
        {
            if (!permissions.CanForward)
            {
                tabVisibility.ForwardTabVisible = false;
            }
        }

        // Check Send to Comment permission
        if (tabVisibility.SendToCommentTabVisible)
        {
            if (!permissions.CanSendtoCommend)
            {
                tabVisibility.SendToCommentTabVisible = false;
            }
        }

        // Check Suspend/Resume permission
        if (tabVisibility.SuspendResumeTabVisible)
        {
            if (!permissions.CanSuspend && !permissions.CanResume)
            {
                tabVisibility.SuspendResumeTabVisible = false;
            }
        }

        // Check Cancel/Abort permission
        if (tabVisibility.AbortTabVisible)
        {
            if (!permissions.CanCancel || currentWfIns.WfWorkflowDef.WfWorkflowDefId == 1312)
            {
                bool isLogical = await _logicalGroupService.IsDefExistLogicalGroup(
                    IsDebugMode ? 659 : 659,  // logDefGroupIdAction
                    currentWfIns.WfWorkflowDef.WfWorkflowDefId,
                    "0"
                );

                if (!isLogical || currentWfIns.WfCurrentState?.WfCurrentActionInstanceId != null)
                {
                    tabVisibility.AbortTabVisible = false;
                }
                else
                {
                    bool isLogicalAdmin = await _logicalGroupService.IsDefExistLogicalGroup(
                        IsDebugMode ? 659 : 659,  // logDefGroupIdAction
                        currentWfIns.WfWorkflowDef.WfWorkflowDefId,
                        "1"
                    );

                    if (!isLogicalAdmin)
                    {
                        var flowAdmin = await _workflowHelperService.FlowAdmin(currentWfIns.WfWorkflowInstanceId, loginId);
                        if (flowAdmin?.LoginId != loginId)
                        {
                            tabVisibility.AbortTabVisible = false;
                        }
                    }
                }
            }
        }

        // Check Add Comment permission
        if (tabVisibility.AddCommentTabVisible)
        {
            var isAssignToLoginIdCheck = (await _workflowHelperService.AssignToIdListLong(currentWfIns.WfWorkflowInstanceId)).Contains(loginId);
            var formDelegation = await _workflowHelperService.FormDelegation(currentWfIns.WfWorkflowInstanceId);
            var isFlowAdmin = await _workflowHelperService.IsWfAdmin(currentWfIns.WfWorkflowDef.WfWorkflowDefId);
            var canCommentToLogin = (await _workflowHelperService.CommendToIdLongList(currentWfIns.WfWorkflowInstanceId)).Contains(loginId);
            var canCommentToLoginDelege = (await _workflowHelperService.GetCommendToLoginDelegeIDList(currentWfIns.WfWorkflowInstanceId)).Contains(loginId);

            if (isAssignToLoginIdCheck || formDelegation || isFlowAdmin || canCommentToLogin || canCommentToLoginDelege)
            {
                if (!permissions.CanSendRequestToComment)
                {
                    tabVisibility.AddCommentTabVisible = false;
                }
            }
            else if (!permissions.CanAddToComment)
            {
                tabVisibility.AddCommentTabVisible = false;
            }
        }

        // Check Rollback permission
        if (tabVisibility.RollbackTabVisible)
        {
            if (!permissions.CanRollBack)
            {
                tabVisibility.RollbackTabVisible = false;
            }
        }

        // Handle special workflow rules - Netsis special case
        if (currentWfIns.WfWorkflowDef.WfWorkflowDefId == 1551)
        {
            var logicalN = IsDebugMode ? 384 : 418;
            var logicalND = IsDebugMode ? 333 : 416;
            var isLogicalN = await _logicalGroupService.IsExistLogicalGroup(logicalN, loginId);
            var isLogicalND = await _logicalGroupService.IsExistLogicalGroup(logicalND, loginId);

            if (isLogicalN && !isLogicalND && currentWfIns.WfCurrentState?.WfStateDef?.WfStateDefId == 1959)
            {
                tabVisibility.AbortTabVisible = true;
                tabVisibility.AddCommentTabVisible = true;
                tabVisibility.ApproveRejectTabVisible = false;
                tabVisibility.ForwardTabVisible = false;
                tabVisibility.NewRequestTabVisible = false;
                tabVisibility.RollbackTabVisible = false;
                tabVisibility.SuspendResumeTabVisible = false;
            }
        }


        return tabVisibility;
    }


    private bool IsDebugMode => _configuration.GetValue<bool>("EmailSettings:IsMailDebugMode");
    }
}