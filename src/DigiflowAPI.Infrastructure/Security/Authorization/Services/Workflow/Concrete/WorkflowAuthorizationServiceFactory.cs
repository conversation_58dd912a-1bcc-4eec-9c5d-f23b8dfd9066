﻿using DigiflowAPI.Infrastructure.Security.Authorization.Services.Workflow.Abstract;
using Microsoft.Extensions.DependencyInjection;

namespace DigiflowAPI.Infrastructure.Security.Authorization.Services.Workflow.Concrete
{
    public class WorkflowAuthorizationServiceFactory
    {
        private readonly IServiceProvider _serviceProvider;

        public WorkflowAuthorizationServiceFactory(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public IWorkflowAuthorizationService CreateWorkflowAuthorizationService()
        {
            return _serviceProvider.CreateScope().ServiceProvider
                .GetRequiredService<IWorkflowAuthorizationService>();
        }
    }
}