﻿using DigiflowAPI.Application.DTOs;
using DigiflowAPI.Application.DTOs.Workflow.Authorization;
using DigiflowAPI.Application.Interfaces.Services;

namespace DigiflowAPI.Infrastructure.Security.Authorization.Services.Workflow.Concrete
{
    public class ActionPermissionService : IActionPermissionService
    {
        public Permission GetActionPermissions(WorkflowAuthorizationInfo authInfo, bool isAdmin, bool isFlowAdmin, long wfDefId, long stateDefId)
        {
            var permissions = new Permission();

            if (isAdmin || isFlowAdmin)
            {
                SetAllPermissions(permissions, true);
            }
            else
            {
                permissions = authInfo.Permissions;
                AdjustPermissionsBasedOnWorkflowState(permissions, authInfo, wfDefId, stateDefId);
            }

            return permissions;
        }

        private void SetAllPermissions(Permission permissions, bool value)
        {
            permissions.SetPermissions(value);
        }

        private void AdjustPermissionsBasedOnWorkflowState(Permission permissions, WorkflowAuthorizationInfo authInfo, long wfDefId, long stateDefId)
        {
            if (authInfo.WorkflowStatus != "STARTED")
            {
                permissions.CanApproval = false;
                permissions.CanReject = false;
            }

            if (authInfo.IsOwner && authInfo.IsLastActor)
            {
                permissions.CanRollBack = true;
            }
        }
    }
}
