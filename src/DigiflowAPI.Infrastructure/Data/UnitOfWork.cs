using DigiflowAPI.Domain.Interfaces;
using DigiflowAPI.Domain.Interfaces.Repositories;
using DigiflowAPI.Infrastructure.Data.Repositories;
using DigiflowAPI.Application.Interfaces.DataAccess;
using DigiflowAPI.Application.Interfaces.Services;
using Microsoft.AspNetCore.Http;
using System.Data;

namespace DigiflowAPI.Infrastructure.Data;

/// <summary>
/// Implementation of the Unit of Work pattern to manage database transactions and coordinate repositories.
/// Adapted for Oracle-based data access architecture.
/// </summary>
public class UnitOfWork : IUnitOfWork
{
    private readonly IOracleDataAccessRepositoryFactory _repositoryFactory;
    private readonly IGlobalHelpers _globalHelpers;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IOracleConnectionManager _connectionManager;
    private bool _disposed;
    
    // Repository instances
    private IUserRepository _userRepository;
    private IWorkflowRepository _workflowRepository;
    private IHistoryRepository _historyRepository;
    private IInboxRepository _inboxRepository;
    private IOrganizationRepository _organizationRepository;
    private IPermissionRepository _permissionRepository;
    private IEmailTemplateRepository _emailTemplateRepository;
    private ISlideRepository _slideRepository;
    private ILogicalGroupRepository _logicalGroupRepository;
    private ISharePointRepository _sharePointRepository;
    private IRepository _repository;
    
    public UnitOfWork(
        IOracleDataAccessRepositoryFactory repositoryFactory,
        IGlobalHelpers globalHelpers,
        IHttpContextAccessor httpContextAccessor,
        IOracleConnectionManager connectionManager)
    {
        _repositoryFactory = repositoryFactory ?? throw new ArgumentNullException(nameof(repositoryFactory));
        _globalHelpers = globalHelpers ?? throw new ArgumentNullException(nameof(globalHelpers));
        _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
    }
    
    // Repository properties with lazy initialization
    public IUserRepository Users => 
        _userRepository ??= new UserRepository(_repositoryFactory, _globalHelpers, _httpContextAccessor);
        
    public IWorkflowRepository Workflows => 
        _workflowRepository ??= new WorkflowRepository(_repositoryFactory, _globalHelpers, _httpContextAccessor);
        
    public IHistoryRepository Histories => 
        _historyRepository ??= new HistoryRepository(_repositoryFactory, _globalHelpers, _httpContextAccessor);
        
    public IInboxRepository Inboxes => 
        _inboxRepository ??= new InboxRepository(_repositoryFactory, _globalHelpers, _httpContextAccessor);
        
    public IOrganizationRepository Organizations => 
        _organizationRepository ??= new OrganizationRepository(_repositoryFactory, _globalHelpers, _httpContextAccessor);
        
    public IPermissionRepository Permissions => 
        _permissionRepository ??= new PermissionRepository(_repositoryFactory, _globalHelpers, _httpContextAccessor);
        
    public IEmailTemplateRepository EmailTemplates => 
        _emailTemplateRepository ??= new EmailTemplateRepository(_repositoryFactory, _globalHelpers, _httpContextAccessor);
        
    public ISlideRepository Slides => 
        _slideRepository ??= new SlideRepository(_repositoryFactory, _globalHelpers, _httpContextAccessor);
        
    public ILogicalGroupRepository LogicalGroups => 
        _logicalGroupRepository ??= new LogicalGroupRepository(_repositoryFactory, _globalHelpers, _httpContextAccessor);
        
    public ISharePointRepository SharePoint => 
        _sharePointRepository ??= new SharePointRepository(_repositoryFactory, _globalHelpers, _httpContextAccessor);
        
    public IRepository Repository => 
        _repository ??= new OracleGenericRepository(_repositoryFactory);
    
    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // In Oracle-based architecture, changes are typically committed at the transaction level
        // Individual repository operations may auto-commit based on configuration
        return await Task.FromResult(0);
    }
    
    public int SaveChanges()
    {
        // In Oracle-based architecture, changes are typically committed at the transaction level
        // Individual repository operations may auto-commit based on configuration
        return 0;
    }
    
    public async Task<IDbContextTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        // For Oracle, we need to implement transaction management differently
        // This is a placeholder - actual implementation would depend on Oracle connection management
        throw new NotImplementedException("Oracle transaction management needs to be implemented based on the existing connection manager.");
    }
    
    public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
    {
        // Placeholder for Oracle transaction commit
        throw new NotImplementedException("Oracle transaction management needs to be implemented based on the existing connection manager.");
    }
    
    public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
    {
        // Placeholder for Oracle transaction rollback
        throw new NotImplementedException("Oracle transaction management needs to be implemented based on the existing connection manager.");
    }
    
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
    
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // Dispose managed resources if any
            }
            
            _disposed = true;
        }
    }
}

/// <summary>
/// Oracle-specific implementation of IDbContextTransaction
/// </summary>
public class OracleDbTransaction : IDbContextTransaction
{
    private readonly IDbTransaction _transaction;
    
    public OracleDbTransaction(IDbTransaction transaction)
    {
        _transaction = transaction ?? throw new ArgumentNullException(nameof(transaction));
    }
    
    public async Task CommitAsync(CancellationToken cancellationToken = default)
    {
        _transaction.Commit();
        await Task.CompletedTask;
    }
    
    public async Task RollbackAsync(CancellationToken cancellationToken = default)
    {
        _transaction.Rollback();
        await Task.CompletedTask;
    }
    
    public void Dispose()
    {
        _transaction?.Dispose();
    }
}