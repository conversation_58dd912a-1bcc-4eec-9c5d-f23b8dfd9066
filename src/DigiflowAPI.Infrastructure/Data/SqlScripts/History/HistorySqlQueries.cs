﻿using Microsoft.SharePoint.Client;
using System.Text;
using System.Text.RegularExpressions;

namespace DigiflowAPI.Infrastructure.Data.SqlScripts.History
{
    public static class HistorySqlQueries
    {
        // Method to validate and sanitize years parameter
        private static string? ValidateYearsParameter(string? filteredYears)
        {
            if (string.IsNullOrEmpty(filteredYears))
                return null;

            // Split by comma and validate each part
            var years = filteredYears.Split(',');
            var validYears = new List<string>();

            foreach (var year in years)
            {
                var trimmedYear = year.Trim();

                // Validate that it's a 4-digit number between reasonable years (1900-2100)
                if (Regex.IsMatch(trimmedYear, @"^\d{4}$"))
                {
                    if (int.TryParse(trimmedYear, out int yearValue) && yearValue >= 1900 && yearValue <= 2100)
                    {
                        validYears.Add(trimmedYear);
                    }
                }
            }

            return validYears.Count > 0 ? string.Join(",", validYears) : null;
        }

        public static string GetHistorySql(string language, bool ordered, string workflowType, string lastAction, string wfLastAction, string? filteredYears = null)
        {
            // Validate years parameter
            var validatedYears = ValidateYearsParameter(filteredYears);

            string extraSelectColumns = language.ToUpper() == "EN"
                ? @"
    A.FLOWDESC AS FLOWNAME,
    A.STATEDESC AS STATENAME,
    A.WF_WORKFLOW_HISTORY_TYPE_ENG AS WF_WORKFLOW_HISTORY_TYPE_NAME,
    A.WF_LAST_ACTION_TYPE_ENG AS WF_LAST_ACTION_TYPE_NAME,"
                : @"
    A.FLOWNAME,
    A.STATENAME,";

            string tableName = "DT_WORKFLOW.TB_WORKFLOW_HISTORY_VIEW";

            // Start building the base query
            var queryBuilder = new StringBuilder();
            queryBuilder.AppendLine("SELECT");
            queryBuilder.AppendLine("    REPLACE(COPYINSTANCELINK2, '00000', :loginValue) AS COPYINSTANCELINK,");
            queryBuilder.AppendLine("    REPLACE(WFINSTANCELINK2, '00000', :loginValue) AS WFINSTANCELINK,");
            queryBuilder.AppendLine("    (SELECT DT_WORKFLOW.DP_HR_USERS.E_MAIL FROM DT_WORKFLOW.DP_HR_USERS WHERE DT_WORKFLOW.DP_HR_USERS.F_LOGIN_ID = A.LASTLOGINID) as LASTLOGINEMAIL,");
            queryBuilder.AppendLine("    REPLACE(MWFINSTANCELINK2, '00000', :loginValue) AS MWFINSTANCELINK,");
            queryBuilder.AppendLine(extraSelectColumns);
            queryBuilder.AppendLine("    B.ROUTE AS ROUTE,");
            queryBuilder.AppendLine("    A.*");
            queryBuilder.AppendLine($"FROM {tableName} A");
            queryBuilder.AppendLine("LEFT JOIN DT_WORKFLOW.WF_XML_DEFINITION B ON A.WF_WORKFLOW_DEF_ID = B.VALUE");
            queryBuilder.AppendLine("WHERE");
            queryBuilder.AppendLine("    VV_LOGIN_ID = :loginValue");

            // Conditions
            string wfStatusCondition;
            if (workflowType != "0")
            {
                // Remove OR '1' = '1'
                wfStatusCondition = "    AND WF_WORKFLOW_STATUS_TYPE_CD = :WfStatusType";
            }
            else
            {
                // For WorkFlowType == "0", include OR '1' = '1'
                wfStatusCondition = "    AND (WF_WORKFLOW_STATUS_TYPE_CD = :WfStatusType OR '1' = '1')";
            }

            string lastActionCondition;
            if (workflowType == "0" && lastAction == "SUSPEND")
            {
                // Remove OR '0' = '0'
                lastActionCondition = "    AND LastAction = :LastAction";
            }
            else
            {
                lastActionCondition = "    AND (LastAction = :LastAction OR '0' = '0')";
            }

            // Append conditions
            queryBuilder.AppendLine(wfStatusCondition);
            queryBuilder.AppendLine("    AND (WF_ACTION_STATUS_TYPE_CD = :TaskStatus OR '3' = '3')");
            queryBuilder.AppendLine(lastActionCondition);

            // Workflow type specific filters
            if (!string.IsNullOrEmpty(workflowType))
            {
                if (workflowType == "STARTED")
                {
                    // Exclude suspended, rejected, canceled workflows
                    queryBuilder.AppendLine("    AND WF_LAST_ACTION NOT IN ('SUSPEND', 'REJECTED', 'CANCEL')");
                }
                else if (workflowType == "COMPLETED" && wfLastAction == "0")
                {
                    queryBuilder.AppendLine("    AND ( WF_LAST_ACTION = :WF_LAST_ACTION OR '2' = '2' )");
                }
                else if (workflowType != "0" && workflowType != "STARTED")
                {
                    queryBuilder.AppendLine("    AND WF_LAST_ACTION = :WF_LAST_ACTION");
                }
            }

            // Year filter - use validated years
            if (!string.IsNullOrEmpty(validatedYears))
            {
                queryBuilder.AppendLine($"    AND EXTRACT(YEAR FROM WFDATE) IN ({validatedYears})");
            }

            // Ordering
            if (ordered)
            {
                queryBuilder.AppendLine("ORDER BY WFINSID DESC");
            }

            return queryBuilder.ToString();
        }
        /// <summary>
        /// Generates the SQL query for fetching the history of workflows for a user.
        /// </summary>
        /// <param name="language">Language code ("EN" for English, others for default).</param>
        /// <param name="ordered">Whether to include an ORDER BY clause.</param>
        /// <param name="workflowType">Type of the workflow to filter.</param>
        /// <param name="filteredYears">Comma-separated years to filter by.</param>
        /// <returns>The SQL query string.</returns>
        public static string GetHistorySql(string language, bool ordered, string workflowType, string? filteredYears = null)
        {
            // Validate years parameter
            var validatedYears = ValidateYearsParameter(filteredYears);

            StringBuilder sqlBuilder = new StringBuilder();

            // Add ORDERED hint if required
            if (ordered)
            {
                sqlBuilder.AppendLine("SELECT /*+ ORDERED */");
            }
            else
            {
                sqlBuilder.AppendLine("SELECT");
            }

            // Common SELECT columns
            sqlBuilder.AppendLine("    REPLACE (COPYINSTANCELINK2, '00000', :loginValue) AS COPYINSTANCELINK,");
            sqlBuilder.AppendLine("    REPLACE (WFINSTANCELINK2, '00000', :loginValue) AS WFINSTANCELINK,");
            sqlBuilder.AppendLine("    REPLACE (MWFINSTANCELINK2, '00000', :loginValue) AS MWFINSTANCELINK,");

            // Language-specific columns
            if (!string.IsNullOrEmpty(language) && language.ToUpper() == "EN")
            {
                sqlBuilder.AppendLine("    A.FLOWDESC AS FLOWNAME,");
                sqlBuilder.AppendLine("    A.STATEDESC AS STATENAME,");
                sqlBuilder.AppendLine("    A.WF_WORKFLOW_HISTORY_TYPE_ENG AS WF_WORKFLOW_HISTORY_TYPE_NAME,");
                sqlBuilder.AppendLine("    A.WF_LAST_ACTION_TYPE_ENG AS WF_LAST_ACTION_TYPE_NAME,");
            }

            // Add the rest of the columns
            sqlBuilder.AppendLine("    A.*");
            sqlBuilder.AppendLine("FROM DT_WORKFLOW.TB_WORKFLOW_HISTORY_VIEW A");
            sqlBuilder.AppendLine("WHERE");
            sqlBuilder.AppendLine("    VV_LOGIN_ID = :loginValue");
            sqlBuilder.AppendLine("    AND (WF_WORKFLOW_STATUS_TYPE_CD = :WfStatusType OR '1' = '1')");
            sqlBuilder.AppendLine("    AND (WF_ACTION_STATUS_TYPE_CD = :TaskStatus OR '3' = '3')");
            sqlBuilder.AppendLine("    AND (LastAction = :LastAction OR '0' = '0')");
            sqlBuilder.AppendLine("    AND (WF_LAST_ACTION = :WF_LAST_ACTION OR '2' = '2')");

            // Modify SQL based on workflowType
            if (!string.IsNullOrEmpty(workflowType) && workflowType != "0")
            {
                sqlBuilder.Replace("OR '1' = '1'", ""); // Remove the always true condition
                sqlBuilder.Replace(":WfStatusType", $"'{workflowType}'"); // Replace parameter with value

                if (workflowType == "STARTED")
                {
                    // Exclude certain actions for started workflows
                    sqlBuilder.Replace("(WF_LAST_ACTION = :WF_LAST_ACTION OR '2' = '2')", "WF_LAST_ACTION NOT IN('SUSPEND','REJECTED','CANCEL')");
                    sqlBuilder.Replace(":WF_LAST_ACTION", "NULL"); // Remove parameter as it's not needed
                }
                else if (workflowType == "SUSPENDED")
                {
                    sqlBuilder.Replace("OR '0' = '0'", ""); // Remove the always true condition
                    sqlBuilder.Replace(":LastAction", "'SUSPEND'"); // Set LastAction to 'SUSPEND'
                }
                else if (workflowType == "ACCEPTED" || workflowType == "REJECTED" || workflowType == "CANCELED")
                {
                    sqlBuilder.Replace("OR '2' = '2'", ""); // Remove the always true condition
                    sqlBuilder.Replace(":WF_LAST_ACTION", $"'{workflowType}'"); // Set WF_LAST_ACTION
                    sqlBuilder.Replace(":WfStatusType", "'COMPLETED'"); // Set status to 'COMPLETED'
                }
            }

            // Add year filtering if provided - use validated years
            if (!string.IsNullOrEmpty(validatedYears))
            {
                sqlBuilder.AppendLine($"    AND EXTRACT(YEAR FROM WFDATE) IN ({validatedYears})");
            }

            // Add ORDER BY clause if required
            if (ordered)
            {
                sqlBuilder.AppendLine("ORDER BY WFINSID DESC");
            }

            return sqlBuilder.ToString();
        }

        /// <summary>
        /// Generates the SQL query for fetching the history from the control view.
        /// </summary>
        /// <param name="language">Language code ("EN" for English, others for default).</param>
        /// <param name="ordered">Whether to include an ORDER BY clause.</param>
        /// <param name="workflowType">Type of the workflow to filter.</param>
        /// <param name="filteredYears">Comma-separated years to filter by.</param>
        /// <returns>The SQL query string.</returns>
        public static string GetHistorySqlKontrol(string language, bool ordered, string workflowType, string? filteredYears = null)
        {
            // Validate years parameter
            var validatedYears = ValidateYearsParameter(filteredYears);

            // Similar to GetHistorySql but uses the control view
            StringBuilder sqlBuilder = new StringBuilder();

            if (ordered)
            {
                sqlBuilder.AppendLine("SELECT /*+ ORDERED */");
            }
            else
            {
                sqlBuilder.AppendLine("SELECT");
            }

            sqlBuilder.AppendLine("    REPLACE (COPYINSTANCELINK2, '00000', :loginValue) AS COPYINSTANCELINK,");
            sqlBuilder.AppendLine("    REPLACE (WFINSTANCELINK2, '00000', :loginValue) AS WFINSTANCELINK,");
            sqlBuilder.AppendLine("    REPLACE (MWFINSTANCELINK2, '00000', :loginValue) AS MWFINSTANCELINK,");

            if (!string.IsNullOrEmpty(language) && language.ToUpper() == "EN")
            {
                sqlBuilder.AppendLine("    A.FLOWDESC AS FLOWNAME,");
                sqlBuilder.AppendLine("    A.STATEDESC AS STATENAME,");
                sqlBuilder.AppendLine("    A.WF_WORKFLOW_HISTORY_TYPE_ENG AS WF_WORKFLOW_HISTORY_TYPE_NAME,");
                sqlBuilder.AppendLine("    A.WF_LAST_ACTION_TYPE_ENG AS WF_LAST_ACTION_TYPE_NAME,");
            }

            sqlBuilder.AppendLine("    A.*");
            sqlBuilder.AppendLine("FROM DT_WORKFLOW.VW_WORKFLOW_HISTORY_VIEW_2 A");
            sqlBuilder.AppendLine("WHERE");
            sqlBuilder.AppendLine("    VV_LOGIN_ID = :loginValue");
            sqlBuilder.AppendLine("    AND (WF_WORKFLOW_STATUS_TYPE_CD = :WfStatusType OR '1' = '1')");
            sqlBuilder.AppendLine("    AND (WF_ACTION_STATUS_TYPE_CD = :TaskStatus OR '3' = '3')");
            sqlBuilder.AppendLine("    AND (LastAction = :LastAction OR '0' = '0')");
            sqlBuilder.AppendLine("    AND (WF_LAST_ACTION = :WF_LAST_ACTION OR '2' = '2')");

            // Modify SQL based on workflowType
            if (!string.IsNullOrEmpty(workflowType) && workflowType != "0")
            {
                sqlBuilder.Replace("OR '1' = '1'", ""); // Remove the always true condition
                sqlBuilder.Replace(":WfStatusType", $"'{workflowType}'"); // Replace parameter with value

                if (workflowType == "STARTED")
                {
                    // Exclude certain actions for started workflows
                    sqlBuilder.Replace("(WF_LAST_ACTION = :WF_LAST_ACTION OR '2' = '2')", "WF_LAST_ACTION NOT IN('SUSPEND','REJECTED','CANCEL')");
                    sqlBuilder.Replace(":WF_LAST_ACTION", "NULL"); // Remove parameter as it's not needed
                }
                else if (workflowType == "SUSPENDED")
                {
                    sqlBuilder.Replace("OR '0' = '0'", ""); // Remove the always true condition
                    sqlBuilder.Replace(":LastAction", "'SUSPEND'"); // Set LastAction to 'SUSPEND'
                }
                else if (workflowType == "ACCEPTED" || workflowType == "REJECTED" || workflowType == "CANCELED")
                {
                    sqlBuilder.Replace("OR '2' = '2'", ""); // Remove the always true condition
                    sqlBuilder.Replace(":WF_LAST_ACTION", $"'{workflowType}'"); // Set WF_LAST_ACTION
                    sqlBuilder.Replace(":WfStatusType", "'COMPLETED'"); // Set status to 'COMPLETED'
                }
            }

            // Add year filtering if provided - use validated years
            if (!string.IsNullOrEmpty(validatedYears))
            {
                sqlBuilder.AppendLine($"    AND EXTRACT(YEAR FROM WFDATE) IN ({validatedYears})");
            }

            // Add ORDER BY clause if required
            if (ordered)
            {
                sqlBuilder.AppendLine("ORDER BY WFINSID DESC");
            }

            return sqlBuilder.ToString();
        }

        /// <summary>
        /// Generates the SQL query for fetching years information for the user's history.
        /// </summary>
        /// <returns>The SQL query string.</returns>
        public static string GetYearsInfoForUserHistorySql()
        {
            return @"
SELECT DISTINCT TO_CHAR(WFDATE, 'YYYY') AS WFDATE
FROM DT_WORKFLOW.TB_WORKFLOW_HISTORY_VIEW
WHERE VV_LOGIN_ID = :loginValue
ORDER BY WFDATE DESC";
        }

        // Additional methods can be added similarly, such as:
        // GetHistorySqlOrdered, GetHistorySqlEngOrdered, etc.

        /// <summary>
        /// Generates the SQL query for fetching the ordered history.
        /// </summary>
        public static string GetHistorySqlOrdered(string language, string workflowType, string? filteredYears = null)
        {
            return GetHistorySql(language, true, workflowType, filteredYears);
        }

        /// <summary>
        /// Generates the SQL query for fetching the ordered control history.
        /// </summary>
        public static string GetHistorySqlKontrolOrdered(string language, string workflowType, string? filteredYears = null)
        {
            return GetHistorySqlKontrol(language, true, workflowType, filteredYears);
        }

        public static string GetAdminHistorySQL()
        {
            return @"
                Select DISTINCT H.* from DT_WORKFLOW.VW_WORKFLOW_HISTORY H, DT_WORKFLOW.TB_WORKFLOW_HISTORY_VIEW A
                WHERE (H.WF_WORKFLOW_INSTANCE_ID= :WorkFlowId or :WorkFlowId = 0)
                    AND (H.WF_WORKFLOW_DEF_ID IN (:WorkFlowDefId) or :WorkFlowDefId = 0)
                    AND (H.WF_WORKFLOW_DEF_ID IN (SELECT distinct FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID
                                                    FROM FRAMEWORK.F_WF_WORKFLOW_DEF
                                                    LEFT JOIN DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS  ON DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.WF_DEF_ID=FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID
                                                    WHERE (DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.ADMIN_ID=:loginValue)
                                                    AND FRAMEWORK.F_WF_WORKFLOW_DEF.VERSION_NUMBER>0) OR :ISADMIN = 1)
                                                    AND (H.WF_WORKFLOW_INSTANCE_ID = :WorkFlowId or :WorkFlowId = 0)
                                                    AND (
                                                    H.DATES
                                                    between to_date(:StartDate,'DD.MM.YYYY') and to_date(:EndDate,'DD.MM.YYYY') OR '4' = '4')
                                                    AND H.WF_WORKFLOW_INSTANCE_ID = A.WFINSID
                                                    AND (A.WF_WORKFLOW_STATUS_TYPE_CD = :WfStatusType OR '1' = '1')
                                                    AND (A.WF_ACTION_STATUS_TYPE_CD = :TaskStatus OR '3' = '3')
                                                    AND (LastAction = :LastAction OR '0' = '0')
                                                    AND (A.WF_LAST_ACTION = :WF_LAST_ACTION OR '2' = '2')
                ORDER BY H.WF_WORKFLOW_INSTANCE_ID, H.DATES ASC
            ";
        }

        public static string GetTakedOnInbox = @"SELECT
    WFINS.WF_WORKFLOW_INSTANCE_ID as WF_INS_ID,
    WFDEF.NAME || ' - ' || ACTDEF.DESCRIPTION as NAME,
    ACTINS.START_TIME AS WF_DATE,
    WFTASKDEF.TASK_SCREEN as TASK_SCREEN,
    (SELECT NAME_SURNAME FROM DT_WORKFLOW.DP_HR_USERS dhr WHERE dhr.F_LOGIN_ID = wfins.OWNER_LOGIN_ID ) AS WF_OWNER,
    DT_WORKFLOW.DIGIFLOWPACKACE.GET_LAST_MODIFIED_BY (WFINS.WF_WORKFLOW_INSTANCE_ID) as WF_LAST_MODIFIED_BY
FROM
    FRAMEWORK.F_WF_ACTION_TASK_INSTANCE taskins
    INNER JOIN FRAMEWORK.F_WF_ACTION_INSTANCE actins ON taskins.WF_ACTION_TASK_INSTANCE_ID = actins.WF_ACTION_INSTANCE_ID
    INNER JOIN FRAMEWORK.F_WF_ACTION_DEF actdef ON ACTINS.WF_ACTION_DEF_ID = ACTDEF.WF_ACTION_DEF_ID
    INNER JOIN FRAMEWORK.F_WF_STATE_INSTANCE stins ON actins.WF_STATE_INSTANCE_ID = stins.WF_STATE_INSTANCE_ID
    INNER JOIN FRAMEWORK.F_WF_WORKFLOW_INSTANCE wfins ON stins.WF_WORKFLOW_INSTANCE_ID = wfins.WF_WORKFLOW_INSTANCE_ID
    INNER JOIN FRAMEWORK.F_WF_WORKFLOW_DEF wfdef ON wfins.WF_WORKFLOW_DEF_ID = WFDEF.WF_WORKFLOW_DEF_ID
    INNER JOIN FRAMEWORK.F_WF_ACTION_TASK_DEF wftaskdef ON WFTASKDEF.WF_ACTION_TASK_DEF_ID = TASKINS.WF_ACTION_TASK_DEF_ID
WHERE
    taskins.WF_ACTION_TASK_STATUS_T_CD = 'TAKEDON'
    AND COALESCE(actins.START_TIME, sysdate) <= sysdate
    AND EXISTS (
        SELECT 1
        FROM FRAMEWORK.F_WF_ASSIGNMENT assgn
        LEFT JOIN FRAMEWORK.F_LOGIN_GROUP_LOGIN_REL rel ON assgn.ASSIGNED_OWNER_REF_ID = rel.LOGIN_GROUP_ID
            AND assgn.WF_ASSIGNED_TYPE_CD = 'LOGINGROUP'
        WHERE
            assgn.WF_ASSIGNMENT_TYPE_CD = 'TASKINBOX'
            AND (
                (assgn.WF_ASSIGNED_TYPE_CD = 'LOGINGROUP' AND rel.LOGIN_ID = :loginId)
                OR (assgn.WF_ASSIGNED_TYPE_CD = 'LOGIN' AND assgn.ASSIGNED_OWNER_REF_ID = :loginId)
                OR (assgn.WF_ASSIGNED_TYPE_CD = 'WFOWNER' AND wfins.OWNER_LOGIN_ID = :loginId)
            )
            AND (
                (COALESCE(assgn.IS_DEF_ASSIGNMENT, 0) = 1 AND assgn.ASSIGNMENT_OWNER_REF_ID = actins.WF_ACTION_DEF_ID)
                OR (COALESCE(assgn.IS_DEF_ASSIGNMENT, 0) = 0 AND assgn.ASSIGNMENT_OWNER_REF_ID = actins.WF_ACTION_INSTANCE_ID)
            )
            AND COALESCE(assgn.DENY, 0) = 0
    )
    AND NOT EXISTS (
        SELECT 1
        FROM FRAMEWORK.F_WF_ASSIGNMENT assgn
        LEFT JOIN FRAMEWORK.F_LOGIN_GROUP_LOGIN_REL rel ON assgn.ASSIGNED_OWNER_REF_ID = rel.LOGIN_GROUP_ID
            AND assgn.WF_ASSIGNED_TYPE_CD = 'LOGINGROUP'
        WHERE
            assgn.WF_ASSIGNMENT_TYPE_CD = 'TASKINBOX'
            AND (
                (assgn.WF_ASSIGNED_TYPE_CD = 'LOGINGROUP' AND rel.LOGIN_ID = :loginId)
                OR (assgn.WF_ASSIGNED_TYPE_CD = 'LOGIN' AND assgn.ASSIGNED_OWNER_REF_ID = :loginId)
                OR (assgn.WF_ASSIGNED_TYPE_CD = 'WFOWNER' AND wfins.OWNER_LOGIN_ID = :loginId)
            )
            AND (
                (COALESCE(assgn.IS_DEF_ASSIGNMENT, 0) = 1 AND assgn.ASSIGNMENT_OWNER_REF_ID = actins.WF_ACTION_DEF_ID)
                OR (COALESCE(assgn.IS_DEF_ASSIGNMENT, 0) = 0 AND assgn.ASSIGNMENT_OWNER_REF_ID = actins.WF_ACTION_INSTANCE_ID)
            )
            AND COALESCE(assgn.DENY, 0) = 1
    )";

        public static string GetSuspendedInbox = @"SELECT
    WFINS.WF_WORKFLOW_INSTANCE_ID as WF_INS_ID,
    WFDEF.NAME || ' - ' || ACTDEF.DESCRIPTION as NAME,
    ACTINS.START_TIME AS WF_DATE,
    WFTASKDEF.TASK_SCREEN as TASK_SCREEN,
    (SELECT NAME_SURNAME FROM DT_WORKFLOW.DP_HR_USERS dhr WHERE dhr.F_LOGIN_ID = wfins.OWNER_LOGIN_ID ) AS WF_OWNER,
    DT_WORKFLOW.DIGIFLOWPACKACE.GET_LAST_MODIFIED_BY (WFINS.WF_WORKFLOW_INSTANCE_ID) as WF_LAST_MODIFIED_BY
FROM
    FRAMEWORK.F_WF_ACTION_TASK_INSTANCE taskins
    INNER JOIN FRAMEWORK.F_WF_ACTION_INSTANCE actins ON taskins.WF_ACTION_TASK_INSTANCE_ID = actins.WF_ACTION_INSTANCE_ID
    INNER JOIN FRAMEWORK.F_WF_ACTION_DEF actdef ON ACTINS.WF_ACTION_DEF_ID = ACTDEF.WF_ACTION_DEF_ID
    INNER JOIN FRAMEWORK.F_WF_STATE_INSTANCE stins ON actins.WF_STATE_INSTANCE_ID = stins.WF_STATE_INSTANCE_ID
    INNER JOIN FRAMEWORK.F_WF_WORKFLOW_INSTANCE wfins ON stins.WF_WORKFLOW_INSTANCE_ID = wfins.WF_WORKFLOW_INSTANCE_ID
    INNER JOIN FRAMEWORK.F_WF_WORKFLOW_DEF wfdef ON wfins.WF_WORKFLOW_DEF_ID = WFDEF.WF_WORKFLOW_DEF_ID
    INNER JOIN FRAMEWORK.F_WF_ACTION_TASK_DEF wftaskdef ON WFTASKDEF.WF_ACTION_TASK_DEF_ID = TASKINS.WF_ACTION_TASK_DEF_ID
WHERE
    taskins.WF_ACTION_TASK_STATUS_T_CD = 'INBOX'
    AND COALESCE(actins.START_TIME, sysdate) >= sysdate
    AND EXISTS (
        SELECT 1
        FROM FRAMEWORK.F_WF_ASSIGNMENT assgn
        LEFT JOIN FRAMEWORK.F_LOGIN_GROUP_LOGIN_REL rel ON assgn.ASSIGNED_OWNER_REF_ID = rel.LOGIN_GROUP_ID
            AND assgn.WF_ASSIGNED_TYPE_CD = 'LOGINGROUP'
        WHERE
            assgn.WF_ASSIGNMENT_TYPE_CD = 'TASKINBOX'
            AND (
                (assgn.WF_ASSIGNED_TYPE_CD = 'LOGINGROUP' AND rel.LOGIN_ID = :loginId)
                OR (assgn.WF_ASSIGNED_TYPE_CD = 'LOGIN' AND assgn.ASSIGNED_OWNER_REF_ID = :loginId)
                OR (assgn.WF_ASSIGNED_TYPE_CD = 'WFOWNER' AND wfins.OWNER_LOGIN_ID = :loginId)
            )
            AND (
                (COALESCE(assgn.IS_DEF_ASSIGNMENT, 0) = 1 AND assgn.ASSIGNMENT_OWNER_REF_ID = actins.WF_ACTION_DEF_ID)
                OR (COALESCE(assgn.IS_DEF_ASSIGNMENT, 0) = 0 AND assgn.ASSIGNMENT_OWNER_REF_ID = actins.WF_ACTION_INSTANCE_ID)
            )
            AND COALESCE(assgn.DENY, 0) = 0
    )
    AND NOT EXISTS (
        SELECT 1
        FROM FRAMEWORK.F_WF_ASSIGNMENT assgn
        LEFT JOIN FRAMEWORK.F_LOGIN_GROUP_LOGIN_REL rel ON assgn.ASSIGNED_OWNER_REF_ID = rel.LOGIN_GROUP_ID
            AND assgn.WF_ASSIGNED_TYPE_CD = 'LOGINGROUP'
        WHERE
            assgn.WF_ASSIGNMENT_TYPE_CD = 'TASKINBOX'
            AND (
                (assgn.WF_ASSIGNED_TYPE_CD = 'LOGINGROUP' AND rel.LOGIN_ID = :loginId)
                OR (assgn.WF_ASSIGNED_TYPE_CD = 'LOGIN' AND assgn.ASSIGNED_OWNER_REF_ID = :loginId)
                OR (assgn.WF_ASSIGNED_TYPE_CD = 'WFOWNER' AND wfins.OWNER_LOGIN_ID = :loginId)
            )
            AND (
                (COALESCE(assgn.IS_DEF_ASSIGNMENT, 0) = 1 AND assgn.ASSIGNMENT_OWNER_REF_ID = actins.WF_ACTION_DEF_ID)
                OR (COALESCE(assgn.IS_DEF_ASSIGNMENT, 0) = 0 AND assgn.ASSIGNMENT_OWNER_REF_ID = actins.WF_ACTION_INSTANCE_ID)
            )
            AND COALESCE(assgn.DENY, 0) = 1
    )";
    }
}
