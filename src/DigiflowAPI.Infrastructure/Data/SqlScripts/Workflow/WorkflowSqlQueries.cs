﻿using Digiturk.Workflow.Digiflow.DataAccessLayer;
using System.Data;

namespace DigiflowAPI.Infrastructure.Data.SqlScripts.Workflow
{
    public static class WorkflowSqlQueries
    {
        public const string GetWorkflowSelectDto = @"
            SELECT FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID as VALUE, FRAMEWORK.F_WF_WORKFLOW_DEF.NAME as FLOWNAME, FRAMEWORK.F_WF_WORKFLOW_DEF.DESCRIPTION as FLOWNAMEEN
            FROM FRAMEWORK.F_WF_WORKFLOW_DEF WHERE FRAMEWORK.F_WF_WORKFLOW_DEF.VERSION_NUMBER=10
            ORDER BY NLSSORT(FRAMEWORK.F_WF_WORKFLOW_DEF.NAME, 'NLS_SORT=XTURKISH')";
        
        public const string GetAdminWorkflowDto = @"
            SELECT distinct FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID as WF_DEF_ID, NAME, DESCRIPTION FROM FRAMEWORK.F_WF_WORKFLOW_DEF
LEFT JOIN DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS  ON DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.WF_DEF_ID=FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID
WHERE (DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.ADMIN_ID=:ADMIN_ID or :ISADMIN=1)
AND FRAMEWORK.F_WF_WORKFLOW_DEF.VERSION_NUMBER>0 order by FRAMEWORK.F_WF_WORKFLOW_DEF.NAME";

        public const string GetActiveDelegateWithRecursive = @"
            Select PATH
            from(
                SELECT distinct WF_DELEGATION_ID,
                            WORKFLOW_DEF_ID,
                            LEVEL - 1 PATHLEN,
                            SYS_CONNECT_BY_PATH (DELEGATE_REF_ID, '/') || '/' ||  DELEGATION_OWNER_REF_ID || '/' PATH,
                            DELEGATION_START_DATE,
                            DELEGATION_END_DATE,
                            DELEGATION_OWNER_REF_ID,
                            DELEGATE_REF_ID,
                            LEVEL AS DEGER
                        FROM framework.f_wf_delegation a
                CONNECT BY NOCYCLE PRIOR DELEGATION_OWNER_REF_ID = DELEGATE_REF_ID  AND  WORKFLOW_DEF_ID=:WorkFlowDefId AND sysdate BETWEEN DELEGATION_START_DATE AND DELEGATION_END_DATE
                START WITH  (sysdate BETWEEN TO_DATE (DELEGATION_START_DATE,'DD.MM.YYYY HH24:MI:SS') AND TO_DATE (DELEGATION_END_DATE,'DD.MM.YYYY HH24:MI:SS')))
            Where WORKFLOW_DEF_ID=:WorkFlowDefId AND DELEGATION_OWNER_REF_ID = :AssignToLoginId and (sysdate BetWeen DELEGATION_START_DATE and DELEGATION_END_DATE)
        ";
        public const string GetActiveDelegateWithRecursiveDateValidation = @"
            SELECT PATH AS PT
            FROM (
                SELECT DISTINCT
                     WF_DELEGATION_ID,
                     WORKFLOW_DEF_ID,
                     LEVEL - 1 PATHLEN,
                     SYS_CONNECT_BY_PATH(DELEGATE_REF_ID, '/') || '/' || DELEGATION_OWNER_REF_ID || '/' PATH,
                     DELEGATION_START_DATE,
                     DELEGATION_END_DATE,
                     DELEGATION_OWNER_REF_ID,
                     DELEGATE_REF_ID,
                     LEVEL AS DEGER
                FROM FRAMEWORK.F_WF_DELEGATION A
                CONNECT BY NOCYCLE PRIOR A.DELEGATION_OWNER_REF_ID = A.DELEGATE_REF_ID 
                AND WORKFLOW_DEF_ID = :I_WORKFLOW_DEF_ID  
                AND (
                    (TO_DATE(:I_DELEGATION_START_DATE, 'DD/MM/YYYY') BETWEEN DELEGATION_START_DATE AND DELEGATION_END_DATE)
                    OR (TO_DATE(:I_DELEGATION_END_DATE, 'DD/MM/YYYY') BETWEEN DELEGATION_START_DATE AND DELEGATION_END_DATE)
                    OR (TO_DATE(:I_DELEGATION_START_DATE, 'DD/MM/YYYY') < DELEGATION_START_DATE 
                        AND TO_DATE(:I_DELEGATION_END_DATE, 'DD/MM/YYYY') > DELEGATION_END_DATE)
                )
                START WITH WF_DELEGATION_ID >= (
                    SELECT MAX(WF_DELEGATION_ID) 
                    FROM FRAMEWORK.F_WF_DELEGATION
                    WHERE DELEGATION_OWNER_REF_ID = :I_DELEGATION_OWNER_REF_ID 
                    AND WORKFLOW_DEF_ID = :I_WORKFLOW_DEF_ID 
                    AND (
                        (TO_DATE(:I_DELEGATION_START_DATE, 'DD/MM/YYYY') BETWEEN DELEGATION_START_DATE AND DELEGATION_END_DATE)
                        OR (TO_DATE(:I_DELEGATION_END_DATE, 'DD/MM/YYYY') BETWEEN DELEGATION_START_DATE AND DELEGATION_END_DATE)
                    )
                )
            ) 
            WHERE WORKFLOW_DEF_ID = :I_WORKFLOW_DEF_ID 
            AND DELEGATION_OWNER_REF_ID = :I_DELEGATION_OWNER_REF_ID 
            AND (
                (TO_DATE(:I_DELEGATION_START_DATE, 'DD/MM/YYYY') BETWEEN DELEGATION_START_DATE AND DELEGATION_END_DATE)
                OR (TO_DATE(:I_DELEGATION_END_DATE, 'DD/MM/YYYY') BETWEEN DELEGATION_START_DATE AND DELEGATION_END_DATE)
                OR (TO_DATE(:I_DELEGATION_START_DATE, 'DD/MM/YYYY') < DELEGATION_START_DATE 
                    AND TO_DATE(:I_DELEGATION_END_DATE, 'DD/MM/YYYY') > DELEGATION_END_DATE)
            )
            AND ROWNUM = 1
            ORDER BY WF_DELEGATION_ID DESC
        ";

        public const string GetDelegationOwnCheck = @"
            SELECT *
            FROM FRAMEWORK.F_WF_DELEGATION wfdelegation
            WHERE WFDELEGATION.DELEGATION_OWNER_REF_ID = :loginValue
              AND WFDELEGATION.WORKFLOW_DEF_ID = :WfdefId
              AND (
                (TO_DATE(:MyDelegationStartDate, 'DD/MM/YYYY') > WFDELEGATION.DELEGATION_START_DATE
                 OR TO_DATE(:MyDelegationEndDate, 'DD/MM/YYYY') > WFDELEGATION.DELEGATION_START_DATE)
                AND 
                (TO_DATE(:MyDelegationStartDate, 'DD/MM/YYYY') < WFDELEGATION.DELEGATION_END_DATE
                 OR TO_DATE(:MyDelegationEndDate, 'DD/MM/YYYY') < WFDELEGATION.DELEGATION_END_DATE)
              )
              AND WFDELEGATION.DELEGATION_START_DATE <> WFDELEGATION.DELEGATION_END_DATE";

        public const string GetWorkflowInstance = @"
            SELECT * FROM FRAMEWORK.F_WF_WORKFLOW_INSTANCE 
            WHERE FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID = :wfInstanceId 
            ORDER BY FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID DESC
        ";

        public const string GetWorkflowAdmins = @"
            SELECT DT_WORKFLOW.VW_WORKFLOW_ADMINS.* FROM DT_WORKFLOW.VW_WORKFLOW_ADMINS 
            WHERE WF_WORKFLOW_DEF_ID=:WfWorkflowDefId ORDER BY NAME_SURNAME ASC
        ";

        public const string GetWorkflowDefinitionIdByPageName = @"
            SELECT DT_WORKFLOW.VW_WORKFLOW_ADMINS.* FROM DT_WORKFLOW.VW_WORKFLOW_ADMINS 
            INNER JOIN DT_WORKFLOW.WF_XML_DEFINITION ON DT_WORKFLOW.WF_XML_DEFINITION.VALUE = DT_WORKFLOW.VW_WORKFLOW_ADMINS.WF_WORKFLOW_DEF_ID
            WHERE DT_WORKFLOW.WF_XML_DEFINITION.FLOWNAME=:PageName
        ";

        public const string GetWorkflowDefinitionIdByValue = @"
            SELECT DT_WORKFLOW.VW_WORKFLOW_ADMINS.* FROM DT_WORKFLOW.VW_WORKFLOW_ADMINS 
            INNER JOIN DT_WORKFLOW.WF_XML_DEFINITION ON DT_WORKFLOW.WF_XML_DEFINITION.VALUE = DT_WORKFLOW.VW_WORKFLOW_ADMINS.WF_WORKFLOW_DEF_ID
            WHERE DT_WORKFLOW.WF_XML_DEFINITION.VALUE=:Value
        ";

        public const string GetWorkFlowHistory = @"
            SELECT
                CASE WHEN DT_WORKFLOW.VW_WORKFLOW_HISTORY.WF_WORKFLOW_HISTORY_TYPE_CD = 'SENDTOCOMMENT' THEN 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.COMMENTS || ' <br> (' || rtrim (xmlagg (xmlelement (e, 'To: ' || NAME_SURNAME || ',')).extract ('//text()'), ',') || ')'
                ELSE DT_WORKFLOW.VW_WORKFLOW_HISTORY.COMMENTS END COMMENTS,
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.*
            FROM DT_WORKFLOW.VW_WORKFLOW_HISTORY,
                (SELECT DISTINCT U.NAME_SURNAME, H.WF_HISTORY_ID
                    FROM FRAMEWORK.F_WF_ASSIGNMENT A, FRAMEWORK.F_WF_WORKFLOW_HISTORY  H , DT_WORKFLOW.DP_HR_USERS U
                    WHERE A.ASSIGNMENT_OWNER_REF_ID = H.WF_ACTION_INSTANCE_ID AND 
                    A.WF_ASSIGNMENT_TYPE_CD = 'TASKCOMMENT' 
                    AND U.F_LOGIN_ID = A.ASSIGNED_OWNER_REF_ID
                    AND H.WF_WORKFLOW_HISTORY_TYPE_CD = 'SENDTOCOMMENT'
                    AND H.WF_WORKFLOW_INSTANCE_ID = :WfInstanceId ORDER BY U.NAME_SURNAME ASC
                ) tbl1 
            WHERE DT_WORKFLOW.VW_WORKFLOW_HISTORY.WF_WORKFLOW_INSTANCE_ID = :WfInstanceId
            AND tbl1.WF_HISTORY_ID(+) = DT_WORKFLOW.VW_WORKFLOW_HISTORY.WF_HISTORY_ID                     
            GROUP BY DT_WORKFLOW.VW_WORKFLOW_HISTORY.ACTION, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.ACTION_ENG, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.ACTION_LOGIN_ID, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.ASSIGNED_LOGIN_ID, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.COLORS, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.COMMENTS, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.DATES, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.STATE, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.STATEDESC, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.USERS, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.WF_DEF_DESC, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.WF_DEF_NAME,
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.WF_HISTORY_ID, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.WF_WORKFLOW_DEF_ID, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.WF_WORKFLOW_HISTORY_TYPE_CD, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.WF_WORKFLOW_INSTANCE_ID
            ORDER BY DATES ASC
        ";

        public const string GetHistory = @"
            SELECT
                CASE WHEN DT_WORKFLOW.VW_WORKFLOW_HISTORY.WF_WORKFLOW_HISTORY_TYPE_CD = 'SENDTOCOMMENT' THEN 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.COMMENTS || ' <br> (' || rtrim (xmlagg (xmlelement (e, 'To: ' || NAME_SURNAME || ',')).extract ('//text()'), ',') || ')'
                ELSE DT_WORKFLOW.VW_WORKFLOW_HISTORY.COMMENTS END COMMENTS,
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.*
            FROM DT_WORKFLOW.VW_WORKFLOW_HISTORY,
                (SELECT DISTINCT U.NAME_SURNAME, H.WF_HISTORY_ID
                    FROM FRAMEWORK.F_WF_ASSIGNMENT A, FRAMEWORK.F_WF_WORKFLOW_HISTORY  H , DT_WORKFLOW.DP_HR_USERS U
                    WHERE A.ASSIGNMENT_OWNER_REF_ID = H.WF_ACTION_INSTANCE_ID AND 
                    A.WF_ASSIGNMENT_TYPE_CD = 'TASKCOMMENT' 
                    AND U.F_LOGIN_ID = A.ASSIGNED_OWNER_REF_ID
                    AND H.WF_WORKFLOW_HISTORY_TYPE_CD = 'SENDTOCOMMENT'
                    AND H.WF_HISTORY_ID = :WfHistoryId ORDER BY U.NAME_SURNAME ASC
                ) tbl1 
            WHERE DT_WORKFLOW.VW_WORKFLOW_HISTORY.WF_HISTORY_ID = :WfHistoryId
            AND tbl1.WF_HISTORY_ID(+) = DT_WORKFLOW.VW_WORKFLOW_HISTORY.WF_HISTORY_ID                     
            GROUP BY DT_WORKFLOW.VW_WORKFLOW_HISTORY.ACTION, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.ACTION_ENG, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.ACTION_LOGIN_ID, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.ASSIGNED_LOGIN_ID, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.COLORS, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.COMMENTS, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.DATES, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.STATE, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.STATEDESC, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.USERS, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.WF_DEF_DESC, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.WF_DEF_NAME,
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.WF_HISTORY_ID, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.WF_WORKFLOW_DEF_ID, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.WF_WORKFLOW_HISTORY_TYPE_CD, 
                DT_WORKFLOW.VW_WORKFLOW_HISTORY.WF_WORKFLOW_INSTANCE_ID
            ORDER BY DATES ASC
        ";

        public const string GetDelegatedUserInfo = @"
            SELECT
                (DOWNR.NAME_SURNAME || ' delegesi ' || DRF.NAME_SURNAME) as Delegation
            FROM FRAMEWORK.F_WF_DELEGATION D
            INNER JOIN DT_WORKFLOW.DP_HR_USERS DOWNR ON D.DELEGATION_OWNER_REF_ID = DOWNR.F_LOGIN_ID
            INNER JOIN DT_WORKFLOW.DP_HR_USERS DRF ON D.DELEGATE_REF_ID = DRF.F_LOGIN_ID
            WHERE D.WORKFLOW_DEF_ID = :WfDefId and sysdate between D.DELEGATION_START_DATE and D.DELEGATION_END_DATE
            AND D.DELEGATION_OWNER_REF_ID = :AssignedLoginId";
    }
}