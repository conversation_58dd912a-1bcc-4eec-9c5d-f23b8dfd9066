﻿namespace DigiflowAPI.Infrastructure.Data.SqlScripts.Inbox
{
    public static class InboxSqlQueries
    {
        public const string GetInboxQuery = @"
                        SELECT 
    DT_WORKFLOW.VW_DIGIFLOW_INBOX.FLOWNAME,
    DT_WORKFLOW.VW_DIGIFLOW_INBOX.FLOWDESC,
    DT_WORKFLOW.WF_XML_DEFINITION.ROUTE,
    DT_WORKFLOW.VW_DIGIFLOW_INBOX.STATENAME,
    DT_WORKFLOW.VW_DIGIFLOW_INBOX.STATEDESC,
    DT_WORKFLOW.VW_DIGIFLOW_INBOX.WFINSID,
    DT_WORKFLOW.VW_DIGIFLOW_INBOX.WFINSTANCEDEF,
    DT_WORKFLOW.VW_DIGIFLOW_INBOX.WFOWNER,
    DT_WORKFLOW.DP_HR_DEPT_NAME_ENG (DT_WORKFLOW.VW_DIGIFLOW_INBOX.OWNER_LOGIN_ID, 2) AS BOLUM_EN,
    DT_WORKFLOW.DP_HR_DEPT_NAME (DT_WORKFLOW.VW_DIGIFLOW_INBOX.OWNER_LOGIN_ID, 2) AS BOLUM,
    DT_WORKFLOW.VW_DIGIFLOW_INBOX.WF_WORKFLOW_DEF_ID,
    DT_WORKFLOW.VW_DIGIFLOW_INBOX.LASTLOGINID,
    (SELECT DT_WORKFLOW.DP_HR_USERS.E_MAIL FROM DT_WORKFLOW.DP_HR_USERS WHERE DT_WORKFLOW.DP_HR_USERS.F_LOGIN_ID = DT_WORKFLOW.VW_DIGIFLOW_INBOX.LASTLOGINID) as LASTLOGINEMAIL,
    DT_WORKFLOW.VW_DIGIFLOW_INBOX.WFLASTMODIFIEDBY,
    DT_WORKFLOW.VW_DIGIFLOW_INBOX.WFLASTMODIFIEDBY_NOM,
    DT_WORKFLOW.VW_WORKFLOW_DETAIL_INFO.DETAIL,
    DT_WORKFLOW.VW_WORKFLOW_DETAIL_INFO.AMOUNT,
    DT_WORKFLOW.VW_WORKFLOW_DETAIL_INFO.CURRENCY,
    DT_WORKFLOW.VW_WORKFLOW_DETAIL_INFO.AMOUNTTL,
    (DT_WORKFLOW.VW_DIGIFLOW_INBOX.TASK_SCREEN
    || '?LoginId='
    || :loginValue
    || '&'
    || 'wfInstanceId='
    || DT_WORKFLOW.VW_DIGIFLOW_INBOX.WFINSID) AS WFINSTANCELINK,
    REPLACE(
        REPLACE(
            REPLACE(
                REPLACE(
                    REPLACE(DT_WORKFLOW.VW_DIGIFLOW_INBOX.TASK_SCREEN, 'WFPages/', ''),
                    'WfPages/', ''
                ),
                'wfpages/', ''
            ),
            'Wfpages/', ''
        ),
        '.aspx', '_Mobil.aspx'
    ) || '?LoginId='
    || :loginValue
    || '&'
    || 'wfInstanceId='
    || DT_WORKFLOW.VW_DIGIFLOW_INBOX.WFINSID AS MWFINSTANCELINK,
    wfDate,
    CASE
        WHEN TopluOnay.WF_WORKFLOW_ENTITY_VALUE IS NOT NULL
        AND DT_WORKFLOW.VW_WORKFLOW_DETAIL_INFO.AMOUNTTL <= TopluOnay.WF_WORKFLOW_ENTITY_VALUE THEN 'TRUE'
        WHEN TopluOnay.WF_WORKFLOW_ENTITY_VALUE IS NOT NULL
        AND TopluOnay.WF_WORKFLOW_ENTITY_VALUE = 0 THEN 'TRUE'
        WHEN DT_WORKFLOW.VW_WORKFLOW_DETAIL_INFO.WF_WORKFLOW_DEF_ID = 1312 THEN 'TRUE'
        ELSE 'FALSE'
    END AS TopluOnayDurum,
    TopluOnay.WF_WORKFLOW_ENTITY,
    TopluOnay.WF_WORKFLOW_ENTITY_VALUE
FROM 
    DT_WORKFLOW.VW_DIGIFLOW_INBOX
    JOIN DT_WORKFLOW.VW_WORKFLOW_DETAIL_INFO ON DT_WORKFLOW.VW_WORKFLOW_DETAIL_INFO.WF_WORKFLOW_DEF_ID = DT_WORKFLOW.VW_DIGIFLOW_INBOX.WF_WORKFLOW_DEF_ID
    AND DT_WORKFLOW.VW_WORKFLOW_DETAIL_INFO.WF_WORKFLOW_INSTANCE_ID = DT_WORKFLOW.VW_DIGIFLOW_INBOX.WFINSID
    JOIN DT_WORKFLOW.WF_XML_DEFINITION ON DT_WORKFLOW.VW_DIGIFLOW_INBOX.WF_WORKFLOW_DEF_ID = DT_WORKFLOW.WF_XML_DEFINITION.VALUE
    JOIN DT_WORKFLOW.WF_XML_DEFINITION ON DT_WORKFLOW.WF_XML_DEFINITION.VALUE = DT_WORKFLOW.VW_DIGIFLOW_INBOX.WF_WORKFLOW_DEF_ID 
    JOIN DT_WORKFLOW.VW_USER_INFORMATION ON DT_WORKFLOW.VW_USER_INFORMATION.LOGIN_ID = DT_WORKFLOW.VW_DIGIFLOW_INBOX.ATANAN
    LEFT JOIN (
        SELECT 
            LOGIN_ID,
            WF_WORKFLOW_DEF_ID,
            WF_WORKFLOW_ENTITY,
            WF_WORKFLOW_ENTITY_VALUE
        FROM 
            DT_WORKFLOW.WF_DF_MULTIPLE_APPROVAL_DEF
        WHERE 
            LOGIN_ID = :loginValue
    ) TopluOnay ON DT_WORKFLOW.VW_DIGIFLOW_INBOX.WF_WORKFLOW_DEF_ID = TopluOnay.WF_WORKFLOW_DEF_ID
WHERE 
    WF_WORKFLOW_STATUS_TYPE_CD = 'STARTED'
    AND DT_WORKFLOW.VW_DIGIFLOW_INBOX.WF_ACTION_STATUS_TYPE_CD <> 'BYPASSED'
    AND (DT_WORKFLOW.VW_DIGIFLOW_INBOX.WF_ACTION_TASK_STATUS_T_CD = 'INBOX' 
        OR DT_WORKFLOW.VW_DIGIFLOW_INBOX.WF_ACTION_TASK_STATUS_T_CD = 'SENDEDTOCOMMENT')
    AND DT_WORKFLOW.VW_DIGIFLOW_INBOX.START_TIME <= SYSDATE
    AND Atanan = :loginValue
    AND WF_ASSIGNMENT_TYPE_CD = 'TASKINBOX'
ORDER BY  DT_WORKFLOW.VW_DIGIFLOW_INBOX.WFINSID DESC";
        public const string GetManagerInbox = @"SELECT /*+ PARALLEL(16) */  *
                    FROM ( SELECT  REPLACE (COPYINSTANCELINK2, '00000', :LoginId) AS COPYINSTANCELINK,
                    REPLACE (WFINSTANCELINK2, '00000', :LoginId) AS WFINSTANCELINK,
                    REPLACE (MWFINSTANCELINK2, '00000', :LoginId) AS MWFINSTANCELINK,A.LOGINID AS ATANANID,
                    A1.*,
                    ROW_NUMBER() OVER (PARTITION BY a1.WFINSID
                    ORDER BY WFDATE DESC) AS rownumber
                    from TB_WORKFLOW_HISTORY_VIEW A1
                    left join dt_workflow.vw_assigneduser a on A.WFINSID= A1.WFINSID
                    where A1.WF_WORKFLOW_DEF_ID in (select WF_DEF_ID + 0
                    from DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS A2
                    WHERE A2.ADMIN_ID = :LoginId
                    AND A1.WF_WORKFLOW_DEF_ID = WF_DEF_ID)
                    AND (WF_WORKFLOW_STATUS_TYPE_CD = :WfStatusType OR '1' = '1')
                    AND (WF_ACTION_STATUS_TYPE_CD = :TaskStatus OR '3' = '3')
                    AND (LastAction = :LastAction OR '0' = '0')
                    AND (WF_LAST_ACTION = :WF_LAST_ACTION OR '2' = '2')
                    AND (:AYRILANSTR)
                    ORDER BY a1.WFINSID DESC)
                    WHERE rownumber = 1";
        public const string GetManagerInbox_Kontrol = @"SELECT /*+ PARALLEL(16) */  *
                    FROM ( SELECT  REPLACE (COPYINSTANCELINK2, '00000', :LoginId) AS COPYINSTANCELINK,
                    REPLACE (WFINSTANCELINK2, '00000', :LoginId) AS WFINSTANCELINK,
                    REPLACE (MWFINSTANCELINK2, '00000', :LoginId) AS MWFINSTANCELINK,A.LOGINID AS ATANANID,
                    A1.*,
                    ROW_NUMBER() OVER (PARTITION BY a1.WFINSID
                    ORDER BY WFDATE DESC) AS rownumber
                    from VW_WORKFLOW_HISTORY_VIEW_2 A1
                    left join dt_workflow.vw_assigneduser a on A.WFINSID= A1.WFINSID
                    where A1.WF_WORKFLOW_DEF_ID in (select WF_DEF_ID + 0
                    from DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS A2
                    WHERE A2.ADMIN_ID = :LoginId
                    AND A1.WF_WORKFLOW_DEF_ID = WF_DEF_ID)
                    AND (WF_WORKFLOW_STATUS_TYPE_CD = :WfStatusType OR '1' = '1')
                    AND (WF_ACTION_STATUS_TYPE_CD = :TaskStatus OR '3' = '3')
                    AND (LastAction = :LastAction OR '0' = '0')
                    AND (WF_LAST_ACTION = :WF_LAST_ACTION OR '2' = '2')
                    AND (:AYRILANSTR)
                    ORDER BY a1.WFINSID DESC)
                    WHERE rownumber = 1";
        public const string GetSystemAdminInbox = @"SELECT
        WFDEF.NAME as FLOWNAME,
        (CASE WHEN WFINS.WF_WORKFLOW_STATUS_TYPE_CD = 'COMPLETED'
        THEN     LASTSTATEDEF.NAME
           ELSE WFSTATEDEF.NAME
        END) as STATENAME,
        WFINS.WF_WORKFLOW_INSTANCE_ID AS wfInsId,
       (CASE WHEN WFINS.WF_WORKFLOW_STATUS_TYPE_CD = 'COMPLETED'
       THEN     WFDEF.NAME || ' - ' || LASTSTATEDEF.NAME
           ELSE WFDEF.NAME || ' - ' || WFSTATEDEF.NAME
        END)
          AS wfInstanceDef,
        DT_WORKFLOW.DIGIFLOWPACKACE.DP_HR_GETIR (1, WFINS.OWNER_LOGIN_ID) AS wfOwner,
       WFINS.WF_WORKFLOW_DEF_ID,
       DT_WORKFLOW.VW_ASSIGNEDUSER.ASSIGNEDNAMESURNAME,
       DT_WORKFLOW.VW_ASSIGNEDUSER.LOGINID AS ASSIGNEDUSERID,
        DT_WORKFLOW.DIGIFLOWPACKACE.GET_LAST_MODIFIED_BY (WfIns.WF_WORKFLOW_INSTANCE_ID)
          AS wfLastModifiedBy,
       (CASE WHEN WFINS.WF_WORKFLOW_STATUS_TYPE_CD = 'COMPLETED' THEN
       DT_WORKFLOW.VW_WF_LAST_TASK_SCREEN.TASK_SCREEN
        || '?LoginId='
        || :LoginId
        || '&'
        || 'wfInstanceId='
        || WFINS.WF_WORKFLOW_INSTANCE_ID
        Else
       ACTIONTASKDEF.TASK_SCREEN
        || '?LoginId='
        || :LoginId
        || '&'
        || 'wfInstanceId='
        || WFINS.WF_WORKFLOW_INSTANCE_ID
        End
        )
          AS wfInstanceLink,
          (Case When ACTIONINSTANCE.START_TIME Is Not Null Then
       ACTIONINSTANCE.START_TIME
       Else
        WFINS.END_TIME
       End) AS wfDate
  FROM FRAMEWORK.F_WF_WORKFLOW_INSTANCE WFINS
       LEFT JOIN FRAMEWORK.F_WF_STATE_INSTANCE WfStateIns
          ON WFINS.WF_CURRENT_STATE_ID = WFSTATEINS.WF_STATE_INSTANCE_ID
             AND WFINS.WF_WORKFLOW_INSTANCE_ID =
                    WFSTATEINS.WF_WORKFLOW_INSTANCE_ID
       LEFT JOIN FRAMEWORK.F_WF_ACTION_INSTANCE ActionInstance
          ON ActionInstance.WF_ACTION_INSTANCE_ID =
                WfStateIns.WF_CURRENT_ACTION_INSTANCE_ID
       LEFT JOIN FRAMEWORK.F_WF_ACTION_TASK_INSTANCE taskins
          ON ACTIONINSTANCE.WF_ACTION_INSTANCE_ID =
                TASKINS.WF_ACTION_TASK_INSTANCE_ID
       LEFT JOIN FRAMEWORK.F_WF_WORKFLOW_DEF WfDef
          ON WfDef.WF_WORKFLOW_DEF_ID = WFINS.WF_WORKFLOW_DEF_ID
       LEFT JOIN FRAMEWORK.F_WF_STATE_DEF WfStateDef
          ON WFSTATEDEF.WF_STATE_DEF_ID = WFSTATEINS.WF_STATE_DEF_ID
       LEFT JOIN FRAMEWORK.F_WF_ACTION_DEF WfActionDef
          ON WFACTIONDEF.WF_ACTION_DEF_ID = ACTIONINSTANCE.WF_ACTION_DEF_ID
       LEFT JOIN FRAMEWORK.F_WF_ACTION_TASK_DEF ActionTaskDef
          ON ACTIONTASKDEF.WF_ACTION_TASK_DEF_ID =
                TASKINS.WF_ACTION_TASK_DEF_ID
       LEFT JOIN DT_WORKFLOW.F_WF_LAST_STATE_INSTANCE LastStateInsCross
          ON WFINS.WF_WORKFLOW_INSTANCE_ID =
                LastStateInsCross.WF_WORKFLOW_INSTANCE_ID
       LEFT JOIN FRAMEWORK.F_WF_STATE_INSTANCE LastStateInstance
          ON LastStateInstance.WF_STATE_INSTANCE_ID =
                LastStateInsCross.WF_STATE_INSTANCE_ID
       LEFT JOIN FRAMEWORK.F_WF_STATE_DEF LastStateDef
          ON LASTSTATEDEF.WF_STATE_DEF_ID =
                LASTSTATEINSTANCE.WF_STATE_DEF_ID
       LEFT JOIN DT_WORKFLOW.VW_WF_LAST_TASK_SCREEN On DT_WORKFLOW.VW_WF_LAST_TASK_SCREEN.WF_WORKFLOW_INSTANCE_ID=WFINS.WF_WORKFLOW_INSTANCE_ID
       LEFT JOIN DT_WORKFLOW.VW_ASSIGNEDUSER On DT_WORKFLOW.VW_ASSIGNEDUSER.WFINSID=WFINS.WF_WORKFLOW_INSTANCE_ID
       LEFT JOIN DT_WORKFLOW.VW_WF_WORKFLOW_LASTACTION On DT_WORKFLOW.VW_WF_WORKFLOW_LASTACTION.WF_WORKFLOW_INSTANCE_ID=WFINS.WF_WORKFLOW_INSTANCE_ID
 WHERE (WFINS.WF_WORKFLOW_STATUS_TYPE_CD = :WfStatusType or :WfStatusType='0') AND
        (ACTIONINSTANCE.WF_ACTION_STATUS_TYPE_CD=:TaskStatus or  :TaskStatus='0') AND
        (WFSTATEDEF.WF_STATE_TYPE_CD<>'START' or WFSTATEDEF.WF_STATE_TYPE_CD Is Null) AND
        (DT_WORKFLOW.VW_WF_WORKFLOW_LASTACTION.WF_WORKFLOW_HISTORY_TYPE_CD=:LastAction or :LastAction='0')
        Order By WFINS.WF_WORKFLOW_INSTANCE_ID Desc";
        public const string GetDelegeInboxSQL = @"SELECT     DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.FLOWDESC,
                                DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.FLOWNAME,
                                DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.STATEDESC AS STATENAME_EN,
                                DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.STATENAME,
                                DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.STATEDESC,
                                DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.WFINSID,
                                DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.NAME
                                 || ' - '
                                 || DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.DESCRIPTION AS WFINSTANCEDEF,
                                 DT_WORKFLOW.DIGIFLOWPACKACE.DP_HR_GETIR (
                                    1,
                                    DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.OWNER_LOGIN_ID)
                                    AS wfOwner,
                                DT_WORKFLOW.DP_HR_DEPT_NAME ( DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.OWNER_LOGIN_ID, 2) AS BOLUM,
                                DT_WORKFLOW.DP_HR_DEPT_NAME_ENG ( DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.OWNER_LOGIN_ID, 2) AS BOLUM_EN,
                                       DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.WF_WORKFLOW_DEF_ID,
                                 DT_WORKFLOW.DIGIFLOWPACKACE.GET_LAST_MODIFIED_BY (
                                    DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.WFINSID)
                                    AS wfLastModifiedBy,
                                 (   DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.TASK_SCREEN
                                  || '?LoginId='
                                  || :loginValue
                                  || '&'
                                  || 'wfInstanceId='
                                  || DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.WFINSID)
                                    AS wfInstanceLink,
                                    REPLACE (
                                  REPLACE(  REPLACE (DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.TASK_SCREEN,
                                             'WFPages/',
                                             ''),'WfPages/',''),
                                    '.aspx',
                                    '_Mobil.aspx')
                                 || '?LoginId='
                                 || :loginValue
                                 || '&'
                                 || 'wfInstanceId='
                                || DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.WFINSID
                                    AS MwfInstanceLink,
                                 DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.START_TIME AS wfDate,
                                 DT_WORKFLOW.VW_WORKFLOW_DETAIL_INFO.AMOUNTTL,
                                 DT_WORKFLOW.VW_WORKFLOW_DETAIL_INFO.AMOUNT,
                                 DT_WORKFLOW.VW_WORKFLOW_DETAIL_INFO.CURRENCY,
                                    case when TopluOnay.WF_WORKFLOW_ENTITY_VALUE IS NOT NULL AND DT_WORKFLOW.VW_WORKFLOW_DETAIL_INFO.AMOUNTTL <= TopluOnay.WF_WORKFLOW_ENTITY_VALUE   THEN
                                    'TRUE'
                                    WHEN TopluOnay.WF_WORKFLOW_ENTITY_VALUE IS NOT NULL AND TopluOnay.WF_WORKFLOW_ENTITY_VALUE = 0 THEN
                                    'TRUE'
                                     WHEN DT_WORKFLOW.VW_WORKFLOW_DETAIL_INFO.WF_WORKFLOW_DEF_ID = 1312 THEN
                                    'TRUE'
                                    ELSE
                                    'FALSE'
                                    END TopluOnayDurum,
                                    TopluOnay.WF_WORKFLOW_ENTITY,
                                    TopluOnay.WF_WORKFLOW_ENTITY_VALUE
                                FROM
                                DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX,
                                DT_WORKFLOW.VW_WORKFLOW_DETAIL_INFO,
                                     (Select DT_WORKFLOW.WF_DF_MULTIPLE_APPROVAL_DEF.LOGIN_ID, DT_WORKFLOW.WF_DF_MULTIPLE_APPROVAL_DEF.WF_WORKFLOW_DEF_ID,
                                   DT_WORKFLOW.WF_DF_MULTIPLE_APPROVAL_DEF.WF_WORKFLOW_ENTITY,
                                   DT_WORKFLOW.WF_DF_MULTIPLE_APPROVAL_DEF.WF_WORKFLOW_ENTITY_VALUE
                                   from
                                   DT_WORKFLOW.WF_DF_MULTIPLE_APPROVAL_DEF where
                                   DT_WORKFLOW.WF_DF_MULTIPLE_APPROVAL_DEF.LOGIN_ID= :loginValue) TopluOnay

                           WHERE DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.WF_ASSIGNMENT_TYPE_CD =  'TASKINBOX'
                                 AND (SYSDATE BETWEEN DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.DELEGATION_START_DATE  AND DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.DELEGATION_END_DATE)
                                 AND DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.WF_WORKFLOW_STATUS_TYPE_CD = 'STARTED'
                                 AND DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.DELEGATE_REF_ID =  :loginValue
                                 AND DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.WFINSID = DT_WORKFLOW.VW_WORKFLOW_DETAIL_INFO.WF_WORKFLOW_INSTANCE_ID
                                 AND DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.WF_WORKFLOW_DEF_ID  = TopluOnay.WF_WORKFLOW_DEF_ID  (+)
                        ORDER BY DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX.WFINSID DESC";
        public const string GetCommentInboxSQL = @"
            SELECT WFINS.ENTITY_REF_ID AS ENTITYREFID,
                                     WfDef.NAME AS FLOWNAME,
                                     WfDef.DESCRIPTION AS FLOWDESC,
                                     WFDEF.WF_WORKFLOW_DEF_ID,
                                     STATEDEF.NAME AS STATENAME,
                                     STATEDEF.DESCRIPTION AS STATEDESC,
                                     ACTIONINSTANCE.WF_ACTION_STATUS_TYPE_CD,
                                     WFINS.OWNER_LOGIN_ID,
                                     DT_WORKFLOW.DP_HR_DEPT_NAME (WFINS.OWNER_LOGIN_ID, 2) AS BOLUM,
                                     DT_WORKFLOW.DP_HR_DEPT_NAME_ENG (WFINS.OWNER_LOGIN_ID, 2) AS BOLUM_EN,
                                     TASKINS.WF_ACTION_TASK_INSTANCE_ID,
                                     TASKINS.WF_ACTION_TASK_STATUS_T_CD,
                                     ACTIONINSTANCE.START_TIME,
                                     WFINS.WF_WORKFLOW_INSTANCE_ID AS wfInsId,
                                     ACTIONTASKDEF.TASK_SCREEN,
                                     WFACTIONDEF.WF_ACTION_DEF_ID,
                                     (   ACTIONTASKDEF.TASK_SCREEN
                                      || '?LoginId='
                                      || :loginValue
                                      || '&'
                                      || 'wfInstanceId='
                                      || WFINS.WF_WORKFLOW_INSTANCE_ID)
                                        AS wfInstanceLink,
                                        (REPLACE ( REPLACE( REPLACE (ACTIONTASKDEF.TASK_SCREEN, 'WFPages/', ''),'WfPages/',''),
                                               '.aspx',
                                               '_Mobil.aspx')
                                      || '?LoginId='
                                      || :loginValue
                                      || '&'
                                      || 'wfInstanceId='
                                      || WFINS.WF_WORKFLOW_INSTANCE_ID)
                                        AS MwfInstanceLink,
                                     WFDEF.NAME || ' - ' || WFACTIONDEF.DESCRIPTION AS wfInstanceDef,
                                     DT_WORKFLOW.DIGIFLOWPACKACE.DP_HR_GETIR (1, WFINS.OWNER_LOGIN_ID)
                                        AS wfOwner,
                                     WFINS.WF_WORKFLOW_DEF_ID,
                                     DT_WORKFLOW.DIGIFLOWPACKACE.LAST_MODIFIED_BY (
                                        WFINS.WF_WORKFLOW_INSTANCE_ID)
                                        AS LastLoginId,
                                     DT_WORKFLOW.DIGIFLOWPACKACE.GET_LAST_MODIFIED_BY (
                                        WFINS.WF_WORKFLOW_INSTANCE_ID)
                                        AS wfLastModifiedBy,
                                     DT_WORKFLOW.DIGIFLOWPACKACE.DP_HR_GETIR (
                                        1,
                                        DT_WORKFLOW.DIGIFLOWPACKACE.LAST_MODIFIED_BY (
                                           WFINS.WF_WORKFLOW_INSTANCE_ID))
                                        AS wfLastModifiedBy_Nom,
                                     ACTIONINSTANCE.START_TIME AS wfDate,
                                     FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID AS AssignedUser,   '' as TOPLUONAYDURUM
                                FROM FRAMEWORK.F_WF_ACTION_TASK_INSTANCE taskins
                                     LEFT JOIN FRAMEWORK.F_WF_ACTION_INSTANCE ActionInstance
                                        ON ACTIONINSTANCE.WF_ACTION_INSTANCE_ID =
                                              TASKINS.WF_ACTION_TASK_INSTANCE_ID
                                     LEFT JOIN FRAMEWORK.F_WF_STATE_INSTANCE WfStateIns
                                        ON ActionInstance.WF_ACTION_INSTANCE_ID =
                                              WfStateIns.WF_CURRENT_ACTION_INSTANCE_ID
                                     INNER JOIN FRAMEWORK.F_WF_WORKFLOW_INSTANCE WfIns
                                        ON WFINS.WF_CURRENT_STATE_ID = WFSTATEINS.WF_STATE_INSTANCE_ID
                                           AND WFINS.WF_WORKFLOW_INSTANCE_ID =
                                                  WFSTATEINS.WF_WORKFLOW_INSTANCE_ID
                                     LEFT JOIN FRAMEWORK.F_WF_WORKFLOW_DEF WfDef
                                        ON WfDef.WF_WORKFLOW_DEF_ID = WFINS.WF_WORKFLOW_DEF_ID
                                     LEFT JOIN FRAMEWORK.F_WF_ACTION_DEF WfActionDef
                                        ON WFACTIONDEF.WF_ACTION_DEF_ID = ACTIONINSTANCE.WF_ACTION_DEF_ID
                                     LEFT JOIN FRAMEWORK.F_WF_ACTION_TASK_DEF ActionTaskDef
                                        ON ACTIONTASKDEF.WF_ACTION_TASK_DEF_ID =
                                              TASKINS.WF_ACTION_TASK_DEF_ID
                                     LEFT JOIN FRAMEWORK.F_WF_STATE_DEF STATEDEF
                                        ON STATEDEF.WF_STATE_DEF_ID = WfStateIns.WF_STATE_DEF_ID
                                     LEFT JOIN FRAMEWORK.F_WF_ASSIGNMENT
                                        ON ( (FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNMENT_OWNER_REF_ID =
                                                 taskins.WF_ACTION_TASK_INSTANCE_ID
                                              AND FRAMEWORK.F_WF_ASSIGNMENT.IS_DEF_ASSIGNMENT = 0)
                                            OR (FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNMENT_OWNER_REF_ID =
                                                   WFACTIONDEF.WF_ACTION_DEF_ID
                                                AND FRAMEWORK.F_WF_ASSIGNMENT.IS_DEF_ASSIGNMENT = 1))

                               WHERE     (ActionInstance.WF_ACTION_STATUS_TYPE_CD <> 'BYPASSED')
                                     --AND (TASKINS.WF_ACTION_TASK_STATUS_T_CD ='INBOX')
                                     AND FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_TYPE_CD = 'TASKCOMMENT'
                                     AND FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNED_TYPE_CD = 'LOGIN'
                                     AND ACTIONINSTANCE.START_TIME < SYSDATE
                                     AND FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID = :loginValue
                            ORDER BY WFINS.WF_WORKFLOW_INSTANCE_ID DESC
        ";
    }
}