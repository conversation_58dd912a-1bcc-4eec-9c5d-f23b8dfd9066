﻿using DigiflowAPI.Application.DTOs.HelperDTOs;
using DigiflowAPI.Application.Interfaces.DataAccess;
using DigiflowAPI.Domain.Entities.YYS;
using DigiflowAPI.Domain.Interfaces.Repositories;

namespace DigiflowAPI.Infrastructure.Data.Repositories
{
    public class LogicalGroupRepository(IOracleDataAccessRepositoryFactory repositoryFactory) : ILogicalGroupRepository
    {
        public async Task<IEnumerable<MemberInfoDto>> GetPersonelList(long logicalGroupId)
        {
            string sql = @"
                SELECT
                    m.FULLNAME,
                    m.EMAIL,
                    m.LOGIN_ID,
                    t.DESCRIPTION,
                    m.CONTENT
                FROM DT_WORKFLOW.YYS_LOGICAL_GROUPS g
                LEFT JOIN DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS m ON g.LOGICAL_GROUP_ID = m.LOGICAL_GROUP_ID
                LEFT JOIN DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBER_TYPES t ON m.LOGICAL_GROUP_MEMBER_TYPE_ID = t.LOGICAL_GROUP_MEMBER_TYPE_ID
                WHERE m.LOGIN_ID > -1 AND g.LOGICAL_GROUP_ID = :LogicalGroupId";

            var parameters = new Dictionary<string, object>
            {
                {"LogicalGroupId", logicalGroupId}
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteQueryAsync(sql, reader => new MemberInfoDto
            {
                Fullname = reader["FULLNAME"].ToString(),
                Email = reader["EMAIL"].ToString(),
                LoginId = Convert.ToInt32(reader["LOGIN_ID"]),
                Description = reader["DESCRIPTION"].ToString(),
                Content = reader["CONTENT"].ToString()
            }, parameters);
        }

        public async Task<bool> IsExistLogicalGroup(long logicalGroupId, long userId)
        {
            var personelList = await GetPersonelList(logicalGroupId);
            return logicalGroupId == -1 || personelList.Any(p => p.LoginId == userId);
        }

        public async Task<int> GetLogicalGroupID(string logicalGroupName)
        {
            string sql = @"SELECT ID FROM DT_WORKFLOW.WF_XML_LOGICAL_GROUP WHERE NAME = :LogicalGroupName";
            var parameters = new Dictionary<string, object>
            {
                {"LogicalGroupName", logicalGroupName}
            };
            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var result = await repository.ExecuteScalarAsync<int>(sql, parameters);

            if (result == 0)
            {
                throw new Exception($"Logical Group bulunamadı: {logicalGroupName}");
            }

            return result;
        }

        public async Task<bool> IsDefExistLogicalGroup(long logicalGroupId, long defId, string isAdmin)
        {
            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var parameters = new Dictionary<string, object>
            {
                {"GroupId", logicalGroupId.ToString()},
                {"IsAdmin", isAdmin.ToString()},
                {"DefId", defId.ToString()}
            };
            string sql = @"Select
                DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.FULLNAME,
                DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.EMAIL,
                DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGIN_ID,
                DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.DESCRIPTION,
                DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.CONTENT
                from DT_WORKFLOW.YYS_LOGICAL_GROUPS
                Left Join DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS On DT_WORKFLOW.YYS_LOGICAL_GROUPS.LOGICAL_GROUP_ID=DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGICAL_GROUP_ID
                Left Join DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBER_TYPES On DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBER_TYPES.LOGICAL_GROUP_MEMBER_TYPE_ID=DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGICAL_GROUP_MEMBER_TYPE_ID
                Left Join FRAMEWORK.F_WF_WORKFLOW_DEF On FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID=DT_WORKFLOW.YYS_LOGICAL_GROUPS.WF_DEF_ID
                where DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGIN_ID>-1
                and DT_WORKFLOW.YYS_LOGICAL_GROUPS.LOGICAL_GROUP_ID=:GroupId AND DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.CONTENT =:DefId AND DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.DESCRIPTION = :IsAdmin";
            IEnumerable<YYSLogicalGroupMember> members = await repository.ExecuteQueryAsync<YYSLogicalGroupMember>(sql, parameters);
            if (members.Any())
            {
                return true;
            }
            else return false;
        }
    }
}
