﻿using DigiflowAPI.Domain.Entities;
using DigiflowAPI.Application.Interfaces.Services;
using Microsoft.AspNetCore.Http;
using DigiflowAPI.Application.Interfaces.DataAccess;
using DigiflowAPI.Application.DTOs.Common;
using DigiflowAPI.Domain.Interfaces.Repositories;

namespace DigiflowAPI.Infrastructure.Data.Repositories
{
    public class UserRepository(IOracleDataAccessRepositoryFactory repositoryFactory, IGlobalHelpers globalHelpers, IHttpContextAccessor httpContextAccessor) : IUserRepository
    {
        public async Task<long> GetUserInfo(Dictionary<string, object> parameters)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");
            return await repository.ExecuteScalarAsync<long>("SELECT FRAMEWORK.F_LOGIN.LOGIN_ID FROM FRAMEWORK.F_LOGIN WHERE FRAMEWORK.F_LOGIN.DOMAIN_USER_NAME = :DomainUserName", parameters);
        }

        public async Task<DpHrDeps?> GetDepartmentById(decimal departmentId)
        {
            string sql = "SELECT * FROM DT_WORKFLOW.DP_HR_DEPS WHERE DT_WORKFLOW.DP_HR_DEPS.ID = :DepartmentId";
            var parameters = new Dictionary<string, object>
            {
                { "DepartmentId", departmentId }
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var departments = await repository.ExecuteQueryAsync<DpHrDeps>(sql, parameters);
            return departments.FirstOrDefault();
        }

        public async Task<IEnumerable<DpHrDeps>> GetDepartmentsAtLevel(long ustBolumId)
        {
            string sql = "SELECT * FROM DT_WORKFLOW.DP_HR_DEPS WHERE DT_WORKFLOW.DP_HR_DEPS.UST_BOLUM_ID = :UstBolumId";
            var parameters = new Dictionary<string, object>
            {
                { "UstBolumId", ustBolumId }
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteQueryAsync<DpHrDeps>(sql, parameters);
        }

        public async Task<long> GetUserDepartmentId(long userId)
        {
            string sql = "SELECT DT_WORKFLOW.VW_USER_INFORMATION.DEPT_ID FROM DT_WORKFLOW.VW_USER_INFORMATION WHERE DT_WORKFLOW.VW_USER_INFORMATION.LOGIN_ID = :UserId AND DT_WORKFLOW.VW_USER_INFORMATION.IS_DELETED = 'N' ORDER BY DT_WORKFLOW.VW_USER_INFORMATION.NAME_SURNAME";
            var parameters = new Dictionary<string, object>
            {
                { "UserId", userId }
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var result = await repository.ExecuteScalarAsync<long>(sql, parameters);
            return result;
        }

        public async Task<IEnumerable<VwUserInformation>> GetUsersByDepartmentId(long departmentId, bool excludeActiveUser)
        {
            string sql = "SELECT * FROM DT_WORKFLOW.VW_USER_INFORMATION WHERE DT_WORKFLOW.VW_USER_INFORMATION.DEPT_ID = :DepartmentId AND DT_WORKFLOW.VW_USER_INFORMATION.LOGIN_ID != :ExcludeUser AND DT_WORKFLOW.VW_USER_INFORMATION.IS_DELETED = 'N' ORDER BY DT_WORKFLOW.VW_USER_INFORMATION.NAME_SURNAME";
            var parameters = new Dictionary<string, object>
            {
                { "DepartmentId", departmentId },
                { "ExcludeUser", 0 }
            };

            if (excludeActiveUser)
            {
                var activeUserInfo = await GetActiveUserIdAsync();
                if (activeUserInfo != null)
                {
                    parameters["ExcludeUser"] = activeUserInfo;
                }
            }

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteQueryAsync<VwUserInformation>(sql, parameters);
        }

        private async Task<bool> IsManager(string username)
        {
            bool sonuc = false;
            var repository = repositoryFactory.Create("DefaultConnection");
            var parameters = new Dictionary<string, object>
            {
                { "Username", username },
            };

            string donen = await repository.ExecuteScalarAsync<string>("select IS_MANAGER from DT_WORKFLOW.dp_hr_users where USERNAME=:Username", parameters);
            if (donen == "Y")
            {
                sonuc = true;
            }
            return sonuc;
        }

        public async Task<IEnumerable<VwUserInformation>> GetForwardPersonel(string username)
        {
            string sql = "";
            var parameters = new Dictionary<string, object>
            {
                { "Username", username }
            };
            
            if (await IsManager(username))
            {
                sql = @"SELECT F_LOGIN_ID as LoginId, NAME_SURNAME as LoginName 
                        FROM DT_WORKFLOW.dp_hr_users 
                        WHERE DELETED <> 'Y' 
                        AND username IN (
                            SELECT MANAGER 
                            FROM DT_WORKFLOW.dp_hr_deps 
                            WHERE UST_BOLUM_ID = (
                                SELECT UST_DEP_ID 
                                FROM DT_WORKFLOW.VW_USER_INFORMATION_MANAGER 
                                WHERE username = :Username
                            ) 
                            AND title = (
                                SELECT TITLE 
                                FROM DT_WORKFLOW.dp_hr_deps 
                                WHERE manager = :Username 
                                AND BOLUM <> 'GM'
                            )
                        ) 
                        ORDER BY LoginName";
            }
            else
            {
                sql = @"SELECT F_LOGIN_ID as LoginId, NAME_SURNAME as LoginName 
                        FROM DT_WORKFLOW.dp_hr_users 
                        WHERE DELETED <> 'Y' 
                        AND DEPT_ID = (
                            SELECT dept_ID 
                            FROM DT_WORKFLOW.VW_USER_INFORMATION 
                            WHERE username = :Username
                        ) 
                        ORDER BY LoginName";
            }

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return (await repository.ExecuteQueryAsync(sql, u => new VwUserInformation
            {
                NameSurname = u["LoginName"].ToString(),
                LoginId = Convert.ToInt64(u["LoginId"].ToString()),
            }, parameters));
        }


        public async Task<IEnumerable<VwUserInformation?>> GetAllAsync()
        {
            string sql = @"SELECT * FROM DT_WORKFLOW.VW_USER_INFORMATION ORDER BY DT_WORKFLOW.VW_USER_INFORMATION.NAME_SURNAME";
            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteQueryAsync<VwUserInformation>(sql);
        }

        public async Task<VwUserInformation?> GetByIdAsync(long? id)
        {
            long userId = id == null ? await globalHelpers.GetUserId() : (long)id;
            string sql = @"SELECT * FROM DT_WORKFLOW.VW_USER_INFORMATION WHERE DT_WORKFLOW.VW_USER_INFORMATION.LOGIN_ID = :LoginId";
            var repository = repositoryFactory.Create("DT_WORKFLOW");

            var parameters = new Dictionary<string, object>
            {
                { "LoginId", userId }
            };

            return await repository.ExecuteSingleQueryAsync<VwUserInformation>(sql, parameters);
        }

        public async Task<VwUserInformation?> GetByUsernameAsync(string username)
        {
            string sql = @"SELECT * FROM DT_WORKFLOW.VW_USER_INFORMATION WHERE DT_WORKFLOW.VW_USER_INFORMATION.USERNAME = :Username";
            var repository = repositoryFactory.Create("DT_WORKFLOW");

            var parameters = new Dictionary<string, object>
            {
                { "Username", username }
            };

            return await repository.ExecuteSingleQueryAsync<VwUserInformation>(sql, parameters);
        }

        public async Task<IEnumerable<SelectOptionDto>> GetUsersByDepartmentIdAsync(long departmentId, bool excludeActiveUser = false)
        {
            string sql = @"SELECT * FROM DT_WORKFLOW.VW_USER_INFORMATION vui 
                           INNER JOIN DT_WORKFLOW.DP_HR_USERS dhu ON vui.LOGIN_ID = dhu.F_LOGIN_ID 
                           WHERE vui.DEPT_ID = :DepartmentId AND dhu.DELETED = 'N'
                           AND vui.LOGIN_ID != :ExcludeUser
                           ORDER BY vui.NAME_SURNAME ASC";

            var parameters = new Dictionary<string, object>
            {
                { "DepartmentId", departmentId },
                { "ExcludeUser", 0 }
            };

            if (excludeActiveUser)
            {
                var activeUserInfo = await GetActiveUserIdAsync();
                if (activeUserInfo != null)
                {
                    parameters["ExcludeUser"] = activeUserInfo;
                }
            }

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteQueryAsync(sql, reader => new SelectOptionDto
            {
                Value = reader["LOGIN_ID"].ToString(),
                Label = reader["NAME_SURNAME"].ToString(),
                LabelEn = reader["NAME_SURNAME"].ToString(),
            }, parameters);
        }

        public async Task<long> GetUserInfo()
        {
            var username = globalHelpers.GetUserName();
            var parts = username.Split('\\');
            //var domain = parts.Length > 1 ? parts[0] : Environment.MachineName;
            var name = parts.Length > 1 ? parts[1] : username;

            var domainUserName = name.ToUpper().Replace("İ", "I").Replace("Ö", "O").Replace("Ü", "U");
            var repository = repositoryFactory.Create("FrameworkConnection");

            var parameters = new Dictionary<string, object>
            {
                { "DomainUserName", domainUserName }
            };

            return await repository.ExecuteScalarAsync<long>("SELECT FRAMEWORK.F_LOGIN.LOGIN_ID FROM FRAMEWORK.F_LOGIN WHERE FRAMEWORK.F_LOGIN.DOMAIN_USER_NAME = :DomainUserName", parameters);
        }

        public string GetActiveUserName()
        {
            return globalHelpers.GetUserName();
        }

        public async Task<long> GetActiveUserIdAsync()
        {
            var loginIdHeader = httpContextAccessor.HttpContext.Request.Headers["X-Login-Id"].ToString();
            if (loginIdHeader == "0")
            {
                loginIdHeader = (await globalHelpers.GetUserId()).ToString();
            }

            var windowsUserId = globalHelpers.GetUserId();
            var isAdmin = globalHelpers.IsSystemAdmin();

            if (isAdmin && loginIdHeader != windowsUserId.ToString())
            {
                var userInfo = await GetUserDetail(Convert.ToInt64(loginIdHeader));
                return (long)userInfo.FLoginId;
            }

            return await globalHelpers.GetUserId();
        }
        public async Task<DpHrUsers?> GetUserDetail(long? userId = null)
        {
            var queryParams = new Dictionary<string, object>
                {
                    { ":LoginId", userId ?? await GetUserInfo() }
                };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var result = await repository.ExecuteQueryAsync<DpHrUsers>(
                @"SELECT * FROM DT_WORKFLOW.DP_HR_USERS WHERE DT_WORKFLOW.DP_HR_USERS.F_LOGIN_ID = :LoginId",
                queryParams
            );

            if (result == null || result.Count == 0)
            {
                return null;
            }

            return result.FirstOrDefault();
        }
    }
}
