﻿using DigiflowAPI.Application.DTOs;
using DigiflowAPI.Application.DTOs.Organization;
using DigiflowAPI.Application.DTOs.User;
using DigiflowAPI.Domain.Interfaces.Repositories;
using DigiflowAPI.Application.Interfaces.Services;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using DigiflowAPI.Domain.Entities;
using DigiflowAPI.Domain.Entities.Framework;
using Microsoft.AspNetCore.Http;
using DigiflowAPI.Application.Interfaces.DataAccess;
using DigiflowAPI.Application.DTOs.Common;

namespace DigiflowAPI.Infrastructure.Data.Repositories
{
    public class OrganizationRepository(IOracleDataAccessRepositoryFactory repositoryFactory, IHttpContextAccessor httpContextAccessor, IUserService userService) : IOrganizationRepository
    {
        public string GetDepartment(long managerID)
        {
            if (System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US")
            {
                return WfDataHelpers.GetDeptAdi(managerID, "DEPS_EN");
            }
            else
            {
                return WfDataHelpers.GetDeptAdi(managerID, "BOLUM");
            }
        }

        public async Task<IEnumerable<DpHrDeps>> GetDepartmentAsync(long? id)
        {
            string sql = @"
                SELECT * FROM DT_WORKFLOW.DP_HR_DEPS 
                WHERE DT_WORKFLOW.DP_HR_DEPS.ID = :BolumId 
                ORDER BY DT_WORKFLOW.DP_HR_DEPS.BOLUM ASC";

            var parameters = new Dictionary<string, object>
            {
                {"BolumId", id}
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteQueryAsync<DpHrDeps>(sql, parameters);
        }

        public async Task<IEnumerable<SelectOptionDto>> GetDepartmentSelectAsync(long? id)
        {
            string sql = @"
                SELECT
                    DT_WORKFLOW.DP_HR_DEPS.ID,
                    DT_WORKFLOW.DP_HR_DEPS.BOLUM,
                    DT_WORKFLOW.DP_HR_DEPS.DEPS_EN
                FROM DT_WORKFLOW.DP_HR_DEPS WHERE DT_WORKFLOW.DP_HR_DEPS.UST_BOLUM_ID = :UstBolumId 
                ORDER BY DT_WORKFLOW.DP_HR_DEPS.BOLUM ASC";

            var parameters = new Dictionary<string, object>
            {
                {"UstBolumId", id}
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteQueryAsync(sql, reader => new SelectOptionDto
            {
                Value = reader["ID"].ToString(),
                Label = reader["BOLUM"].ToString(),
                LabelEn = reader["DEPS_EN"].ToString(),
            }, parameters);
        }

        public async Task<DPHRUsersDto?> GetDPHRUserByIdAsync(long? userId = null)
        {
            userId ??= await userService.GetUserInfo();
            string sql = @"SELECT * FROM DT_WORKFLOW.DP_HR_USERS u WHERE F_LOGIN_ID = :UserId 
                            INNER JOIN DT_WORKFLOW.DEPS_PATH d ON u.";

            var parameters = new Dictionary<string, object>
            {
                { "UserId", userId }
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var result = await repository.ExecuteSingleQueryAsync(sql, reader => new DPHRUsersDto
            {
                DeptId = Convert.ToInt64(reader["DEPT_ID"]),
            }, parameters);

            return result;
        }

        public async Task<DepsPathSelectDto?> GetDPHRUserDepsPathByIdAsync(long? userId = null)
        {
            userId ??= await userService.GetUserInfo();
            string sql = @"SELECT * FROM DT_WORKFLOW.DP_HR_USERS u INNER JOIN DT_WORKFLOW.DEPS_PATH d ON u.DEPT_ID = d.ID WHERE u.F_LOGIN_ID = :UserId";

            var parameters = new Dictionary<string, object>
            {
                { "UserId", userId }
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteSingleQueryAsync(sql, reader => new DepsPathSelectDto
            {
                DepsEn = Convert.ToString(reader["DEPS_EN"]),
                DepsTr = Convert.ToString(reader["DEPS_TR"]),
                NameSurname = Convert.ToString(reader["NAME_SURNAME"]),
            }, parameters);
        }

        public async Task<OrganizationSchemaParamsDto?> GetOrganizationHierarchy(long? wfInstanceId=null)
        {

            if (!long.TryParse(httpContextAccessor.HttpContext?.Request.Headers["X-Login-Id"].ToString(), out long selectedUserId))
            {
                if (wfInstanceId == null)
                    selectedUserId = await userService.GetUserInfo();
                else
                {
                    var frameworkRepository = repositoryFactory.Create("FrameworkConnection");
                    var wfIns = await frameworkRepository.GetEntityAsync<FWfWorkflowInstance>(wfInstanceId.Value);
                    selectedUserId = wfIns.OwnerLoginId.Value;
                };
            }

            var organizationSchema = new OrganizationSchemaParamsDto();

            organizationSchema.DepsPath = await GetDPHRUserDepsPathByIdAsync(selectedUserId);

            long userDepartmentId = await userService.GetUserDepartmentId(selectedUserId);
            var departmentHierarchy = await GetDepartmentHierarchy(userDepartmentId);

            organizationSchema.Departments = ConvertToOrgTreeSelectOptions(await FetchDepartments(99));
            organizationSchema.SelectedDepartment = organizationSchema.Departments.FirstOrDefault(d => departmentHierarchy.Any(h => h.ToString() == d.Value));

            if (organizationSchema.SelectedDepartment != null)
            {
                organizationSchema.Divisions = ConvertToOrgTreeSelectOptions(await FetchDepartments(long.Parse(organizationSchema.SelectedDepartment.Value)));
                organizationSchema.SelectedDivision = organizationSchema.Divisions.FirstOrDefault(d => departmentHierarchy.Skip(1).Any(h => h.ToString() == d.Value));
            }

            if (organizationSchema.SelectedDivision != null)
            {
                organizationSchema.Units = ConvertToOrgTreeSelectOptions(await FetchDepartments(long.Parse(organizationSchema.SelectedDivision.Value)));
                organizationSchema.SelectedUnit = organizationSchema.Units.FirstOrDefault(d => departmentHierarchy.Skip(2).Any(h => h.ToString() == d.Value));
            }

            if (organizationSchema.SelectedUnit != null)
            {
                organizationSchema.Teams = ConvertToOrgTreeSelectOptions(await FetchDepartments(long.Parse(organizationSchema.SelectedUnit.Value)));
                organizationSchema.SelectedTeam = organizationSchema.Teams.FirstOrDefault(d => departmentHierarchy.Skip(3).Any(h => h.ToString() == d.Value));
            }

            if (organizationSchema.SelectedTeam != null)
            {
                organizationSchema.SubTeams = await GetSubTeamsRecursively(long.Parse(organizationSchema.SelectedTeam.Value));
                organizationSchema.SelectedSubTeams = new List<OrgTreeSelectOptionDto>();
                var currentLevel = organizationSchema.SubTeams;
                foreach (var subTeamId in departmentHierarchy.Skip(4))
                {
                    var selectedSubTeam = currentLevel.FirstOrDefault(st => st.Value == subTeamId.ToString());
                    if (selectedSubTeam != null)
                    {
                        organizationSchema.SelectedSubTeams.Add(selectedSubTeam);
                        currentLevel = selectedSubTeam.SubTeams;
                    }
                    else
                    {
                        break;
                    }
                }
            }

            var users = await userService.GetUsersByDepartmentId(userDepartmentId, true);
            organizationSchema.Users = ConvertToOrgTreeSelectOptions(users.Select(u => new SelectOptionDto
            {
                Value = u.LoginId.ToString(),
                Label = u.NameSurname,
                LabelEn = u.NameSurname,
            }));

            return organizationSchema;
        }

        public async Task<IEnumerable<DpHrDeps>> GetSubDepartments(long departmentId)
        {
            string sql = "SELECT * FROM DT_WORKFLOW.DP_HR_DEPS WHERE DT_WORKFLOW.DP_HR_DEPS.UST_BOLUM_ID = :DepartmentId";
            var parameters = new Dictionary<string, object>
            {
                { "DepartmentId", departmentId }
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteQueryAsync<DpHrDeps>(sql, parameters);
        }

        private async Task<List<DpHrDeps>> GetDepartmentHierarchy(long departmentId)
        {
            var hierarchy = new List<DpHrDeps>();
            var currentDepartment = await userService.GetDepartmentById(departmentId);

            while (currentDepartment != null && currentDepartment.Id != 99)
            {
                hierarchy.Add(currentDepartment);
                currentDepartment = currentDepartment.UstBolumId.HasValue
                    ? await userService.GetDepartmentById(currentDepartment.UstBolumId.Value)
                    : null;
            }

            hierarchy.Reverse();
            return hierarchy;
        }

        private async Task<List<OrgTreeSelectOptionDto>> FetchDepartments(long parentId)
        {
            string sql = "SELECT * FROM DT_WORKFLOW.DP_HR_DEPS WHERE DT_WORKFLOW.DP_HR_DEPS.UST_BOLUM_ID = :DepartmentId";
            var parameters = new Dictionary<string, object>
            {
                { "DepartmentId", parentId }
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteQueryAsync(sql, reader => new OrgTreeSelectOptionDto
            {
                Value = reader["ID"].ToString(),
                Label = reader["BOLUM"].ToString(),
                LabelEn = reader["DEPS_EN"].ToString(),
            }, parameters);
        }

        private List<OrgTreeSelectOptionDto> ConvertToOrgTreeSelectOptions(IEnumerable<SelectOptionDto> selectOptions)
        {
            return selectOptions.Select(o => new OrgTreeSelectOptionDto
            {
                Value = o.Value,
                Label = o.Label,
                LabelEn = o.LabelEn
            }).ToList();
        }

        private async Task<List<OrgTreeSelectOptionDto>> GetSubTeamsRecursively(long parentId)
        {
            var subTeams = await GetSubDepartments(parentId);
            var result = new List<OrgTreeSelectOptionDto>();

            foreach (var subTeam in subTeams)
            {
                var subTeamOption = new OrgTreeSelectOptionDto
                {
                    Value = subTeam.Id.ToString(),
                    Label = subTeam.Bolum,
                    LabelEn = subTeam.DepsEn
                };

                var subSubTeams = await GetSubTeamsRecursively(subTeam.Id);
                if (subSubTeams.Any())
                {
                    subTeamOption.SubTeams = subSubTeams;
                }

                result.Add(subTeamOption);
            }

            return result;
        }
    }
}
