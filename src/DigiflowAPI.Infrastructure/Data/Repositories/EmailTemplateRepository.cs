﻿using DigiflowAPI.Application.Interfaces.DataAccess;
using DigiflowAPI.Domain.Entities;
using DigiflowAPI.Domain.Interfaces.Repositories;

namespace DigiflowAPI.Infrastructure.Data.Repositories
{
    public class EmailTemplateRepository(IOracleDataAccessRepositoryFactory repositoryFactory) : IEmailTemplateRepository
    {
        public async Task<EmailTemplate?> GetTemplateByIdAsync(long templateId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            var parameters = new Dictionary<string, object>
            {
                {":TemplateId", templateId },
            };

            return await repository.ExecuteSingleQueryAsync<EmailTemplate>(@"
                SELECT * FROM FRAMEWORK.F_WF_MAIL_TEMPLATE
                WHERE WF_EMAIL_TEMPLATE_ID = :TemplateId
           ", parameters);
        }
    }

}
