using DigiflowAPI.Application.Interfaces.DataAccess;
using DigiflowAPI.Domain.Entities;
using DigiflowAPI.Domain.Interfaces.Repositories;

namespace DigiflowAPI.Infrastructure.Data.Repositories
{
    public class SlideRepository(IOracleDataAccessRepositoryFactory repositoryFactory) : ISlideRepository
    {
        public async Task<IEnumerable<HomeSlide>> GetAllSlidesAsync()
        {
            const string sql = @"
                SELECT ID, MENU_NAME_ID, SLIDE_NAME, SLIDE_IMAGE_PATH, THUMBNAIL_IMAGE_PATH,
                       SLIDE_CLICK_ACTION, SLIDE_TARGET_LINK, SLIDE_TARGET_CONTENT,
                       SLIDE_POPUP_WIDTH, SLIDE_POPUP_HEIGHT, ACTIVE, ORDER_NO,
                       VALID_DATE_START, VALID_DATE_END, CREATED, CREATED_BY,
                       LAST_UPDATED, LAST_UPDATED_BY, SLIDE_TARGET_HEADLINE, DELETED
                FROM DT_WORKFLOW.DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE
                WHERE DELETED = '0'
                ORDER BY ORDER_NO ASC, CREATED DESC";

            var repository = repositoryFactory.Create("DefaultConnection"); // Corrected typo "DefaultConnecstion"
            return await repository.ExecuteQueryAsync<HomeSlide>(sql);
        }

        public async Task<IEnumerable<HomeSlide>> GetActiveSlidesAsync()
        {
            const string sql = @"
                SELECT ID, MENU_NAME_ID, SLIDE_NAME, SLIDE_IMAGE_PATH, THUMBNAIL_IMAGE_PATH,
                       SLIDE_CLICK_ACTION, SLIDE_TARGET_LINK, SLIDE_TARGET_CONTENT,
                       SLIDE_POPUP_WIDTH, SLIDE_POPUP_HEIGHT, ACTIVE, ORDER_NO,
                       VALID_DATE_START, VALID_DATE_END, CREATED, CREATED_BY,
                       LAST_UPDATED, LAST_UPDATED_BY, SLIDE_TARGET_HEADLINE, DELETED
                FROM DT_WORKFLOW.DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE
                WHERE ACTIVE = '1' AND DELETED = '0'
                ORDER BY ORDER_NO ASC, CREATED DESC";

            var repository = repositoryFactory.Create("DefaultConnection");
            return await repository.ExecuteQueryAsync<HomeSlide>(sql);
        }

        public async Task<HomeSlide?> GetSlideByIdAsync(int id)
        {
            const string sql = @"
                SELECT ID, MENU_NAME_ID, SLIDE_NAME, SLIDE_IMAGE_PATH, THUMBNAIL_IMAGE_PATH,
                       SLIDE_CLICK_ACTION, SLIDE_TARGET_LINK, SLIDE_TARGET_CONTENT,
                       SLIDE_POPUP_WIDTH, SLIDE_POPUP_HEIGHT, ACTIVE, ORDER_NO,
                       VALID_DATE_START, VALID_DATE_END, CREATED, CREATED_BY,
                       LAST_UPDATED, LAST_UPDATED_BY, SLIDE_TARGET_HEADLINE, DELETED
                FROM DT_WORKFLOW.DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE
                WHERE ID = :Id AND DELETED = '0'";

            var parameters = new Dictionary<string, object>
            {
                { "Id", id }
            };

            var repository = repositoryFactory.Create("DefaultConnection");
            var results = await repository.ExecuteQueryAsync<HomeSlide>(sql, parameters);
            return results.FirstOrDefault();
        }

        public async Task<HomeSlide> UpdateSlideAsync(HomeSlide slide)
        {
            const string sql = @"
                UPDATE DT_WORKFLOW.DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE
                SET MENU_NAME_ID = :MenuNameId,
                    SLIDE_NAME = :Title,
                    SLIDE_IMAGE_PATH = :ImageUrl,
                    THUMBNAIL_IMAGE_PATH = :ThumbnailImagePath,
                    SLIDE_CLICK_ACTION = :SlideClickAction,
                    SLIDE_TARGET_LINK = :LinkUrl,
                    SLIDE_TARGET_CONTENT = :Description,
                    SLIDE_POPUP_WIDTH = :SlidePopupWidth,
                    SLIDE_POPUP_HEIGHT = :SlidePopupHeight,
                    ACTIVE = :IsActive,
                    ORDER_NO = :Order,
                    VALID_DATE_START = :ValidDateStart,
                    VALID_DATE_END = :ValidDateEnd,
                    LAST_UPDATED = :ModifiedDate,
                    LAST_UPDATED_BY = :ModifiedBy,
                    SLIDE_TARGET_HEADLINE = :SlideTargetHeadline
                WHERE ID = :Id AND DELETED = '0'";

            var parameters = new Dictionary<string, object>
            {
                { "Id", slide.Id },
                { "MenuNameId", slide.MenuNameId },
                { "Title", slide.Title },
                { "ImageUrl", slide.ImageUrl },
                { "ThumbnailImagePath", slide.ThumbnailImagePath },
                { "SlideClickAction", slide.SlideClickAction },
                { "LinkUrl", slide.LinkUrl },
                { "Description", slide.Description },
                { "SlidePopupWidth", slide.SlidePopupWidth },
                { "SlidePopupHeight", slide.SlidePopupHeight },
                { "IsActive", slide.IsActive },
                { "Order", slide.Order },
                { "ValidDateStart", slide.ValidDateStart },
                { "ValidDateEnd", slide.ValidDateEnd },
                { "ModifiedDate", DateTime.Now }, // Assuming ModifiedDate should be updated to current time
                { "ModifiedBy", slide.ModifiedBy }, // Assuming ModifiedBy is passed in slide object
                { "SlideTargetHeadline", slide.SlideTargetHeadline }
            };
            var repository = repositoryFactory.Create("DefaultConnection");
            await repository.ExecuteUpdateAsync(sql, parameters); // Assuming ExecuteUpdateAsync is appropriate
            return slide; // Return the slide, potentially refreshed if needed and possible
        }

        public async Task<bool> DeleteSlideAsync(int id)
        {
            // Soft delete by setting DELETED = '1'
            const string sql = @"UPDATE DT_WORKFLOW.DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE
                                 SET DELETED = '1', LAST_UPDATED = :CurrentDate
                                 WHERE ID = :Id AND DELETED = '0'";

            var parameters = new Dictionary<string, object>
            {
                { "Id", id },
                { "CurrentDate", DateTime.Now } // Set LAST_UPDATED on soft delete
            };

            var repository = repositoryFactory.Create("DefaultConnection");
            // Assuming ExecuteUpdateAsync returns int (rows affected) or similar method exists
            var rowsAffected = await repository.ExecuteUpdateAsync(sql, parameters);
            return rowsAffected > 0;
        }

        public async Task<bool> SlideExistsAsync(int id)
        {
            const string sql = @"SELECT COUNT(1) FROM DT_WORKFLOW.DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE
                                 WHERE ID = :Id AND DELETED = '0'";

            var parameters = new Dictionary<string, object>
            {
                { "Id", id }
            };

            var repository = repositoryFactory.Create("DefaultConnection");
            var count = await repository.ExecuteScalarAsync<int>(sql, parameters);
            return count > 0;
        }
    }
}
