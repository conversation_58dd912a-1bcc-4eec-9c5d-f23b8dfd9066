﻿using DigiflowAPI.Application.DTOs.Inbox;
using DigiflowAPI.Application.Interfaces.DataAccess;
using DigiflowAPI.Domain.Interfaces.Repositories;
using Microsoft.AspNetCore.Http;
namespace DigiflowAPI.Infrastructure.Data.Repositories
{
    public class InboxRepository(IOracleDataAccessRepositoryFactory repositoryFactory, IHttpContextAccessor httpContextAccessor) : IInboxRepository
    {
        public async Task<InboxPageView> GetAllAsync(long userId)
        {

            var loginParam = new Dictionary<string, object>
            {
                {"loginValue", userId },
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var inbox = await repository.ExecuteQueryAsync<InboxDto>(SqlScripts.Inbox.InboxSqlQueries.GetInboxQuery, loginParam);
            var delege = await repository.ExecuteQueryAsync<InboxDto>(SqlScripts.Inbox.InboxSqlQueries.GetDelegeInboxSQL, loginParam);
            var comment = await repository.ExecuteQueryAsync<InboxDto>(SqlScripts.Inbox.InboxSqlQueries.GetCommentInboxSQL, loginParam);
            //return (inbox, delege, comment);
            return new InboxPageView
            {
                Inbox = inbox,
                Delegated = delege,
                Commented = comment
            };
        }
        public async Task<IEnumerable<InboxDto?>> GetAllInboxAsync(long userId)
        {
            var parameters = new Dictionary<string, object>
            {
                {"LoginId", userId },
                {"AssignmentTypeCd", "TASKINBOX"},
                {"language", httpContextAccessor.HttpContext.Items["UserLanguage"]?.ToString() ?? "en"},
                {"ordered", "true"}
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteQueryAsync<InboxDto>(SqlScripts.Inbox.InboxSqlQueries.GetInboxQuery, parameters);
        }
        public async Task<IEnumerable<InboxDto?>> GetAllDelegatedAsync(long userId)
        {
            var parameters = new Dictionary<string, object>
            {
                {"LoginId", userId },
                {"AssignmentTypeCd", "TASKINBOX"},
                {"language", httpContextAccessor.HttpContext.Items["UserLanguage"]?.ToString() ?? "en"},
                {"ordered", "true"}
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteQueryAsync<InboxDto>(SqlScripts.Inbox.InboxSqlQueries.GetDelegeInboxSQL, parameters);
        }
        public async Task<IEnumerable<InboxDto?>> GetAllCommentedAsync(long userId)
        {
            var parameters = new Dictionary<string, object>
            {
                {"LoginId", userId },
                {"AssignmentTypeCd", "TASKINBOX"},
                {"language", httpContextAccessor.HttpContext.Items["UserLanguage"]?.ToString() ?? "en"},
                {"ordered", "true"}
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteQueryAsync<InboxDto>(SqlScripts.Inbox.InboxSqlQueries.GetCommentInboxSQL, parameters);
        }
    }
}