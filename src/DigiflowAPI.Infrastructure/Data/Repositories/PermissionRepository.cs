﻿using DigiflowAPI.Domain.Interfaces.Repositories;
using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Domain.DTOs.SOAP;
using DigiflowAPI.Application.DTOs.Common;

namespace DigiflowAPI.Infrastructure.Data.Repositories
{
    public class PermissionRepository(IPermissionProcessService permissionProcess) : IPermissionRepository
    {
        public async Task<IEnumerable<SelectOptionDto>?> GetLocationsAsync()
        {
            return await permissionProcess.ExecuteProcessAsync<GetLocationDto, SelectOptionDto>(
                "GetLocation",
                "LOCATION",
                null,
                serviceDto => new SelectOptionDto
                {
                    Value = serviceDto.LOCATION,
                    Label = serviceDto.LOCATION,
                    LabelEn = serviceDto.LOCATION,
                });

        }
        public async Task<IEnumerable<SelectOptionDto>?> GetSuggestersAsync(string location)
        {

            return await permissionProcess.ExecuteProcessAsync<GetLocationTeamDto, SelectOptionDto>(
                "GetLocationTeam",
                "",
                new Dictionary<string, string> { { "mtLocation", location } },
                serviceDto => new SelectOptionDto
                {
                    Value = serviceDto.USERNAME,
                    Label = serviceDto.NAME_SURNAME,
                    LabelEn = serviceDto.NAME_SURNAME
                });


        }
        public async Task<string> GetSuggestersTeamAsync(string suggester)
        {
            return await permissionProcess.ExecuteProcessValueAsync<string>(
                "GetCCUserInfo",
                "",
                "TEAM_NAME",
                new Dictionary<string, string> { { "login", suggester } });
        }
    }
}
