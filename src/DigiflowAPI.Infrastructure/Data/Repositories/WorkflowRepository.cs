﻿using DigiflowAPI.Application.DTOs.Workflow.XML.Definition;
using DigiflowAPI.Domain.Entities;
using DigiflowAPI.Domain.Entities.History;
using DigiflowAPI.Domain.Interfaces.Repositories;
using DigiflowAPI.Application.Interfaces.Services;
using Microsoft.Extensions.Configuration;
using System.Data;
using DigiflowAPI.Domain.Entities.Framework;
using DigiflowAPI.Domain.Enums;
using DigiflowAPI.Application.DTOs.Workflow.Delegation;
using DigiflowAPI.Application.DTOs.Workflow.Monitoring;
using DigiflowAPI.Domain.Entities.Workflow;
using DigiflowAPI.Infrastructure.Helpers;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Reflection;
using System.Text.Json;
using DigiflowAPI.Application.DTOs.Workflow.Operation;
using System.Text;
using DigiflowAPI.Application.Interfaces.DataAccess;
using DigiflowAPI.Application.DTOs.Common;
using DigiflowAPI.Application.DTOs.Workflow.History;

namespace DigiflowAPI.Infrastructure.Data.Repositories
{
    public class WorkflowRepository(IOracleDataAccessRepositoryFactory repositoryFactory, WorkflowTypeHelper workflowTypeHelper, Func<IGenericMailService> genericMailServiceFactory, IGlobalHelpers globalHelpers, IUserService userService, IConfiguration configuration) : IWorkflowRepository
    {
        private IGenericMailService genericMailService => genericMailServiceFactory();

        public async Task<IEnumerable<WorkflowXmlDefinitionSelectDto>> GetAllWorkflowsAsync()
        {
            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteQueryAsync(SqlScripts.Workflow.WorkflowSqlQueries.GetWorkflowSelectDto, reader => new WorkflowXmlDefinitionSelectDto
            {
                FlowName = Convert.ToString(reader["FLOWNAME"]),
                FlowNameEn = Convert.ToString(reader["FLOWNAMEEN"]),
                Value = Convert.ToString(reader["VALUE"]),
            });
        }

        public async Task<IEnumerable<SelectOptionDto>> GetAllAdminWorkflowsAsync()
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            long AdminId = await userService.GetActiveUserIdAsync();
            long IsSystemAdmin = 0;

            YYSAdminType adminType = await GetFlowAdminType(AdminId);

            if (adminType == YYSAdminType.SystemAdmin || adminType == YYSAdminType.SystemAndFlowAdmin)
                IsSystemAdmin = 1;

            var parameters = new Dictionary<string, object>
            {
                { "ADMIN_ID", AdminId },
                { "ISADMIN", IsSystemAdmin },
            };
            return await repository.ExecuteQueryAsync(SqlScripts.Workflow.WorkflowSqlQueries.GetAdminWorkflowDto, reader => new SelectOptionDto
            {
                IsDisabled = false,
                Username = "",
                Label = Convert.ToString(reader["NAME"]),
                LabelEn = Convert.ToString(reader["DESCRIPTION"]),
                Value = Convert.ToString(reader["WF_DEF_ID"]),
            }, parameters);
        }

        public async Task<long> GetActiveDelegateWithRecursiveDateValidation(long ownerLoginID, long delegationLoginId, long workflowDefID, DateTime delegationStartDate, DateTime delegationEndDate)
        {
            var parameters = new Dictionary<string, object>
            {
                { ":I_DELEGATION_OWNER_REF_ID", delegationLoginId },
                { ":I_WORKFLOW_DEF_ID", workflowDefID },
                { ":I_DELEGATION_START_DATE", delegationStartDate.ToString("dd/MM/yyyy") },
                { ":I_DELEGATION_END_DATE", delegationEndDate.ToString("dd/MM/yyyy") }
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            string? resultPath = await repository.ExecuteScalarAsync<string>(Data.SqlScripts.Workflow.WorkflowSqlQueries.GetActiveDelegateWithRecursiveDateValidation, parameters);

            if (string.IsNullOrEmpty(resultPath))
            {
                return -1;
            }

            string[] resultList = resultPath.Split('/');
            if (resultList.Length > 1)
            {
                if (long.TryParse(resultList[1], out long result))
                {
                    return result;
                }
                throw new InvalidOperationException("Failed to parse result to long.");
            }

            return -1;
        }

        public async Task<long> GetActiveDelegateWithRecursive(long loginID, long workflowDefID)
        {
            var parameters = new Dictionary<string, object>
            {
                { ":AssignToLoginId", loginID },
                { ":WorkFlowDefId", workflowDefID },
            };

            var repository = repositoryFactory.Create("DefaultConnection");
            string? resultPath = await repository.ExecuteScalarAsync<string>(Data.SqlScripts.Workflow.WorkflowSqlQueries.GetActiveDelegateWithRecursive, parameters);

            if (resultPath != null)
            {
                string[] resultlist = resultPath.Split('/');

                if (resultlist.Length > 1)
                {
                    return long.Parse(resultlist[1].ToString());
                }
            }

            return -1;
        }

        public async Task<bool> CheckForDelegationOwn(long LoginId, long WfDefinitionId, DateTime StartDate, DateTime EndDate)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            var parameters = new Dictionary<string, object>
            {
                {":loginValue", LoginId.ToString() },
                {":WfdefId", WfDefinitionId.ToString()},
                {":MyDelegationEndDate", EndDate.ToString("dd/MM/yyyy")},
            };

            if (StartDate.ToString("dd/MM/yyyy") == DateTime.Today.ToString("dd/MM/yyyy"))
            {
                parameters.Add(":MyDelegationStartDate", DateTime.Today.ToString("dd/MM/yyyy"));
            }
            else
            {
                parameters.Add(":MyDelegationStartDate", StartDate.ToString("dd/MM/yyyy"));
            }

            var result = await repository.ExecuteQueryAsync<DelegationWorkflowEntity>(Data.SqlScripts.Workflow.WorkflowSqlQueries.GetDelegationOwnCheck, parameters);
            return result.Count > 0;
        }

        public async Task<long> GetLastActionToLoginId(long WfInsId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            var parameters = new Dictionary<string, object>
            {
                {":WfInstId", WfInsId.ToString() },
            };

            var result = await repository.ExecuteScalarAsync<decimal>(@"
                SELECT
                    FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID
                    FROM FRAMEWORK.F_WF_WORKFLOW_INSTANCE
                    LEFT JOIN FRAMEWORK.F_WF_STATE_INSTANCE ON FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID =FRAMEWORK.F_WF_STATE_INSTANCE.WF_WORKFLOW_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_INSTANCE ON FRAMEWORK.F_WF_STATE_INSTANCE.WF_STATE_INSTANCE_ID = FRAMEWORK.F_WF_ACTION_INSTANCE.WF_STATE_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_TASK_INSTANCE ON FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID =FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ASSIGNMENT  ON (FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID =FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNMENT_OWNER_REF_ID) 
                    WHERE
                    FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_TYPE_CD='TASKINBOX'
                    and FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID<>0 and
                    FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID =(Select
                    Max(FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID)
                    from FRAMEWORK.F_WF_WORKFLOW_INSTANCE
                    Inner Join
                    FRAMEWORK.F_WF_STATE_INSTANCE
                    On FRAMEWORK.F_WF_STATE_INSTANCE.WF_WORKFLOW_INSTANCE_ID = FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID
                    Inner Join FRAMEWORK.F_WF_ACTION_INSTANCE On FRAMEWORK.F_WF_ACTION_INSTANCE.WF_STATE_INSTANCE_ID=FRAMEWORK.F_WF_STATE_INSTANCE.WF_STATE_INSTANCE_ID
                    Inner Join FRAMEWORK.F_WF_ACTION_TASK_INSTANCE On FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID=FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID
                    where FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID=:WfInstId
                    and FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_STATUS_T_CD='SENDED')
            ", parameters);

            return result != null ? (long)result : 0;
        }

        public async Task<IList<string>?> GetLastActionToLoginIdList(long WfInsId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            var parameters = new Dictionary<string, object>
            {
                {":WfInstId", WfInsId.ToString() },
            };

            return await repository.ExecuteQueryAsync<string>(@"
                SELECT
                    FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID as ASSIGNED_OWNER_REF_ID
                    FROM FRAMEWORK.F_WF_WORKFLOW_INSTANCE
                    LEFT JOIN FRAMEWORK.F_WF_STATE_INSTANCE ON FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID =FRAMEWORK.F_WF_STATE_INSTANCE.WF_WORKFLOW_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_INSTANCE ON FRAMEWORK.F_WF_STATE_INSTANCE.WF_STATE_INSTANCE_ID = FRAMEWORK.F_WF_ACTION_INSTANCE.WF_STATE_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_TASK_INSTANCE ON FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID =FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ASSIGNMENT  ON (FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID =FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNMENT_OWNER_REF_ID) 
                    WHERE
                    FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_TYPE_CD='TASKINBOX'
                    and FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID<>0 and
                    FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID =(Select
                    Max(FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID)
                    from FRAMEWORK.F_WF_WORKFLOW_INSTANCE
                    Inner Join
                    FRAMEWORK.F_WF_STATE_INSTANCE
                    On FRAMEWORK.F_WF_STATE_INSTANCE.WF_WORKFLOW_INSTANCE_ID = FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID
                    Inner Join FRAMEWORK.F_WF_ACTION_INSTANCE On FRAMEWORK.F_WF_ACTION_INSTANCE.WF_STATE_INSTANCE_ID=FRAMEWORK.F_WF_STATE_INSTANCE.WF_STATE_INSTANCE_ID
                    Inner Join FRAMEWORK.F_WF_ACTION_TASK_INSTANCE On FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID=FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID
                    where FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID=:WfInstId
                    and FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_STATUS_T_CD='SENDED')
            ", reader => reader["ASSIGNED_OWNER_REF_ID"].ToString(), parameters);
        }

        public async Task<IList<long>> GetAssignedUser(long WfInsId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            var parameters = new Dictionary<string, object>
            {
                {":WfInstId", WfInsId.ToString() },
            };

            return await repository.ExecuteQueryAsync(@"
                SELECT
                    FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID
                    FROM FRAMEWORK.F_WF_WORKFLOW_INSTANCE
                    LEFT JOIN FRAMEWORK.F_WF_STATE_INSTANCE ON FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_CURRENT_STATE_ID=FRAMEWORK.F_WF_STATE_INSTANCE.WF_STATE_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_INSTANCE ON FRAMEWORK.F_WF_STATE_INSTANCE.WF_CURRENT_ACTION_INSTANCE_ID =FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_TASK_INSTANCE ON FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID =FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID
                    INNER JOIN FRAMEWORK.F_WF_ASSIGNMENT ON (FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID =FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNMENT_OWNER_REF_ID)
                    WHERE FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID = :WfInstId
                    and FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_TYPE_CD='TASKINBOX'
                    ORDER BY FRAMEWORK.F_WF_ACTION_INSTANCE.START_TIME DESC
            ", reader => Convert.ToInt64(reader["ASSIGNED_OWNER_REF_ID"]), parameters);
        }

        public async Task<IList<FLogin>> GetAssignedList(long WfActionInsId, long? WfDefId = null, string assignmentTypeCd = "TASKINBOX")
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            var parameters = new Dictionary<string, object>
            {
                { "assignmentTypeCd", assignmentTypeCd },
                { "instanceId", WfActionInsId },
                { "definitionId", WfDefId }
            };

            string sql = @"
            SELECT b.*
            FROM FRAMEWORK.F_WF_ASSIGNMENT a
            INNER JOIN FRAMEWORK.F_LOGIN B on B.LOGIN_ID = A.ASSIGNED_OWNER_REF_ID
            WHERE a.WF_ASSIGNMENT_TYPE_CD = :assignmentTypeCd
            AND (
                (a.IS_DEF_ASSIGNMENT = 0 AND a.ASSIGNMENT_OWNER_REF_ID = :instanceId)
                OR (a.IS_DEF_ASSIGNMENT = 1 AND a.ASSIGNMENT_OWNER_REF_ID = :definitionId)
            )";

            if (WfDefId == null)
            {
                sql = sql.Replace("OR (a.IS_DEF_ASSIGNMENT = 1 AND a.ASSIGNMENT_OWNER_REF_ID = :definitionId)", "");
            }

            return await repository.ExecuteQueryAsync<FLogin>(sql, parameters);
        }

        public async Task<long> AssignToId(long wfInsId, long loginId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");
            var assignList = await AssignToIdListLong(wfInsId);
            if (assignList.Contains(loginId))
            {
                return loginId;
            }

            var parameters = new Dictionary<string, object>
            {
                {":WfInstId", wfInsId.ToString() },
            };

            IList<long> result = await repository.ExecuteQueryAsync<long>(@"
                SELECT
                    FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID
                    FROM FRAMEWORK.F_WF_WORKFLOW_INSTANCE
                    LEFT JOIN FRAMEWORK.F_WF_STATE_INSTANCE ON FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_CURRENT_STATE_ID=FRAMEWORK.F_WF_STATE_INSTANCE.WF_STATE_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_INSTANCE ON FRAMEWORK.F_WF_STATE_INSTANCE.WF_CURRENT_ACTION_INSTANCE_ID =FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_TASK_INSTANCE ON FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID =FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID
                    INNER JOIN FRAMEWORK.F_WF_ASSIGNMENT ON (FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID =FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNMENT_OWNER_REF_ID)
                    WHERE FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID = :WfInstId
                    and FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_TYPE_CD='TASKINBOX'
                    ORDER BY FRAMEWORK.F_WF_ACTION_INSTANCE.START_TIME DESC
            ", reader => Convert.ToInt64(reader["ASSIGNED_OWNER_REF_ID"]), parameters);

            if (result.Count > 0)
            {
                return result[0];
            }
            else
            {
                return await GetLastAssignToLoginId(wfInsId);
            }
        }

        public async Task<long> GetLastAssignToLoginId(long wfInsId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            var parameters = new Dictionary<string, object>
            {
                {":WfInstId", wfInsId.ToString() },
            };
            IList<long> result = await repository.ExecuteQueryAsync<long>(@"
                Select
                    FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID
                    from FRAMEWORK.F_WF_ASSIGNMENT
                    where FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_ID = (SELECT MAX(FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_ID)
                    FROM FRAMEWORK.F_WF_WORKFLOW_INSTANCE
                    LEFT JOIN FRAMEWORK.F_WF_STATE_INSTANCE ON FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID = FRAMEWORK.F_WF_STATE_INSTANCE.WF_WORKFLOW_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_INSTANCE ON FRAMEWORK.F_WF_STATE_INSTANCE.WF_STATE_INSTANCE_ID = FRAMEWORK.F_WF_ACTION_INSTANCE.WF_STATE_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_TASK_INSTANCE ON FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID = FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ASSIGNMENT ON(FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID = FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNMENT_OWNER_REF_ID)
                    LEFT JOIN FRAMEWORK.F_WF_DELEGATION ON FRAMEWORK.F_WF_DELEGATION.WORKFLOW_DEF_ID = FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_DEF_ID AND FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID = FRAMEWORK.F_WF_DELEGATION.DELEGATION_OWNER_REF_ID
                    WHERE FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID = :WfInstId
                and FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_TYPE_CD<>'TASKCOMMENT'  and FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_TYPE_CD<>'WFVIEW'    and FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID Is Not Null)
            ", reader => Convert.ToInt64(reader["ASSIGNED_OWNER_REF_ID"]), parameters);
            if (result.Count > 0)
            {
                return result.First();
            }
            else
            {
                return 1;
            }
        }


        public async Task<IList<FLogin>> AssignToIdList(long WfInsId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            var parameters = new Dictionary<string, object>
            {
                {":WfInstId", WfInsId.ToString() },
            };

            return await repository.ExecuteQueryAsync<FLogin>(@"
                SELECT
                    FRAMEWORK.F_LOGIN.*
                    FROM FRAMEWORK.F_WF_WORKFLOW_INSTANCE
                    LEFT JOIN FRAMEWORK.F_WF_STATE_INSTANCE ON FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_CURRENT_STATE_ID=FRAMEWORK.F_WF_STATE_INSTANCE.WF_STATE_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_INSTANCE ON FRAMEWORK.F_WF_STATE_INSTANCE.WF_CURRENT_ACTION_INSTANCE_ID =FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_TASK_INSTANCE ON FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID =FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID
                    INNER JOIN FRAMEWORK.F_WF_ASSIGNMENT ON (FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID =FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNMENT_OWNER_REF_ID)
                    INNER JOIN FRAMEWORK.F_LOGIN ON FRAMEWORK.F_LOGIN.LOGIN_ID = FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID
                    WHERE FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID = :WfInstId
                    and FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_TYPE_CD='TASKINBOX'
                    ORDER BY FRAMEWORK.F_WF_ACTION_INSTANCE.START_TIME DESC
            ", parameters);
        }

        public async Task<IList<long>> AssignToIdListLong(long wfInsId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            var parameters = new Dictionary<string, object>
            {
                {":WfInstId", wfInsId.ToString() },
            };

            return await repository.ExecuteQueryAsync(@"
                SELECT
                    FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID
                    FROM FRAMEWORK.F_WF_WORKFLOW_INSTANCE
                    LEFT JOIN FRAMEWORK.F_WF_STATE_INSTANCE ON FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_CURRENT_STATE_ID=FRAMEWORK.F_WF_STATE_INSTANCE.WF_STATE_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_INSTANCE ON FRAMEWORK.F_WF_STATE_INSTANCE.WF_CURRENT_ACTION_INSTANCE_ID =FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_TASK_INSTANCE ON FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID =FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID
                    INNER JOIN FRAMEWORK.F_WF_ASSIGNMENT ON (FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID =FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNMENT_OWNER_REF_ID)
                    WHERE FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID = :WfInstId
                    and FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_TYPE_CD='TASKINBOX'
                    ORDER BY FRAMEWORK.F_WF_ACTION_INSTANCE.START_TIME DESC
            ", reader => Convert.ToInt64(reader["ASSIGNED_OWNER_REF_ID"]), parameters);
        }

        public async Task<bool> DelegateCheck(long InstanceId, long LoginId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");
            var wfInstance = await repository.GetEntityAsync<FWfWorkflowInstance>(InstanceId);
            if (wfInstance == null)
            {
                return false;
            }
            long delegeId = await GetActiveDelegateWithRecursive(LoginId, wfInstance.WfWorkflowDef.WfWorkflowDefId);

            if (delegeId > 0)
            {
                return true;
            }
            return false;
        }

        public async Task<FWfWorkflowInstance?> GetWorkflowInstance(long wfInstanceId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");
            return await repository.GetEntityAsync<FWfWorkflowInstance>(wfInstanceId);
        }

        public async Task<string> GetWorkflowAdmins(long WfWorkflowDefId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            var parameters = new Dictionary<string, object>
            {
                {":Value", WfWorkflowDefId },
            };

            var result = await repository.ExecuteQueryAsync<VwWorkflowAdmins>(SqlScripts.Workflow.WorkflowSqlQueries.GetWorkflowDefinitionIdByValue, parameters);
            return string.Join(",", result
                                .Where(item => !string.IsNullOrEmpty(item.NameSurname))
                                .Select(item => item.NameSurname));
        }

        public async Task<string> GetWorkflowAdmins(string PageName)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            var parameters = new Dictionary<string, object>
            {
                {":PageName", PageName },
            };

            var result = await repository.ExecuteQueryAsync<VwWorkflowAdmins>(SqlScripts.Workflow.WorkflowSqlQueries.GetWorkflowDefinitionIdByPageName, parameters);
            return string.Join(",", result
                                .Where(item => !string.IsNullOrEmpty(item.NameSurname))
                                .Select(item => item.NameSurname));
        }

        public async Task<IEnumerable<VwWorkflowHistory>> GetWorkflowHistory(long WfInstanceId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            var parameters = new Dictionary<string, object>
            {
                {":WfInstanceId", WfInstanceId },
            };

            return await repository.ExecuteQueryAsync<VwWorkflowHistory>(SqlScripts.Workflow.WorkflowSqlQueries.GetWorkFlowHistory, parameters);
        }

        public async Task<VwWorkflowHistory?> GetHistory(long WfHistoryId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            var parameters = new Dictionary<string, object>
            {
                {":WfHistoryId", WfHistoryId },
            };

            return await repository.ExecuteSingleQueryAsync<VwWorkflowHistory>(SqlScripts.Workflow.WorkflowSqlQueries.GetHistory, parameters);
        }

        public async Task UpdateHistoryCommentAsync(long wfHistoryId, string NewComment, List<string> uploadedFiles = null)
        {
            var workflowRepository = repositoryFactory.Create("DT_WORKFLOW");
            var frameworkRepository = repositoryFactory.Create("FrameworkConnection");
            var parameters = new Dictionary<string, object>
    {
        {":WfHistoryId", wfHistoryId },
    };

            var history = await frameworkRepository.ExecuteSingleQueryAsync<WorkflowHistory>(@"
        SELECT * FROM FRAMEWORK.F_WF_WORKFLOW_HISTORY
        WHERE FRAMEWORK.F_WF_WORKFLOW_HISTORY.WF_HISTORY_ID = :WfHistoryId
    ", parameters);

            if (history == null)
                throw new Exception("History record not found.");

            long WfInstanceId = (long)history.WfWorkflowInstanceId;

            string oldComment = history?.TaskComment ?? "";

            // First, we write to history log
            var historyLogObj = new Dictionary<string, object>
    {
        {":TaskComment", history.TaskComment },
        {":UpdatedBy", await userService.GetUserInfo() }, // This returns the user ID or info
        {":Updated", DateTime.Now },
        {":WfHistoryId", wfHistoryId },
        {":VersionID", await GetHistoryLogVersion(wfHistoryId) },
    };

            await workflowRepository.ExecuteUpdateAsync(@"
        UPDATE DT_WORKFLOW.F_WF_WORKFLOW_HISTORY_LOG 
        SET 
            TASKCOMMENT = :TaskComment,
            UPDATED_BY = :UpdatedBy,
            UPDATED = :Updated,
            VERSION_ID = :VersionID
        WHERE 
            WF_HISTORY_ID = :WfHistoryId
    ", historyLogObj);

            // If the action is "Dosya Yüklendi" and we have uploaded files, 
            // we will append file links to the NewComment
            if (uploadedFiles != null && uploadedFiles.Count > 0)
            {
                // Upload each file to SharePoint and create file links
                List<string> fileLinks = new List<string>();
                string baseSharePointUrl = "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/ActionPanelDocs/";

                foreach (var fileName in uploadedFiles)
                {
                    // If you need to physically upload the file to SharePoint:
                    // var uploadedUrl = UploadDocumentHelper.SharepointWriteList(localFilePath, fileName, configuration["SharePointActionPanelUploadFolder"]);
                    // Here we assume the file is already placed or the URL is directly available.
                    // If you only have the filename and need to get a URL, adapt as needed.

                    // Append link
                    string fileLink = $"<a target='_blank' href='{baseSharePointUrl}{fileName}'>{fileName}</a>";
                    fileLinks.Add(fileLink);
                }

                // Append files to NewComment
                var commentBuilder = new StringBuilder(NewComment.Trim());
                if (!string.IsNullOrEmpty(NewComment))
                {
                    commentBuilder.Append("<br/>");
                }

                if (fileLinks.Count > 0)
                {
                    commentBuilder.Append(" - ");
                    commentBuilder.Append(string.Join("<br/> - ", fileLinks));
                    commentBuilder.Append("<br/>");
                }

                NewComment = commentBuilder.ToString();
            }

            // Update the main history record with the new comment (with or without files)
            history.TaskComment = NewComment;

            var historyObj = new Dictionary<string, object>
    {
        {":TaskComment", history.TaskComment },
        {":WfHistoryId", wfHistoryId },
    };

            await frameworkRepository.ExecuteUpdateAsync(@"
        UPDATE FRAMEWORK.F_WF_WORKFLOW_HISTORY 
        SET 
            TASKCOMMENT = :TaskComment
        WHERE 
            WF_HISTORY_ID = :WfHistoryId
    ", historyObj);

            // Retrieve workflow definition name
            var wfInstanceParams = new Dictionary<string, object>
    {
        {":WfInstanceId", WfInstanceId },
    };
            string WfDefName = await frameworkRepository.ExecuteScalarAsync<string>(@"
        SELECT def.NAME FROM FRAMEWORK.F_WF_WORKFLOW_INSTANCE ins 
        INNER JOIN FRAMEWORK.F_WF_WORKFLOW_DEF def ON ins.WF_WORKFLOW_DEF_ID = def.WF_WORKFLOW_DEF_ID
        WHERE ins.WF_WORKFLOW_INSTANCE_ID = :WfInstanceId", wfInstanceParams);

            // Sending email notification
            DpHrUsers userInfo = await userService.GetUserInfo(null);

            var emailParameters = new Dictionary<string, string>
    {
        { "CommentUpdatedUserNameSurName", userInfo.NameSurname },
        { "OldDescription", oldComment },
        { "Description", NewComment },
        { "WfDefName", WfDefName },
        { "WfHistoryInstanceId", WfInstanceId.ToString() },
    };

            IList<string> toList = await GetMailActionUser(WfInstanceId);

            if (configuration.GetValue<bool>("EmailSettings:IsMailDebugMode"))
            {
                string mailAddresses = string.Join(", ", toList);
                emailParameters["ToListMailActionUser"] = mailAddresses;
            }

            if (toList.Count > 0)
            {
                FWfWorkflowInstance wfIns = await frameworkRepository.GetEntityAsync<FWfWorkflowInstance>(WfInstanceId);
                FWfStateInstance wfState = await frameworkRepository.GetEntityAsync<FWfStateInstance>(wfIns.WfCurrentStateId);
                FWfActionInstance actionInstance = await frameworkRepository.GetEntityAsync<FWfActionInstance>(wfState.WfCurrentActionInstanceId);

                string workflowAdmins = await GetWorkflowAdmins(wfIns.WfWorkflowDef.WfWorkflowDefId);
                emailParameters.Add("WorkFlowAdminList", workflowAdmins);

                await genericMailService.SendMailAsync(
                    mailTemplateId: 10120,
                    actionInstance: actionInstance,
                    emailParameters: emailParameters,
                    emailToList: toList,
                    emailCcList: null
                );
            }
        }

        private async Task<long> GetHistoryLogVersion(long historyId)
        {
            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var parameters = new Dictionary<string, object>
            {
                {":V_WF_HISTORY_ID", historyId },
            };

            string sql = @"
                    SELECT A.VERSION_ID FROM DT_WORKFLOW.F_WF_WORKFLOW_HISTORY_LOG A 
                        WHERE A.WF_HISTORY_ID=:V_WF_HISTORY_ID AND A.VERSION_ID 
                        IN (SELECT MAX(B.VERSION_ID) FROM DT_WORKFLOW.F_WF_WORKFLOW_HISTORY_LOG B WHERE B.WF_HISTORY_ID=:V_WF_HISTORY_ID)
            ";
            long? result = await repository.ExecuteSingleQueryAsync<long?>(sql, parameters);

            return result ?? 0;
        }

        private async Task<IList<string>> GetMailActionUser(long wfInstanceId)
        {
            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var parameters = new Dictionary<string, object>
            {
                {":WorkflowInstanceId", wfInstanceId },
            };

            string sql = @"
                    SELECT 
                        U.E_MAIL, 
                        H.ACTION_LOGIN_ID, 
                        I.WF_WORKFLOW_DEF_ID
                    FROM 
                        (SELECT DISTINCT ACTION_LOGIN_ID, WF_WORKFLOW_INSTANCE_ID
                         FROM FRAMEWORK.F_WF_WORKFLOW_HISTORY
                         WHERE WF_WORKFLOW_INSTANCE_ID = :WorkflowInstanceId
                           AND WF_WORKFLOW_HISTORY_TYPE_CD <> 'ASSIGN'
                           AND ACTION_LOGIN_ID <> 0) H
                    INNER JOIN DT_WORKFLOW.DP_HR_USERS U ON U.F_LOGIN_ID = H.ACTION_LOGIN_ID
                    INNER JOIN FRAMEWORK.F_WF_WORKFLOW_INSTANCE I ON I.WF_WORKFLOW_INSTANCE_ID = H.WF_WORKFLOW_INSTANCE_ID
                    WHERE NOT EXISTS
                        (SELECT 1
                         FROM DT_WORKFLOW.WF_DENY_MAIL D
                         WHERE D.LOGIN_ID = H.ACTION_LOGIN_ID
                           AND D.WF_DEF_ID = I.WF_WORKFLOW_DEF_ID)

            ";

            var result = await repository.ExecuteQueryAsync<string>(sql, reader => reader["E_MAIL"].ToString(), parameters);
            return result;
        }

        public async Task<int> GetStateDefId(string stateDefAdi)
        {
            var repository = repositoryFactory.Create("DT_WORKFLOW");
            string sql = @"SELECT ID FROM DT_WORKFLOW.WF_XML_STATE_DEFINITION WHERE NAME = :StateDefName";
            var parameters = new Dictionary<string, object>
            {
                { "StateDefName", stateDefAdi }
            };

            var result = await repository.ExecuteScalarAsync<int>(sql, parameters);
            if (result == 0)
            {
                throw new Exception($"State Def Bulunamadı: {stateDefAdi}");
            }
            return result;
        }

        public async Task<IEnumerable<WorkflowHistoryDto>> GetWorkflowInstanceHistoryMigrateDelegation(long wfdefId, long WfInstanceId, IEnumerable<WorkflowHistoryDto> historyData)
        {
            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var updatedHistoryData = new List<WorkflowHistoryDto>();

            foreach (var history in historyData)
            {
                if (history.WfWorkflowHistoryTypeCd != "STARTED")
                {
                    try
                    {
                        string sql = @"
                            SELECT (DOWNR.NAME_SURNAME || ' delegesi ' || DRF.NAME_SURNAME) as Delegation
                            FROM FRAMEWORK.F_WF_DELEGATION D
                            INNER JOIN DT_WORKFLOW.DP_HR_USERS DOWNR ON D.DELEGATION_OWNER_REF_ID = DOWNR.F_LOGIN_ID
                            INNER JOIN DT_WORKFLOW.DP_HR_USERS DRF ON D.DELEGATE_REF_ID = DRF.F_LOGIN_ID
                            WHERE D.WORKFLOW_DEF_ID = :WfDefId 
                            AND SYSDATE BETWEEN D.DELEGATION_START_DATE AND D.DELEGATION_END_DATE
                            AND D.DELEGATION_OWNER_REF_ID = :AssignedLoginId";

                        var parameters = new Dictionary<string, object>
                        {
                            { "WfDefId", wfdefId },
                            { "AssignedLoginId", history.AssignedLoginId }
                        };

                        var delegation = await repository.ExecuteScalarAsync<string>(sql, parameters);

                        if (!string.IsNullOrEmpty(delegation))
                        {
                            history.Users = delegation;
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log the exception
                        // Consider whether to throw or handle the exception
                        Console.WriteLine($"Error processing history item: {ex.Message}");
                    }
                }

                updatedHistoryData.Add(history);
            }

            return updatedHistoryData;
        }

        private async Task<long> GetWorkflowDefId(long InstanceId)
        {
            var repository = repositoryFactory.Create("DefaultConnection");
            var parameters = new Dictionary<string, object>
            {
                { "InstanceId", InstanceId }
            };

            string sql = "SELECT WF_WORKFLOW_DEF_ID FROM FRAMEWORK.F_WF_WORKFLOW_INSTANCE WHERE WF_WORKFLOW_INSTANCE_ID = :InstanceId";

            return await repository.ExecuteScalarAsync<long>(sql, parameters);
        }

        public bool IsInFlowManagerList(string val, string AccessLoginIdList)
        {
            if (AccessLoginIdList.Contains("|"))
            {
                string[] arr = AccessLoginIdList.Split('|');
                return arr.Contains(val);
            }
            else
            {
                return AccessLoginIdList == val;
            }
        }

        private YYSAdminType ConvertToYYSAdminType(string deger) => deger switch
        {
            "FlowAdmin" => YYSAdminType.FlowAdmin,
            "SystemAdmin" => YYSAdminType.SystemAdmin,
            "SystemAndFlowAdmin" => YYSAdminType.SystemAndFlowAdmin,
            _ => YYSAdminType.None,
        };

        public async Task<long> GetMaxDinamicAssignLoginId(long WorkFlowInstanceId)
        {
            var repository = repositoryFactory.Create("DefaultConnection");
            var parameters = new Dictionary<string, object>
            {
                { "InstanceId", WorkFlowInstanceId }
            };

            string sql = @"select A.ASSIGNED_LOGIN_ID
                        from FRAMEWORK.F_WF_WORKFLOW_HISTORY a 
                        left join FRAMEWORK.F_WF_STATE_INSTANCE b on B.WF_STATE_INSTANCE_ID = A.WF_STATE_INSTANCE_ID
                        left join dt_workflow.YYS_STATE_AUTHORIZATION c on C.STATE_DEF_ID = B.WF_STATE_DEF_ID
                        where A.WF_WORKFLOW_INSTANCE_ID = :InstanceId and A.WF_WORKFLOW_HISTORY_TYPE_CD = 'ACCEPTED' and A.ACTION_DATE = (
                        select MAX(A.ACTION_DATE)
                        from FRAMEWORK.F_WF_WORKFLOW_HISTORY a 
                        left join FRAMEWORK.F_WF_STATE_INSTANCE b on B.WF_STATE_INSTANCE_ID = A.WF_STATE_INSTANCE_ID
                        left join dt_workflow.YYS_STATE_AUTHORIZATION c on C.STATE_DEF_ID = B.WF_STATE_DEF_ID
                        where A.WF_WORKFLOW_INSTANCE_ID = :InstanceId and A.WF_WORKFLOW_HISTORY_TYPE_CD = 'ACCEPTED' AND C.STATE_DEF_ID IS NULL
                        )";

            return await repository.ExecuteScalarAsync<long>(sql, parameters);
        }


        public async Task<bool?> IsWorkflowInstanceExists(int wfInstanceId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            var parameters = new Dictionary<string, object>
            {
                {":wfInstanceId", wfInstanceId },
            };

            var result = await repository.ExecuteQueryAsync<DelegationWorkflowEntity>(SqlScripts.Workflow.WorkflowSqlQueries.GetWorkflowInstance, parameters);
            return result.Count > 0;
        }

        public async Task<bool> DelegationCheck(long InstanceId, long AssignedLoginId, long ActionLoginId, DateTime DelegationCheckDate)
        {
            var repository = repositoryFactory.Create("DefaultConnection");
            var parameters = new Dictionary<string, object>
            {
                { "WfInstId", InstanceId },
                { "AssignToLoginId", AssignedLoginId },
                { "ActionLoginId", ActionLoginId },
                { "WorkFlowDefId", await GetWorkflowDefId(InstanceId) },
                { "DelegationLevel", await GetMaxDelegationLevel(await GetWorkflowDefId(InstanceId), AssignedLoginId, ActionLoginId) }
            };

            string sql = @"Select distinct * from 
                (SELECT WF_DELEGATION_ID,
                          WORKFLOW_DEF_ID,
                          LEVEL - 1 PATHLEN,
                             SYS_CONNECT_BY_PATH (DELEGATE_REF_ID, '/')
                          || '/'
                          || DELEGATION_OWNER_REF_ID
                          || '/'
                             PATH,
                          DELEGATION_START_DATE,
                          DELEGATION_END_DATE,
                          DELEGATION_OWNER_REF_ID,
                          DELEGATE_REF_ID,
                          LEVEL AS DEGER
                     FROM framework.f_wf_delegation a
               CONNECT BY NOCYCLE PRIOR DELEGATION_OWNER_REF_ID = DELEGATE_REF_ID
                                  AND sysdate BETWEEN DELEGATION_START_DATE AND DELEGATION_END_DATE
               START WITH DELEGATE_REF_ID = :ActionLoginId AND WORKFLOW_DEF_ID = :WorkFlowDefId AND
                  (sysdate BETWEEN TO_DATE (DELEGATION_START_DATE, 'DD.MM.YYYY HH24:MI:SS') AND TO_DATE ( DELEGATION_END_DATE,'DD.MM.YYYY HH24:MI:SS'))) RecDelegation                  
                  WHERE DEGER = :DelegationLevel and PATH like '%:AssignToLoginId%' and WORKFLOW_DEF_ID = :WorkFlowDefId";

            var result = await repository.ExecuteQueryAsync<object>(sql, parameters);
            return result.Any();
        }

        public async Task<bool> IsActionFlow(long instanceId, long loginId)
        {
            var repository = repositoryFactory.Create("DefaultConnection");
            var parameters = new Dictionary<string, object>
            {
                { "InstanceId", instanceId },
                { "LoginId", loginId }
            };

            string sql = @"Select
             FRAMEWORK.F_WF_WORKFLOW_HISTORY.WF_HISTORY_ID
             from FRAMEWORK.F_WF_WORKFLOW_HISTORY
             where FRAMEWORK.F_WF_WORKFLOW_HISTORY.WF_WORKFLOW_INSTANCE_ID = :InstanceId
             and (FRAMEWORK.F_WF_WORKFLOW_HISTORY.ASSIGNED_LOGIN_ID = :LoginId or
             FRAMEWORK.F_WF_WORKFLOW_HISTORY.ACTION_LOGIN_ID = :LoginId)";

            try
            {
                var result = await repository.ExecuteScalarAsync<long>(sql, parameters);
                return result > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task<YYSAdminType> GetFlowAdminType(long LoginId)
        {
            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var parameters = new Dictionary<string, object>
            {
                { "LoginId", LoginId }
            };

            string sql = @"SELECT DT_WORKFLOW.YYS_ADMIN_LEVEL_TYPES.DESCRIPTION 
                           FROM DT_WORKFLOW.YYS_ADMINS 
                           INNER JOIN DT_WORKFLOW.YYS_ADMIN_LEVEL_TYPES ON DT_WORKFLOW.YYS_ADMINS.ADMIN_LEVEL_TYPE_ID = DT_WORKFLOW.YYS_ADMIN_LEVEL_TYPES.ADMIN_LEVEL_TYPE_ID
                           WHERE DT_WORKFLOW.YYS_ADMINS.LOGIN_ID = :LoginId";

            var result = await repository.ExecuteScalarAsync<string>(sql, parameters);

            return ConvertToYYSAdminType(result);
        }

        public async Task<IEnumerable<FWfWorkflowDef>> GetLiveWorkFlows()
        {
            var repository = repositoryFactory.Create("DefaultConnection");
            string sql = @"Select * from FRAMEWORK.F_WF_WORKFLOW_DEF where FRAMEWORK.F_WF_WORKFLOW_DEF.VERSION_NUMBER > 0 Order By FRAMEWORK.F_WF_WORKFLOW_DEF.NAME";

            return await repository.ExecuteQueryAsync<FWfWorkflowDef>(sql);
        }

        public async Task<IEnumerable<FWfWorkflowDef>> GetWorkflowsOfAdmin(long adminId, bool isAdmin)
        {
            var repository = repositoryFactory.Create("DefaultConnection");
            var parameters = new Dictionary<string, object>
            {
                { "ADMIN_ID", adminId },
                { "ISADMIN", isAdmin ? 1 : 0 }
            };

            string sql = @"SELECT distinct FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID, NAME, DESCRIPTION FROM FRAMEWORK.F_WF_WORKFLOW_DEF
                            LEFT JOIN DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS ON DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.WF_DEF_ID = FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID
                            WHERE (DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.ADMIN_ID = :ADMIN_ID or :ISADMIN = 1)
                            AND FRAMEWORK.F_WF_WORKFLOW_DEF.VERSION_NUMBER > 0 order by FRAMEWORK.F_WF_WORKFLOW_DEF.NAME";

            return await repository.ExecuteQueryAsync<FWfWorkflowDef>(sql, parameters);
        }

        public async Task<bool> IsStateStatik(long WorkFlowInstanceId, long WorkFlowStateId)
        {
            var repository = repositoryFactory.Create("DefaultConnection");
            var parameters = new Dictionary<string, object>
            {
                { "InstanceId", WorkFlowInstanceId },
                { "StateId", WorkFlowStateId }
            };

            string sql = @"select case when C.STATE_DEF_ID IS NULL then 'False' else 'True' end as DURUM
                        from FRAMEWORK.F_WF_WORKFLOW_HISTORY a 
                        left join FRAMEWORK.F_WF_STATE_INSTANCE b on B.WF_STATE_INSTANCE_ID = A.WF_STATE_INSTANCE_ID
                        left join dt_workflow.YYS_STATE_AUTHORIZATION c on C.STATE_DEF_ID = B.WF_STATE_DEF_ID
                        where A.WF_WORKFLOW_INSTANCE_ID = :InstanceId and A.WF_STATE_INSTANCE_ID = :StateId";

            return await repository.ExecuteScalarAsync<bool>(sql, parameters);
        }

        private async Task<long> GetMaxDelegationLevel(long wfdefinitionId, long AssignUserId, long ActionUserId)
        {
            var repository = repositoryFactory.Create("DefaultConnection");
            var parameters = new Dictionary<string, object>
            {
                { "wfdefinitionId", wfdefinitionId },
                { "AssignUserId", AssignUserId },
                { "ActionUserId", ActionUserId }
            };

            string sql = @"Select MAX(DEGER) from 
                (SELECT WF_DELEGATION_ID,
                          WORKFLOW_DEF_ID,
                          LEVEL - 1 PATHLEN,
                             SYS_CONNECT_BY_PATH (DELEGATE_REF_ID, '/')
                          || '/'
                          || DELEGATION_OWNER_REF_ID
                          || '/'
                             PATH,
                          DELEGATION_START_DATE,
                          DELEGATION_END_DATE,
                          DELEGATION_OWNER_REF_ID,
                          DELEGATE_REF_ID,
                          LEVEL AS DEGER
                     FROM framework.f_wf_delegation a
               CONNECT BY NOCYCLE PRIOR DELEGATION_OWNER_REF_ID = DELEGATE_REF_ID
                                  AND sysdate BETWEEN DELEGATION_START_DATE
                                                                            AND DELEGATION_END_DATE
               START WITH DELEGATE_REF_ID = :ActionUserId AND WORKFLOW_DEF_ID = :wfdefinitionId AND
          (sysdate BETWEEN TO_DATE (DELEGATION_START_DATE, 'DD.MM.YYYY HH24:MI:SS') AND TO_DATE ( DELEGATION_END_DATE,'DD.MM.YYYY HH24:MI:SS'))) RecDelegation
          Where PATH like '/:ActionUserId%' and PATH like '%/:AssignUserId/'";

            return await repository.ExecuteScalarAsync<long>(sql, parameters);
        }

        public async Task<FWfWorkflowInstance> GetCurrentWfIns(long instanceId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");
            var parameters = new Dictionary<string, object>
            {
                { "InstanceId", instanceId }
            };

            string sql = @"SELECT * FROM FRAMEWORK.F_WF_WORKFLOW_INSTANCE 
                           WHERE WF_WORKFLOW_INSTANCE_ID = :InstanceId";

            return await repository.ExecuteSingleQueryAsync<FWfWorkflowInstance>(sql, parameters);
        }

        public async Task<FWfActionTaskInstance> GetCurrentActionTaskInstance(long instanceId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");
            try
            {
                var currentWfIns = await repository.GetEntityAsync<FWfWorkflowInstance>(instanceId);

                if (currentWfIns == null || currentWfIns.WfCurrentState == null)
                {
                    return null;
                }

                var currentStateDef = await repository.GetEntityAsync<FWfStateDef>(currentWfIns.WfCurrentState.WfStateDefId);

                if (currentWfIns.WfCurrentState.WfCurrentActionInstanceId != null)
                {
                    var actionTaskInstance = await repository.GetInheritedEntityAsync<FWfActionTaskInstance>(currentWfIns.WfCurrentState.WfCurrentActionInstanceId);

                    // Additional check similar to the original logic
                    if (actionTaskInstance != null &&
                        (actionTaskInstance.WfActionInstanceId != currentWfIns.WfCurrentState.WfCurrentActionInstanceId ||
                         currentWfIns.WfCurrentState != null))
                    {
                        actionTaskInstance = await repository.GetInheritedEntityAsync<FWfActionTaskInstance>(currentWfIns.WfCurrentState.WfCurrentActionInstanceId);
                    }

                    return actionTaskInstance;
                }
                else if (currentStateDef != null && currentStateDef.WfStateType.WfStateTypeCd == "MIDDLE")
                {
                    // This is to handle the case in the catch block of the original code
                    return await repository.GetInheritedEntityAsync<FWfActionTaskInstance>(currentWfIns.WfCurrentState.WfCurrentActionInstanceId);
                }
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"An error occurred: {ex.Message}");
                // You might want to log this exception or handle it according to your application's error handling strategy
            }

            return null;
        }

        //    private async Task<FWfWorkflowInstance> GetCurrentWorkflowInstance(long instanceId)
        //    {
        //        var repository = repositoryFactory.Create("DefaultConnection");
        //        var parameters = new Dictionary<string, object>
        //{
        //    { "WfInstId", instanceId }
        //};
        //        string sql = @"SELECT * FROM FRAMEWORK.F_WF_WORKFLOW_INSTANCE 
        //               WHERE WF_WORKFLOW_INSTANCE_ID = :InstanceId";
        //        return await repository.ExecuteSingleQueryAsync<FWfWorkflowInstance>(sql, parameters);
        //    }

        //    private async Task<FWfActionTaskInstance> GetActionTaskInstance(long actionInstanceId)
        //    {
        //        var repository = repositoryFactory.Create("DefaultConnection");
        //        var parameters = new Dictionary<string, object>
        //{
        //    { "ActionInstanceId", actionInstanceId }
        //};
        //        string sql = @"SELECT * FROM FRAMEWORK.F_WF_ACTION_TASK_INSTANCE 
        //               WHERE WF_ACTION_INSTANCE_ID = :ActionInstanceId";
        //        return await repository.ExecuteSingleQueryAsync<FWfActionTaskInstance>(sql, parameters);
        //    }

        //    private async Task<bool> IsMiddleState(long stateDefId)
        //    {
        //        var repository = repositoryFactory.Create("DefaultConnection");
        //        var parameters = new Dictionary<string, object>
        //{
        //    { "StateDefId", stateDefId }
        //};
        //        string sql = @"SELECT WF_STATE_TYPE_CD FROM FRAMEWORK.F_WF_STATE_DEF 
        //               JOIN FRAMEWORK.F_WF_STATE_TYPE ON F_WF_STATE_DEF.WF_STATE_TYPE_ID = F_WF_STATE_TYPE.WF_STATE_TYPE_ID
        //               WHERE WF_STATE_DEF_ID = :StateDefId";
        //        var result = await repository.ExecuteScalarAsync<string>(sql, parameters);
        //        return result == "MIDDLE";
        //    }


        public async Task<bool> IsFlowAdmin(long instanceId, long userId)
        {
            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var parameters = new Dictionary<string, object>
            {
                { "InstanceId", instanceId },
                { "UserId", userId }
            };

            string sql = @"SELECT COUNT(*) FROM DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS 
                           WHERE ADMIN_ID = :UserId 
                           AND WF_DEF_ID = (
                               SELECT WF_WORKFLOW_DEF_ID 
                               FROM FRAMEWORK.F_WF_WORKFLOW_INSTANCE 
                               WHERE WF_WORKFLOW_INSTANCE_ID = :InstanceId
                           )";

            int count = await repository.ExecuteScalarAsync<int>(sql, parameters);
            return count > 0;
        }

        public async Task<long> CommendToId(long instanceId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");
            var parameters = new Dictionary<string, object>
            {
                { "WfInstId", instanceId },
            };

            string sql = @"select distinct(assigned_owner_ref_id)  AS assigned_owner_ref_id from
                    (
                    SELECT
                    FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID
                    FROM FRAMEWORK.F_WF_WORKFLOW_INSTANCE
                    LEFT JOIN FRAMEWORK.F_WF_STATE_INSTANCE ON FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_CURRENT_STATE_ID=FRAMEWORK.F_WF_STATE_INSTANCE.WF_STATE_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_INSTANCE ON FRAMEWORK.F_WF_STATE_INSTANCE.WF_CURRENT_ACTION_INSTANCE_ID =FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_TASK_INSTANCE ON FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID =FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID
                    INNER JOIN FRAMEWORK.F_WF_ASSIGNMENT ON (FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID =FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNMENT_OWNER_REF_ID)  
                    LEFT JOIN FRAMEWORK.F_WF_DELEGATION ON FRAMEWORK.F_WF_DELEGATION.WORKFLOW_DEF_ID =FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_DEF_ID AND FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID = FRAMEWORK.F_WF_DELEGATION.DELEGATION_OWNER_REF_ID
                    WHERE FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID = :WfInstId
                    and FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_TYPE_CD='TASKCOMMENT'
                    ORDER BY FRAMEWORK.F_WF_ACTION_INSTANCE.START_TIME DESC)";
            long? result = await repository.ExecuteSingleQueryAsync(sql, reader => Convert.ToInt64(reader["ASSIGNED_OWNER_REF_ID"].ToString()), parameters);
            if (result != null)
                return (long)result;
            else return 0;
        }

        public async Task<IList<long>> CommendToIdLongList(long instanceId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");
            var parameters = new Dictionary<string, object>
            {
                { "WfInstId", instanceId },
            };

            string sql = @"select distinct(assigned_owner_ref_id)  AS assigned_owner_ref_id from
                    (
                    SELECT
                    FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID
                    FROM FRAMEWORK.F_WF_WORKFLOW_INSTANCE
                    LEFT JOIN FRAMEWORK.F_WF_STATE_INSTANCE ON FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_CURRENT_STATE_ID=FRAMEWORK.F_WF_STATE_INSTANCE.WF_STATE_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_INSTANCE ON FRAMEWORK.F_WF_STATE_INSTANCE.WF_CURRENT_ACTION_INSTANCE_ID =FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_TASK_INSTANCE ON FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID =FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID
                    INNER JOIN FRAMEWORK.F_WF_ASSIGNMENT ON (FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID =FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNMENT_OWNER_REF_ID)  
                    LEFT JOIN FRAMEWORK.F_WF_DELEGATION ON FRAMEWORK.F_WF_DELEGATION.WORKFLOW_DEF_ID =FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_DEF_ID AND FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID = FRAMEWORK.F_WF_DELEGATION.DELEGATION_OWNER_REF_ID
                    WHERE FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID = :WfInstId
                    and FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_TYPE_CD='TASKCOMMENT'
                    ORDER BY FRAMEWORK.F_WF_ACTION_INSTANCE.START_TIME DESC)";
            return await repository.ExecuteQueryAsync(sql, reader => Convert.ToInt64(reader["ASSIGNED_OWNER_REF_ID"].ToString()), parameters);
        }


        public async Task<long> DelegationUserId(long loginId, long workflowDefId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");
            var parameters = new Dictionary<string, object>
            {
                { "AssignToLoginId", loginId },
                { "WorkFlowDefId", workflowDefId },
            };

            string sql = @"Select PATH
                                        from
                                        (
                                        SELECT distinct WF_DELEGATION_ID,
                                                      WORKFLOW_DEF_ID,
                                                      LEVEL - 1 PATHLEN,
                                                      SYS_CONNECT_BY_PATH (DELEGATE_REF_ID, '/') || '/' ||  DELEGATION_OWNER_REF_ID || '/' PATH,
                                                      DELEGATION_START_DATE,
                                                      DELEGATION_END_DATE,
                                                      DELEGATION_OWNER_REF_ID,
                                                      DELEGATE_REF_ID,
                                                      LEVEL AS DEGER
                                                 FROM framework.f_wf_delegation a
                                           CONNECT BY NOCYCLE PRIOR DELEGATION_OWNER_REF_ID = DELEGATE_REF_ID  AND  WORKFLOW_DEF_ID=:WorkFlowDefId AND sysdate BETWEEN DELEGATION_START_DATE AND DELEGATION_END_DATE
                                           START WITH  (sysdate BETWEEN TO_DATE (DELEGATION_START_DATE,'DD.MM.YYYY HH24:MI:SS') AND TO_DATE (DELEGATION_END_DATE,'DD.MM.YYYY HH24:MI:SS')))
                                        Where WORKFLOW_DEF_ID=:WorkFlowDefId AND DELEGATION_OWNER_REF_ID = :AssignToLoginId and (sysdate BetWeen DELEGATION_START_DATE and DELEGATION_END_DATE)";
            string? result = await repository.ExecuteSingleQueryAsync<string>(sql, reader => reader["PATH"].ToString(), parameters);
            if (result != null)
                return Convert.ToInt64(result.Split('/').ToString());
            else
                return -1;

        }

        public async Task<IList<FLogin>> GetCommendToLoginList(long instanceId)
        {
            return await GetFLoginList(instanceId);
        }

        public async Task<IList<FLogin>> GetFLoginList(long instanceId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            IList<FLogin> result = [];
            IList<long> commentIdLongList = await CommendToIdLongList(instanceId);
            foreach (var ownerRefId in commentIdLongList)
            {
                result.Append(await repository.GetEntityAsync<FLogin>(ownerRefId));
            }

            return result;
        }

        public async Task<bool> IsWfAdmin(long wfDefId)
        {
            var userId = await userService.GetActiveUserIdAsync();
            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var parameters = new Dictionary<string, object>
            {
                { "WF_DEF_ID", wfDefId },
                { "LOGIN_ID", userId }
            };

            string sql = @"SELECT * FROM YYS_WORKFLOWS_OF_ADMINS WHERE ADMIN_ID=:LOGIN_ID and WF_DEF_ID=:WF_DEF_ID";

            int count = await repository.ExecuteScalarAsync<int>(sql, parameters);
            return count > 0;
        }

        public async Task<bool> IsViewUser(long UserId, long WfInstanceId, long wfDefinitionId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            string sql = @"SELECT * FROM FRAMEWORK.F_WF_ASSIGNMENT WHERE ((FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNMENT_OWNER_REF_ID = " + WfInstanceId.ToString() + ") or (    (FRAMEWORK.F_WF_ASSIGNMENT.is_def_assignment = 1 and Assignment_owner_ref_id = " + wfDefinitionId.ToString() + " ) )) AND  FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_TYPE_CD = 'WFVIEW' and  FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID =" + UserId.ToString() + "";

            long assigmentID = await repository.ExecuteScalarAsync<long>(sql);
            return assigmentID != 0;
        }

        public async Task<bool> IsReportAdmin(long WfDefId, long loginId)
        {
            var repository = repositoryFactory.Create("DT_WORKFLOW");

            string sql = @"SELECT * FROM DT_WORKFLOW.VW_REPORT_ADMINS WHERE WF_DEF_ID=:WF_DEF_ID AND LOGIN_ID=:LOGIN_ID";
            var parameters = new Dictionary<string, object>
            {
                { "WF_DEF_ID", WfDefId },
                { "LOGIN_ID", loginId }
            };
            long result = await repository.ExecuteScalarAsync<long>(sql, parameters);
            return result != 0;
        }

        public async Task<bool> IsRolledBack(long instanceId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");
            var stateInsParams = new Dictionary<string, object>
                {
                    { "InstanceId", instanceId }
                };

            IList<FWfStateType> wfStateTypeCd = await repository.ExecuteQueryAsync<FWfStateType>(@"
                SELECT FRAMEWORK.F_WF_STATE_DEF.* FROM FRAMEWORK.F_WF_STATE_INSTANCE 
                INNER JOIN FRAMEWORK.F_WF_STATE_DEF ON FRAMEWORK.F_WF_STATE_DEF.WF_STATE_DEF_ID = FRAMEWORK.F_WF_STATE_INSTANCE.WF_STATE_DEF_ID
                WHERE FRAMEWORK.F_WF_STATE_INSTANCE.WF_WORKFLOW_INSTANCE_ID = :InstanceId 
                ORDER BY FRAMEWORK.F_WF_STATE_INSTANCE.WF_STATE_INSTANCE_ID DESC
            ", stateInsParams);

            if (wfStateTypeCd != null && wfStateTypeCd.Count == 0)
            {
                return false;
            }
            else
            {
                return wfStateTypeCd[1].WfStateTypeCd == "MIDDLE";
            }
        }

        public async Task<bool> GetDaemonStatus(long wfInstanceId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");
            var stateInsParams = new Dictionary<string, object>
                {
                    { "record_ID", wfInstanceId.ToString() },
                };

            return await repository.ExecuteScalarAsync<bool>("select case when count(*) >= 1 then 1 else 0 end as Sonuc from FRAMEWORK.F_WF_COMMAND_QUEUE where (cmd_status = 'Active' or cmd_status='Working') and record_ID = :record_ID", stateInsParams);
        }




        public async Task<List<DelegationWorkflowEntity>> GetDelegationsOfUserAsync(long loginId, string delegationType)
        {
            var repository = repositoryFactory.Create("DT_WORKFLOW");

            string sql = @"SELECT * FROM DT_WORKFLOW.WF_DF_DELEGATION_REQUEST
                           WHERE OWNER_LOGIN_ID = :LoginId";

            if (delegationType == "active")
            {
                sql += " AND END_TIME > SYSDATE";
            }
            else if (delegationType == "expired")
            {
                sql += " AND END_TIME <= SYSDATE";
            }

            var parameters = new Dictionary<string, object>
            {
                { "LoginId", loginId }
            };

            var delegations = await repository.ExecuteQueryAsync<DelegationWorkflowEntity>(sql, parameters);
            return delegations;
        }

        public async Task<FWfDelegation?> GetDelegationInfoAsync(long delegationId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            string sql = @"SELECT * FROM FRAMEWORK.F_WF_DELEGATION
                           WHERE WF_DELEGATION_ID = :DelegationId";

            var parameters = new Dictionary<string, object>
            {
                { "DelegationId", delegationId }
            };

            var delegation = await repository.ExecuteSingleQueryAsync<FWfDelegation>(sql, parameters);
            return delegation;
        }

        public async Task<int> UpdateDelegationRequestAsync(long delegationId, DateTime startDate, DateTime endDate)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            string sql = @"UPDATE FRAMEWORK.F_WF_DELEGATION
                        SET     FRAMEWORK.F_WF_DELEGATION.DELEGATION_END_DATE    = :DELEGATION_END_DATE,
                                FRAMEWORK.F_WF_DELEGATION.DELEGATION_START_DATE  = :DELEGATION_START_DATE
                        WHERE  FRAMEWORK.F_WF_DELEGATION.WF_DELEGATION_ID        = :WF_DELEGATION_ID";

            var parameters = new Dictionary<string, object>
            {
                { "DELEGATION_END_DATE",  endDate },
                { "DELEGATION_START_DATE", startDate},
                { "WF_DELEGATION_ID", delegationId.ToString() }
            };

            var result = await repository.ExecuteUpdateAsync(sql, parameters);
            return result;
        }

        public async Task<List<EndDelegationTableDto>> GetAllDelegationsAsync(long adminId, string delegationType)
        {
            var repository = repositoryFactory.Create("DT_WORKFLOW");

            // Check if the user is an admin
            string isAdminSql = @"SELECT * FROM DT_WORKFLOW.YYS_ADMINS A
                                  WHERE A.IS_SYS_ADMIN = 1 AND A.LOGIN_ID = :AdminId";

            var isAdminParameters = new Dictionary<string, object>
            {
                { "AdminId", adminId }
            };

            var isAdminResult = await repository.ExecuteQueryAsync<object>(isAdminSql, isAdminParameters);

            int isAdmin = isAdminResult.Count > 0 ? 1 : 0;

            string activeDelegations = "";
            if (delegationType == "active")
            {
                activeDelegations = "FRAMEWORK.F_WF_DELEGATION.DELEGATION_END_DATE>sysdate and ";
            }
            string sql = @"Select WF_DELEGATION_ID , FRAMEWORK.F_WF_WORKFLOW_DEF.NAME, FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID , FRAMEWORK.F_WF_DELEGATION.DELEGATION_OWNER_REF_ID, FRAMEWORK.F_WF_DELEGATION.DELEGATE_REF_ID,
                 UI.NAME_SURNAME as OWNER_NAMESURNAME , DT_WORKFLOW.VW_USER_INFORMATION.NAME_SURNAME,DELEGATION_START_DATE,DELEGATION_END_DATE from FRAMEWORK.F_WF_DELEGATION
                LEFT JOIN DT_WORKFLOW.VW_USER_INFORMATION on FRAMEWORK.F_WF_DELEGATION.DELEGATE_REF_ID=DT_WORKFLOW.VW_USER_INFORMATION.LOGIN_ID
                LEFT JOIN DT_WORKFLOW.VW_USER_INFORMATION UI on UI.LOGIN_ID=FRAMEWORK.F_WF_DELEGATION.DELEGATION_OWNER_REF_ID
                LEFT JOIN FRAMEWORK.F_WF_WORKFLOW_DEF ON FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID=FRAMEWORK.F_WF_DELEGATION.WORKFLOW_DEF_ID
                WHERE " + activeDelegations + @"  FRAMEWORK.F_WF_DELEGATION.WORKFLOW_DEF_ID in
                (SELECT distinct FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID FROM FRAMEWORK.F_WF_WORKFLOW_DEF
                LEFT JOIN DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS  ON DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.WF_DEF_ID=FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID
                WHERE (DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.ADMIN_ID=:ADMIN_ID or :ISADMIN=1)
                AND FRAMEWORK.F_WF_WORKFLOW_DEF.VERSION_NUMBER>0)
                Order by DELEGATION_END_DATE";


            var parameters = new Dictionary<string, object>
            {
                { "ISADMIN", isAdmin },
                { "ADMIN_ID", adminId }
            };

            var delegations = await repository.ExecuteQueryAsync<EndDelegationTableDto>(sql, parameters);
            return delegations;
        }

        public async Task<bool> EndDelegationAsync(EndDelegationRequestDto[] delegations)
        {
            // Fetch the delegation request
            if (delegations.Length == 0)
                return false;

            try
            {
                var repository = repositoryFactory.Create("FrameworkConnection");

                foreach (EndDelegationRequestDto delegation in delegations)
                {

                    string sql = @"UPDATE framework.f_wf_delegation
                                    SET delegation_end_date = :i_delegation_update_date
                                    WHERE (f_wf_delegation.delegation_owner_ref_id = :i_delegation_owner_ref_id)
                                       AND (f_wf_delegation.delegate_ref_id = :i_delegate_ref_id)
                                       AND (f_wf_delegation.workflow_def_id = :i_workflow_def_id)
                                       AND (:i_delegation_update_date BETWEEN f_wf_delegation.delegation_start_date
                                       AND f_wf_delegation.delegation_end_date)";

                    DateTime date = DateTime.Now;
                    if (date < delegation.DelegationStartDate)
                    {
                        date = delegation.DelegationStartDate;
                    }

                    var parameters = new Dictionary<string, object>
                    {
                        { "i_delegation_update_date",  date },
                        { "i_delegation_owner_ref_id", delegation.DelegationId},
                        { "i_delegate_ref_id", delegation.DelegatedLoginId},
                        { "i_workflow_def_id", delegation.WorkflowDefId }
                    };

                    var result = await repository.ExecuteUpdateAsync(sql, parameters);
                }
            }
            catch (Exception)
            {
                return false;
            }

            return true;
        }

        public async Task<IEnumerable<EndMonitoringTableDto>> GetActiveMonitoringsAsync(long loginId)
        {

            var repository = repositoryFactory.Create("DefaultConnection");

            string activeMonitoringSQL = @" Select
            WFINS.OWNER_LOGIN_ID,
            FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID,
            F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID,
            WFINS.WF_WORKFLOW_DEF_ID,
            DT_WORKFLOW.WF_DF_MONITORING_REQUEST.MONITORING_REQUEST_ID ,
            DT_WORKFLOW.WF_DF_MONITORING_REQUEST.CREATED_BY as PERSONEL_ID ,
            DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_TYPE_ID,
            DT_WORKFLOW.VW_USER_INFORMATION.NAME_SURNAME,
            DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_INSTANCE_ID as AKIS_NO ,
            (Case
            When DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_TYPE_ID=1 Then 'İş Akışı Numarası Bazında'
            When  DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_TYPE_ID=2 Then 'İş Akışı Türü Bazında'
            When DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_TYPE_ID=3 Then 'Kullanıcı Bazında' End) as FlowType,
            (Case
            When DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_TYPE_ID=1 Then  INSDEFID.NAME
            When  DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_TYPE_ID=2 Then FRAMEWORK.F_WF_WORKFLOW_DEF.NAME
            When DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_TYPE_ID=3 Then DT_WORKFLOW.DIGIFLOWPACKACE.GetMonitoringFlowName(DT_WORKFLOW.WF_DF_MONITORING_REQUEST.MONITORING_REQUEST_ID) End) as FlowName,
            ('WFPages/MonitoringRequest.aspx?LoginId='||:LoginId||'&wfInstanceId='||FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID) as WfLink
            from DT_WORKFLOW.WF_DF_MONITORING_REQUEST
            Left Join DT_WORKFLOW.VW_USER_INFORMATION On DT_WORKFLOW.VW_USER_INFORMATION.LOGIN_ID=DT_WORKFLOW.WF_DF_MONITORING_REQUEST.CREATED_BY
            Left Join FRAMEWORK.F_WF_WORKFLOW_DEF On FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID=DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_DEF_ID
            Left Join FRAMEWORK.F_WF_WORKFLOW_INSTANCE On FRAMEWORK.F_WF_WORKFLOW_INSTANCE.ENTITY_REF_ID=DT_WORKFLOW.WF_DF_MONITORING_REQUEST.MONITORING_REQUEST_ID
            Left Join FRAMEWORK.F_WF_WORKFLOW_INSTANCE WfIns On WFINS.WF_WORKFLOW_INSTANCE_ID=WF_DF_MONITORING_REQUEST.FLOW_INSTANCE_ID and  DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_TYPE_ID=1
            Left Join FRAMEWORK.F_WF_WORKFLOW_DEF InsDefId On INSDEFID.WF_WORKFLOW_DEF_ID= WFINS.WF_WORKFLOW_DEF_ID
            Left Join FRAMEWORK.F_WF_ASSIGNMENT On FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNMENT_OWNER_REF_ID= DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_DEF_ID and FRAMEWORK.F_WF_ASSIGNMENT.IS_DEF_ASSIGNMENT=1 and FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_TYPE_CD='WorkflowModify'
            where DT_WORKFLOW.WF_DF_MONITORING_REQUEST.IS_ACTIVE=1 and FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_DEF_ID=1339
            and (WFINS.OWNER_LOGIN_ID=:LoginId or DT_WORKFLOW.WF_DF_MONITORING_REQUEST.PERSONEL_ID=:LoginId or FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID=:LoginId)
            and FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_STATUS_TYPE_CD='COMPLETED'
            Order by DT_WORKFLOW.WF_DF_MONITORING_REQUEST.MONITORING_REQUEST_ID DESC";

            var parameters = new Dictionary<string, object>
                {
                    { "LoginId", loginId }
                };

            return await repository.ExecuteQueryAsync<EndMonitoringTableDto>(activeMonitoringSQL, parameters);
        }

        public async Task<IEnumerable<EndMonitoringTableDto>> GetAllMonitoringsAsync(long adminId)
        {

            var AdminType = await GetFlowAdminType(adminId);
            string isAdmin = "0";

            if (AdminType != null)
            {
                isAdmin = "1";
            }

            var repository = repositoryFactory.Create("FrameworkConnection");
            var allMonitoringSQL = $@"
               Select
            WFINS.OWNER_LOGIN_ID,
            FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID,
            F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID,
            WFINS.WF_WORKFLOW_DEF_ID,
            DT_WORKFLOW.WF_DF_MONITORING_REQUEST.MONITORING_REQUEST_ID ,
            DT_WORKFLOW.WF_DF_MONITORING_REQUEST.CREATED_BY as PERSONEL_ID ,
            DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_TYPE_ID,
            DT_WORKFLOW.VW_USER_INFORMATION.NAME_SURNAME,
            DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_INSTANCE_ID as AKIS_NO ,
            (Case
            When DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_TYPE_ID=1 Then 'İş Akışı Numarası Bazında'
            When  DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_TYPE_ID=2 Then 'İş Akışı Türü Bazında'
            When DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_TYPE_ID=3 Then 'Kullanıcı Bazında' End) as FlowType,
            (Case
            When DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_TYPE_ID=1 Then  INSDEFID.NAME
            When  DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_TYPE_ID=2 Then FRAMEWORK.F_WF_WORKFLOW_DEF.NAME
            When DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_TYPE_ID=3 Then DT_WORKFLOW.DIGIFLOWPACKACE.GetMonitoringFlowName(DT_WORKFLOW.WF_DF_MONITORING_REQUEST.MONITORING_REQUEST_ID) End) as FlowName
          , ('WFPages/MonitoringRequest.aspx?LoginId='||:LoginId||'&wfInstanceId='||FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID) as WfLink
            from DT_WORKFLOW.WF_DF_MONITORING_REQUEST
            Left Join DT_WORKFLOW.VW_USER_INFORMATION On DT_WORKFLOW.VW_USER_INFORMATION.LOGIN_ID=DT_WORKFLOW.WF_DF_MONITORING_REQUEST.CREATED_BY
            Left Join FRAMEWORK.F_WF_WORKFLOW_DEF On FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID=DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_DEF_ID
            Left Join FRAMEWORK.F_WF_WORKFLOW_INSTANCE On FRAMEWORK.F_WF_WORKFLOW_INSTANCE.ENTITY_REF_ID=DT_WORKFLOW.WF_DF_MONITORING_REQUEST.MONITORING_REQUEST_ID
            Left Join FRAMEWORK.F_WF_WORKFLOW_INSTANCE WfIns On WFINS.WF_WORKFLOW_INSTANCE_ID=WF_DF_MONITORING_REQUEST.FLOW_INSTANCE_ID and  DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_TYPE_ID=1
            LEFT JOIN DT_WORKFLOW.VW_USER_INFORMATION UINFO ON UINFO.LOGIN_ID =WfIns.OWNER_LOGIN_ID
            Left Join FRAMEWORK.F_WF_WORKFLOW_DEF InsDefId On INSDEFID.WF_WORKFLOW_DEF_ID= WFINS.WF_WORKFLOW_DEF_ID
            Left Join FRAMEWORK.F_WF_ASSIGNMENT On FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNMENT_OWNER_REF_ID= DT_WORKFLOW.WF_DF_MONITORING_REQUEST.FLOW_DEF_ID and FRAMEWORK.F_WF_ASSIGNMENT.IS_DEF_ASSIGNMENT=1 and FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_TYPE_CD='WorkflowModify'

            where DT_WORKFLOW.WF_DF_MONITORING_REQUEST.IS_ACTIVE=1 and FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_DEF_ID=1339
            and (WFINS.OWNER_LOGIN_ID=:LoginId or DT_WORKFLOW.WF_DF_MONITORING_REQUEST.PERSONEL_ID=:LoginId or FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID=:LoginId)
            and FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_STATUS_TYPE_CD='COMPLETED'
            AND (FLOW_INSTANCE_ID
            in (select FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID  from FRAMEWORK.F_WF_WORKFLOW_INSTANCE
            where   FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_STATUS_TYPE_CD='COMPLETED' and  FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_DEF_ID in (SELECT distinct FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID  FROM FRAMEWORK.F_WF_WORKFLOW_DEF
                LEFT JOIN DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS  ON DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.WF_DEF_ID=FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID
                WHERE (DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.ADMIN_ID=:ADMIN_ID or :ISADMIN=1)) ) OR  Flow_Def_Id in  (SELECT distinct FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID  FROM FRAMEWORK.F_WF_WORKFLOW_DEF
                LEFT JOIN DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS  ON DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.WF_DEF_ID=FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID
                WHERE (DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.ADMIN_ID=:ADMIN_ID or :ISADMIN=1)) OR   flow_instance_id
            in (select FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID  from FRAMEWORK.F_WF_WORKFLOW_INSTANCE
            where   FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_STATUS_TYPE_CD='COMPLETED' and  FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_DEF_ID in (SELECT distinct FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID  FROM FRAMEWORK.F_WF_WORKFLOW_DEF
                LEFT JOIN DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS  ON DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.WF_DEF_ID=FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID
                WHERE (DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.ADMIN_ID=:ADMIN_ID or :ISADMIN=1)) )  )
            Order by DT_WORKFLOW.WF_DF_MONITORING_REQUEST.MONITORING_REQUEST_ID DESC";

            var parameters = new Dictionary<string, object>
            {
                { "ADMIN_ID", adminId },
                { "ISADMIN", isAdmin },
                { "LoginId", "0" }
            };

            return await repository.ExecuteQueryAsync<EndMonitoringTableDto>(allMonitoringSQL, parameters);

        }


        public async Task<IEnumerable<long>> GetCommendToLoginDelegeIDList(long WfInsId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");

            var query = @"
                select distinct(VW_DIGIFLOW_DELEGATION_INBOX2.DELEGATE_REF_ID) DELEGATE_REF_ID from
                    (
                    SELECT
                    FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID, FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_DEF_ID
                    FROM FRAMEWORK.F_WF_WORKFLOW_INSTANCE
                    LEFT JOIN FRAMEWORK.F_WF_STATE_INSTANCE ON FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_CURRENT_STATE_ID=FRAMEWORK.F_WF_STATE_INSTANCE.WF_STATE_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_INSTANCE ON FRAMEWORK.F_WF_STATE_INSTANCE.WF_CURRENT_ACTION_INSTANCE_ID =FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID
                    LEFT JOIN FRAMEWORK.F_WF_ACTION_TASK_INSTANCE ON FRAMEWORK.F_WF_ACTION_INSTANCE.WF_ACTION_INSTANCE_ID =FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID
                    INNER JOIN FRAMEWORK.F_WF_ASSIGNMENT ON (FRAMEWORK.F_WF_ACTION_TASK_INSTANCE.WF_ACTION_TASK_INSTANCE_ID =FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNMENT_OWNER_REF_ID)  
                    LEFT JOIN FRAMEWORK.F_WF_DELEGATION ON FRAMEWORK.F_WF_DELEGATION.WORKFLOW_DEF_ID =FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_DEF_ID AND FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID = FRAMEWORK.F_WF_DELEGATION.DELEGATION_OWNER_REF_ID
                    WHERE FRAMEWORK.F_WF_WORKFLOW_INSTANCE.WF_WORKFLOW_INSTANCE_ID = :WfInstId
                    and FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_TYPE_CD='TASKCOMMENT'
                    ORDER BY FRAMEWORK.F_WF_ACTION_INSTANCE.START_TIME DESC) Y, 
                    DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX2  
                    WHERE
                    Y.assigned_owner_ref_id = DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX2.DELEGATION_OWNER_REF_ID   
                    AND  DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX2.WF_WORKFLOW_DEF_ID = Y.WF_WORKFLOW_DEF_ID  
                    AND DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX2.WF_ASSIGNMENT_TYPE_CD = 'TASKCOMMENT' 
                    AND DT_WORKFLOW.VW_DIGIFLOW_DELEGATION_INBOX2.wf_action_status_type_cd <> 'BYPASSED'  
            ";

            var parameters = new Dictionary<string, object>
            {
                { "WfInstId", WfInsId.ToString() },
            };

            return await repository.ExecuteQueryAsync<long>(query, parameters);
        }

        public async Task<bool> EndMonitoringAsync(EndMonitoringRequestDto[] monitorings)
        {
            var frameworkRepository = repositoryFactory.Create("FrameworkConnection");
            var defaultRepository = repositoryFactory.Create("DefaultConnection");

            try
            {
                foreach (var monitoring in monitorings)
                {
                    var requestObject = await defaultRepository.GetEntityAsync<MonitoringRequestEntity>(monitoring.MonitoringRequestId);
                    // Delete from F_WF_ASSIGNMENT if necessary
                    if (monitoring.FlowTypeId == 1)
                    {
                        string sql = @"Select FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_ID from FRAMEWORK.F_WF_ASSIGNMENT  where
                                FRAMEWORK.F_WF_ASSIGNMENT.IS_DEF_ASSIGNMENT=:IS_DEF_ASSIGNMENT
                                and FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_TYPE_CD=:WF_ASSIGNMENT_TYPE_CD
                                and FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID=:ASSIGNED_OWNER_REF_ID
                                and FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNMENT_OWNER_REF_ID=:ASSIGNMENT_OWNER_REF_ID";


                        var parameters = new Dictionary<string, object>
                        {
                            { "IS_DEF_ASSIGNMENT", 0 },
                            { "WF_ASSIGNMENT_TYPE_CD", "WFVIEW" },
                            { "ASSIGNED_OWNER_REF_ID", requestObject.CreatedBy },
                            { "ASSIGNMENT_OWNER_REF_ID", requestObject.FlowInstanceId }
                        };

                        var wfAssignmentId = await frameworkRepository.ExecuteScalarAsync<long>(sql, parameters);

                        if (wfAssignmentId != null)
                        {
                            string deleteSql = @"DELETE from FRAMEWORK.F_WF_ASSIGNMENT WHERE  FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_ID = :WF_ASSIGNMENT_ID";

                            var deleteParameters = new Dictionary<string, object>
                            {
                                { "WF_ASSIGNMENT_ID", wfAssignmentId },
                            };

                            await frameworkRepository.ExecuteUpdateAsync(deleteSql, deleteParameters);
                        }
                    }
                    else if (monitoring.FlowTypeId == 2)
                    {
                        string sql = @"Select FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_ID from FRAMEWORK.F_WF_ASSIGNMENT  where
                                FRAMEWORK.F_WF_ASSIGNMENT.IS_DEF_ASSIGNMENT=:IS_DEF_ASSIGNMENT
                                and FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_TYPE_CD=:WF_ASSIGNMENT_TYPE_CD
                                and FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID=:ASSIGNED_OWNER_REF_ID
                                and FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNMENT_OWNER_REF_ID=:ASSIGNMENT_OWNER_REF_ID";

                        var parameters = new Dictionary<string, object>
                        {
                            { "IS_DEF_ASSIGNMENT", 1 },
                            { "WF_ASSIGNMENT_TYPE_CD", "WFVIEW" },
                            { "ASSIGNED_OWNER_REF_ID", requestObject.CreatedBy },
                            { "ASSIGNMENT_OWNER_REF_ID", requestObject.FlowDefId }
                        };

                        var wfAssignmentId = await frameworkRepository.ExecuteScalarAsync<long>(sql, parameters);

                        if (wfAssignmentId != null)
                        {
                            string deleteSql = @"DELETE from FRAMEWORK.F_WF_ASSIGNMENT WHERE  FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_ID = :WF_ASSIGNMENT_ID";

                            var deleteParameters = new Dictionary<string, object>
                            {
                                { "WF_ASSIGNMENT_ID", wfAssignmentId },
                            };

                            await frameworkRepository.ExecuteUpdateAsync(deleteSql, deleteParameters);
                        }
                    }

                    // Update WF_DF_MONITORING_REQUEST
                    string updateSql = @"
                        UPDATE DT_WORKFLOW.WF_DF_MONITORING_REQUEST
                        SET DT_WORKFLOW.WF_DF_MONITORING_REQUEST.IS_ACTIVE = 0,
                            DT_WORKFLOW.WF_DF_MONITORING_REQUEST.LAST_UPDATED = SYSDATE,
                            DT_WORKFLOW.WF_DF_MONITORING_REQUEST.LAST_UPDATED_BY = :CreatedBy
                        WHERE DT_WORKFLOW.WF_DF_MONITORING_REQUEST.MONITORING_REQUEST_ID = :MonitoringRequestId";

                    var updateParameters = new Dictionary<string, object>
                    {
                        { "CreatedBy", monitoring.CreatedBy },
                        { "MonitoringRequestId", monitoring.MonitoringRequestId }
                    };

                    await defaultRepository.ExecuteUpdateAsync(updateSql, updateParameters);


                    // Call UPDATE_WF_HISTORY_MONITORING procedure
                    try
                    {
                        await defaultRepository.ExecuteQueryAsync<dynamic>("EXEC DT_WORKFLOW.UPDATE_WF_HISTORY_MONITORING(:LoginId)",
                            new Dictionary<string, object> { { "LoginId", requestObject.CreatedBy } });
                    }
                    catch (Exception)
                    {

                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }
        private async Task<bool> CheckIfUserIsAdmin(long adminId)
        {
            var repository = repositoryFactory.Create("DT_WORKFLOW");

            string sql = "SELECT 1 FROM DT_WORKFLOW.YYS_ADMINS A WHERE A.IS_SYS_ADMIN = 1 AND A.LOGIN_ID = :AdminId";
            var parameters = new Dictionary<string, object> { { "AdminId", adminId } };

            var result = await repository.ExecuteScalarAsync<int>(sql, parameters);
            return result != null && result == 1;
        }


        private void UpdateEntityProperties(object entity, JsonElement properties)
        {
            var entityType = entity.GetType();
            foreach (var property in properties.EnumerateObject())
            {
                var propertyName = property.Name;
                var propertyValue = property.Value;

                var entityProperty = entityType.GetProperty(propertyName);
                if (entityProperty != null && entityProperty.CanWrite)
                {
                    try
                    {
                        object convertedValue;
                        if (entityProperty.PropertyType == typeof(string))
                        {
                            convertedValue = propertyValue.GetString();
                        }
                        else if (entityProperty.PropertyType == typeof(JsonElement))
                        {
                            convertedValue = propertyValue;
                        }
                        else if (entityProperty.PropertyType.IsEnum)
                        {
                            convertedValue = Enum.Parse(entityProperty.PropertyType, propertyValue.GetString());
                        }
                        else if (propertyValue.ValueKind == JsonValueKind.Array && entityProperty.PropertyType != typeof(string))
                        {
                            var arrayType = entityProperty.PropertyType.GetElementType();
                            var arrayValue = JsonSerializer.Deserialize(propertyValue.GetRawText(), arrayType);
                            convertedValue = arrayValue;
                        }
                        else
                        {
                            convertedValue = JsonSerializer.Deserialize(propertyValue.GetRawText(), entityProperty.PropertyType);
                        }
                        entityProperty.SetValue(entity, convertedValue);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Failed to set property {propertyName}: {ex.Message}");
                    }
                }
                else
                {
                    Console.WriteLine($"Property {propertyName} not found or not writable on entity type {entityType.Name}");
                }
            }
        }

        private UpdateSqlResult ConstructUpdateSql(Type entityType, object entity, object id)
        {
            var tableAttribute = entityType.GetCustomAttribute<TableAttribute>();
            if (tableAttribute == null)
                throw new InvalidOperationException($"TableAttribute not found on type {entityType.Name}");

            var properties = entityType.GetProperties()
                .Where(p => p.GetCustomAttribute<ColumnAttribute>() != null)
                .ToList();

            var updateClauses = new List<string>();
            var parameters = new Dictionary<string, object>();

            foreach (var prop in properties)
            {
                var columnAttr = prop.GetCustomAttribute<ColumnAttribute>();
                if (columnAttr != null && prop.GetCustomAttribute<KeyAttribute>() == null)
                {
                    var paramName = $"p_{prop.Name}";
                    updateClauses.Add($"{columnAttr.Name} = :{paramName}");
                    var value = prop.GetValue(entity);
                    parameters.Add(paramName, value ?? DBNull.Value);
                }
            }

            var keyProperty = GetKeyProperty(entityType);
            if (keyProperty == null)
                throw new InvalidOperationException($"No property marked with [Key] attribute found on type {entityType.Name}");

            var keyColumnName = keyProperty.GetCustomAttribute<ColumnAttribute>()?.Name ?? keyProperty.Name;

            var sql = $"UPDATE {tableAttribute.Schema}.{tableAttribute.Name} SET {string.Join(", ", updateClauses)} WHERE {keyColumnName} = :id";
            parameters.Add("id", id);

            return new UpdateSqlResult(sql, parameters);
        }

        private PropertyInfo GetKeyProperty(Type type)
        {
            return type.GetProperties().FirstOrDefault(p =>
                p.GetCustomAttribute<KeyAttribute>() != null ||
                p.Name.Equals("Id", StringComparison.OrdinalIgnoreCase) ||
                p.Name.Equals($"{type.Name}Id", StringComparison.OrdinalIgnoreCase));
        }

        public async Task<bool> UpdateEntity(UpdateEntityRequestDto request, string workflowName)
        {

            try
            {
                var repository = repositoryFactory.Create("DT_WORKFLOW");
                var entityType = workflowTypeHelper.GetWorkflowEntity(workflowName);
                Console.WriteLine($"Updating entity of type {entityType.Name} with ID {request.Id}");

                var method = typeof(IOracleDataAccessRepository).GetMethod("GetEntityAsync", new[] { typeof(object) });
                if (method == null)
                {
                    Console.WriteLine("GetEntityAsync method not found on IOracleDataAccessRepository");
                    return false;
                }

                var genericMethod = method.MakeGenericMethod(entityType);

                dynamic task = genericMethod.Invoke(repository, new object[] { request.Id });
                var entity = await task;

                if (entity == null)
                {
                    Console.WriteLine($"Entity of type {entityType.Name} with ID {request.Id} not found");
                    return false;
                }

                Console.WriteLine($"Entity found. Updating properties...");
                UpdateEntityProperties(entity, request.Properties);

                var updateSqlResult = ConstructUpdateSql(entityType, entity, request.Id);

                Console.WriteLine($"Executing update SQL: {updateSqlResult.Sql}");
                var rowsAffected = await repository.ExecuteUpdateAsync(updateSqlResult.Sql, updateSqlResult.Parameters);

                Console.WriteLine($"Update completed. Rows affected: {rowsAffected}");
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating entity: {ex.Message}");
                return false;
            }
        }
    }
    public class UpdateSqlResult
    {
        public string Sql { get; }
        public Dictionary<string, object> Parameters { get; }

        public UpdateSqlResult(string sql, Dictionary<string, object> parameters)
        {
            Sql = sql;
            Parameters = parameters;
        }
    }
}