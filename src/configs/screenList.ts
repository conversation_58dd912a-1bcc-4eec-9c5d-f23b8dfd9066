import InboxScreen from '@/screens/Header/InboxScreen'
import WorkflowSelectorScreen from '@/screens/Workflow/WorkflowSelectorScreen'
import HistoryScreen from '@/screens/Header/HistoryScreen'
import TestScreen from '@/screens/Header/TestScreen'
import EndDelegationScreen from '@/screens/Header/EndDelegationScreen'
import EndMonitoringScreen from '@/screens/Header/EndMonitoringScreen'
import WorkflowManagement from '@/screens/Header/WorkflowManagementScreen'
import FlowAdminHistory from '@/screens/Report/FlowAdminHistoryScreen'
import SuspendedWorkflowsScreen from '@/screens/Header/SuspendedWorkflowsScreen'

const screenList = {
  inbox: InboxScreen,
  history: HistoryScreen,
  workflow: WorkflowSelectorScreen,
  test: TestScreen,
  'end-delegation': EndDelegationScreen,
  'end-monitoring': EndMonitoringScreen,
  'workflow-management': WorkflowManagement,
  'flow-admin-history': FlowAdminHistory,
  'suspended-inbox': SuspendedWorkflowsScreen,
}
export default screenList
