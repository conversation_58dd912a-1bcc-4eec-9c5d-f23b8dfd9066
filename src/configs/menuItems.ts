import { IMenuTreeItem } from 'wface'
import i18n from '../i18n' // Import the i18n setup
import languageEn from '../locales/en/menu.json'
import languageTr from '../locales/tr/menu.json'

const getMenuItems = async (): Promise<IMenuTreeItem[]> => {
  const language = localStorage.getItem('language') || 'en'
  await i18n.changeLanguage(language)
  const translations = language === 'tr' ? languageTr : languageEn

  return [
    {
      id: 'inbox',
      text: translations['inbox'],
      screen: 'inbox',
      icon: 'save',
      isDefaultScreen: true,
      notClosable: true,
    },
    {
      id: 'history',
      text: translations['history'],
      screen: 'history',
      icon: 'save',
      notClosable: true,
    },
    {
      id: 'workflow',
      text: translations['workflow'],
      screen: 'workflow',
      icon: 'save',
    },
    {
      id: 'test',
      text: translations['test'],
      screen: 'test',
      icon: 'save',
    },
    {
      id: 'end-delegation',
      text: translations['test'],
      screen: 'end-delegation',
      icon: 'save',
    },
    {
      id: 'end-monitoring',
      text: translations['test'],
      screen: 'end-monitoring',
      icon: 'save',
    },
    {
      id: 'workflow-management',
      text: translations['test'],
      screen: 'workflow-management',
      icon: 'save',
    },
    {
      id: 'flow-admin-history',
      text: translations['test'],
      screen: 'flow-admin-history',
      icon: 'save',
    },
    {
      id: 'suspended-inbox',
      text: translations['test'],
      screen: 'suspended-inbox',
      icon: 'save',
    },
  ]
}

export default getMenuItems
