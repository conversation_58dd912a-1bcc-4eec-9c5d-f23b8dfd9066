/* Import our enhanced design system first */
@import url('../../styles/design-system.css');

:root {
  /* Keep existing brand palette for compatibility - enhanced by design-system.css */
  --df-primary-50: #f4effa;
  --df-primary-100: #e2d7f1;
  --df-primary-500: #5c2d91;
  --df-primary-600: #4b2173;
  --df-primary-700: #3e1b5f;

  /* secondary can be a complementary color or a variation */
  --df-secondary-500: #805ad5;

  --df-success-500: #38a169;
  --df-warning-500: #dd6b20;
  --df-error-500: #e53e3e;

  /* Typography - now enhanced */
  --df-font-family: 'Inter', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  --df-font-size: 14px; /* Slightly increased for better readability */
  --df-text-color: #2d3748;
  --df-text-color-muted: #718096;

  /* Shapes - enhanced */
  --df-radius: 8px; /* Slightly more modern */
  --df-border-color: #e2e8f0;
}

/* Enhanced global styles */
html,
body {
  font-family: var(--df-font-family-primary);
  font-size: var(--df-text-sm);
  background: var(--df-neutral-50);
  color: var(--df-neutral-900);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Enhanced scrollbars using design system */
::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
}

::-webkit-scrollbar-track {
  background: var(--df-neutral-100) !important;
  border-radius: var(--df-radius-full) !important;
}

::-webkit-scrollbar-thumb {
  background: var(--df-neutral-300) !important;
  border-radius: var(--df-radius-full) !important;
  transition: background-color var(--df-transition-base);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--df-neutral-400) !important;
}

/* Enhanced table styles */
th {
  padding: var(--df-space-3) var(--df-space-2) !important;
  font-weight: var(--df-font-weight-semibold) !important;
  background-color: var(--df-neutral-50);
  border-bottom: 1px solid var(--df-neutral-200);
  color: var(--df-neutral-700);
  font-size: var(--df-text-sm);
}

td {
  padding: var(--df-space-3) var(--df-space-2) !important;
  overflow-x: hidden !important;
  border-bottom: 1px solid var(--df-neutral-100);
  color: var(--df-neutral-800);
  font-size: var(--df-text-sm);
}

/* Enhanced main layout */
main {
  background-color: var(--df-neutral-50) !important;
  font-family: var(--df-font-family-primary);
  font-size: var(--df-text-sm);
  line-height: var(--df-leading-normal);
  min-height: 100vh;
}

.container {
  padding: 0 var(--df-space-4);
  max-width: 1280px;
  margin: 0 auto;
}

/* Enhanced alert styles using design system colors */
.warning {
  text-align: center;
  color: var(--df-error-500) !important;
  padding: var(--df-space-8);
  background-color: var(--df-error-50);
  border: 1px solid var(--df-error-200);
  border-radius: var(--df-radius-lg);
  margin: var(--df-space-4);
}

/* Enhanced row styles */
.underlineRow {
  border-bottom: 1px solid var(--df-neutral-200);
  padding: var(--df-space-2) 0;
  transition: background-color var(--df-transition-base);
}

.row {
  border-bottom: 1px solid var(--df-neutral-200);
  transition: all var(--df-transition-base);
}

.row:hover {
  background: var(--df-neutral-50);
  transform: translateX(2px);
}

.underlineHeader {
  border-bottom: 2px solid var(--df-primary-500);
  font-size: var(--df-text-lg) !important;
  font-weight: var(--df-font-weight-semibold);
  color: var(--df-neutral-900);
  padding-bottom: var(--df-space-2);
  margin-bottom: var(--df-space-4);
}

.subRow {
  border-top: 1px solid var(--df-neutral-100);
  margin-top: var(--df-space-3) !important;
  color: var(--df-neutral-600);
  font-style: italic;
  padding-top: var(--df-space-2);
}

/* Enhanced error styles */
.error {
  margin: var(--df-space-5) !important;
  box-shadow: var(--df-shadow-md) !important;
  border: 1px solid var(--df-error-200);
  border-radius: var(--df-radius-lg);
  padding: var(--df-space-4);
  text-align: center;
  color: var(--df-error-600);
  background-color: var(--df-error-50);
}

/* Enhanced step container */
.stepContainer {
  margin: var(--df-space-5) !important;
  box-shadow: var(--df-shadow-sm) !important;
  border: 1px solid var(--df-neutral-200);
  border-radius: var(--df-radius-lg);
  padding: var(--df-space-6);
  background-color: var(--df-neutral-0);
  position: relative;
}

.stepContainer > h2,
.stepContainer > h3 {
  position: absolute;
  background: var(--df-neutral-0);
  height: auto;
  margin-top: calc(-1 * var(--df-space-6));
  padding: 0 var(--df-space-3);
  color: var(--df-primary-600);
  font-weight: var(--df-font-weight-semibold);
  font-size: var(--df-text-lg);
}

/* Enhanced dropdown styles */
.dropdown {
  position: relative;
}

.dcontent {
  position: absolute;
  background-color: var(--df-neutral-0);
  width: 100%;
  border: 1px solid var(--df-neutral-200);
  border-radius: var(--df-radius-base);
  z-index: var(--df-z-dropdown);
  min-height: 2rem;
  max-height: 20rem;
  overflow-y: auto;
  overflow-x: hidden;
  box-shadow: var(--df-shadow-lg);
}

.dcontent a {
  color: var(--df-neutral-900);
  padding: var(--df-space-2) var(--df-space-3);
  height: auto;
  width: 100% !important;
  text-decoration: none;
  display: block;
  font-size: var(--df-text-sm);
  line-height: var(--df-leading-normal);
  font-weight: var(--df-font-weight-normal);
  font-family: var(--df-font-family-primary);
  cursor: pointer;
  transition: all var(--df-transition-base);
}

.dcontent p {
  color: var(--df-neutral-500);
  font-size: var(--df-text-sm);
  font-weight: var(--df-font-weight-medium);
  font-family: var(--df-font-family-primary);
  text-align: center;
  padding: var(--df-space-3);
}

.dropdown a:hover {
  background-color: var(--df-primary-50);
  color: var(--df-primary-700);
}

.dcontent .selected {
  border: none !important;
  background-color: var(--df-primary-100);
  color: var(--df-primary-800);
  font-weight: var(--df-font-weight-medium);
}

/* Enhanced form elements */
.sselectIcon {
  color: var(--df-neutral-500);
  position: absolute;
  margin: 0 0 0 -20px;
  padding: var(--df-space-1);
}

.sselectText {
  width: 100%;
  border-width: 0 0 2px;
  border-color: var(--df-neutral-300);
  display: inline-block;
  background-color: transparent !important;
  font-size: var(--df-text-sm) !important;
  font-family: var(--df-font-family-primary);
  color: var(--df-neutral-900);
  transition: all var(--df-transition-base);
}

.sselectText:hover {
  border-color: var(--df-primary-500);
}

.sselectText::placeholder {
  color: var(--df-neutral-400);
  font-family: var(--df-font-family-primary);
  font-weight: var(--df-font-weight-normal);
}

.no-outline:focus {
  outline: none;
  border-color: var(--df-primary-500);
  box-shadow: var(--df-input-shadow-focus);
}

/* Enhanced buttons */
.contactAddBtn {
  padding: var(--df-space-1) !important;
  margin: 0;
  min-width: 32px !important;
  border-radius: var(--df-radius-base);
  background-color: var(--df-primary-500);
  color: var(--df-neutral-0);
  transition: all var(--df-transition-base);
}

.contactAddBtn:hover {
  background-color: var(--df-primary-600);
  transform: translateY(-1px);
  box-shadow: var(--df-shadow-sm);
}

.register {
  padding: var(--df-space-2) var(--df-space-3) !important;
  margin: 0;
  border-radius: var(--df-radius-base);
  background-color: var(--df-primary-500);
  color: var(--df-neutral-0);
  transition: all var(--df-transition-base);
}

/* Enhanced jump to state container */
.jump-to-state-container {
  padding: var(--df-space-6);
  border-radius: var(--df-radius-lg);
  box-shadow: var(--df-shadow-sm);
  background: linear-gradient(135deg, var(--df-neutral-25) 0%, var(--df-neutral-0) 100%);
  border: 1px solid var(--df-neutral-200);
}

.jump-to-state-container .field-row {
  padding: var(--df-space-4) 0;
  border-bottom: 1px solid var(--df-neutral-100);
}

.jump-to-state-container .field-row:last-child {
  border-bottom: none;
}

.jump-to-state-container .field-label {
  font-weight: var(--df-font-weight-semibold);
  color: var(--df-neutral-700);
  font-size: var(--df-text-sm);
}

.jump-to-state-container .field-value {
  color: var(--df-neutral-900);
  font-size: var(--df-text-base);
}

.jump-to-state-container .description-field {
  margin-top: var(--df-space-6);
}

.jump-to-state-container .jump-button {
  margin-top: var(--df-space-6);
  box-shadow: var(--df-shadow-sm);
  background: linear-gradient(135deg, var(--df-primary-500) 0%, var(--df-primary-600) 100%);
  transition: all var(--df-transition-base);
}

.jump-to-state-container .jump-button:hover {
  box-shadow: var(--df-shadow-md);
  transform: translateY(-1px);
}

.jump-to-state-container .error-message {
  color: var(--df-error-500);
  margin-top: var(--df-space-2);
  font-size: var(--df-text-sm);
}

/* Navigation and layout improvements */
#main-hamburger-button,
#btn-main-more {
  display: none;
}

#main-app-bar div span h6 {
  margin-left: var(--df-space-3);
  color: var(--df-neutral-0);
  font-weight: var(--df-font-weight-medium);
}

main {
  margin-left: 0 !important;
  padding-top: var(--df-space-4);
}

/* Enhanced table interactions */
.MuiTableCell-head .filterButtonContainer {
  display: flex !important;
}

.digiTable .MuiTouchRipple-root {
  display: none;
}

.digiTable .custom-input-effect input {
  outline: none;
  border: 1px solid var(--df-neutral-300);
  border-radius: var(--df-radius-base);
  padding: var(--df-space-2) var(--df-space-3);
  transition: all var(--df-transition-base);
}

.digiTable .custom-input-effect input:focus {
  outline: none;
  border-color: var(--df-primary-500);
  box-shadow: var(--df-input-shadow-focus);
}

.MuiChip-root .filterButtonContainer {
  display: none !important;
}

/* Enhanced WebView specific styles */
body.webview-mode #main-app-bar,
body.webview-mode .header-component,
body.webview-mode .main-page > div:nth-child(2),
body.webview-mode .app-bar,
body.webview-mode .top-navigation,
body.webview-mode .main-header,
body.webview-mode .MuiAppBar-root,
body.webview-mode .MuiToolbar-root.MuiToolbar-gutters {
  display: none !important;
}

body.webview-mode main,
body.webview-mode .main-content,
body.webview-mode .workflow-layout {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

/* Enhanced responsive design */
@media (max-width: 768px) {
  .custom-toaster {
    margin-top: 3rem !important;
  }

  .container {
    padding: 0 var(--df-space-2);
  }

  th, td {
    padding: var(--df-space-2) var(--df-space-1) !important;
    font-size: var(--df-text-xs);
  }

  .stepContainer {
    margin: var(--df-space-3) !important;
    padding: var(--df-space-4);
  }
}

/* Animation improvements */
@media (prefers-reduced-motion: no-preference) {
  * {
    scroll-behavior: smooth;
  }

  .row,
  .underlineRow,
  button,
  input,
  select,
  textarea {
    transition: all var(--df-transition-base);
  }
}

/* Focus improvements for accessibility */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
[role="button"]:focus-visible {
  outline: 2px solid var(--df-primary-500);
  outline-offset: 2px;
  box-shadow: 0 0 0 3px var(--df-focus-ring-color);
}
