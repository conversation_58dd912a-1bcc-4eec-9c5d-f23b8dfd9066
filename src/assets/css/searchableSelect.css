/* Basic styles for the dropdown container */
.dropdown {
  position: relative;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  width: 100%; /* Ensure it takes full width like other inputs */
}

/* Styles for the dropdown content (the list of options) */
.dcontent {
  position: absolute;
  background-color: var(--digi-color-background-default, #fff);
  width: 100%;
  border: 1px solid var(--digi-color-border-neutral-strong, #ccc);
  border-top: none; /* Remove top border as it's connected to the input */
  z-index: var(--digi-z-index-dropdown, 1050); /* Ensure it's above other elements */
  min-height: 2rem;
  max-height: 15rem; /* Consistent max-height */
  overflow-y: auto; /* Changed from scroll to auto */
  overflow-x: hidden;
  -ms-overflow-style: none; /* For IE and Edge */
  scrollbar-width: thin; /* For Firefox */
  scrollbar-color: var(--digi-color-scrollbar-thumb, #888) var(--digi-color-scrollbar-track, #f1f1f1);
  border-radius: 0 0 var(--digi-border-radius-medium, 4px) var(--digi-border-radius-medium, 4px);
  box-shadow: var(--digi-shadow-medium, 0 4px 8px rgba(0, 0, 0, 0.1));
}

/* Scrollbar styling for WebKit browsers */
.dcontent::-webkit-scrollbar {
  width: 8px;
}

.dcontent::-webkit-scrollbar-track {
  background: var(--digi-color-scrollbar-track, #f1f1f1);
  border-radius: var(--digi-border-radius-medium, 4px);
}

.dcontent::-webkit-scrollbar-thumb {
  background: var(--digi-color-scrollbar-thumb, #888);
  border-radius: var(--digi-border-radius-medium, 4px);
}

.dcontent::-webkit-scrollbar-thumb:hover {
  background: var(--digi-color-scrollbar-thumb-hover, #555);
}

/* Styles for individual options in the dropdown */
.dcontent a {
  color: var(--digi-color-text-primary, #333);
  padding: var(--digi-spacing-small, 8px) var(--digi-spacing-medium, 12px); /* Consistent padding */
  text-decoration: none;
  display: block; /* Changed from inline-block for full width usage */
  font-size: var(--digi-font-size-medium, 1rem); /* Standard font size */
  line-height: var(--digi-line-height-medium, 1.5);
  font-weight: var(--digi-font-weight-regular, 400);
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Hover state for dropdown options */
.dropdown .dcontent a:hover {
  background-color: var(--digi-color-background-hover, #f5f5f5);
  color: var(--digi-color-text-accent, #007bff);
}

/* Styles for the "no options" message */
.dcontent p {
  color: var(--digi-color-text-secondary, #757575);
  font-size: var(--digi-font-size-small, 0.875rem);
  font-weight: var(--digi-font-weight-regular, 400);
  text-align: center;
  padding: var(--digi-spacing-medium, 12px);
  margin: 0;
}

/* Styles for a selected option */
.dcontent .selected {
  background-color: var(--digi-color-background-selected, #e0e0e0); /* Softer selected color */
  color: var(--digi-color-text-primary-on-selected, #000);
  font-weight: var(--digi-font-weight-medium, 500); /* Slightly bolder for selected */
}

.dcontent .selected:hover {
  background-color: var(--digi-color-background-selected-hover, #d5d5d5);
}

/*
  The .sselectIcon class seems to be unused in the TSX.
  If it were used for an icon within the text field, it would need adjustments.
  For now, commenting out as the WTextField's rightButtons handles icons.
*/
/*
.sselectIcon {
  color: rgba(0, 0, 0, 0.5);
  position: absolute;
  margin: 0 0 0 -20px;
  padding: 3px;
}
*/

/*
  Styles for the WTextField component when used as sselectText.
  WTextField itself will have its own base styling. We are enhancing or overriding specific aspects.
  The className "sselectText" is applied to WTextField in the TSX.
*/
.sselectText .MuiInputBase-input {
  /* Targeting the actual input element within WTextField */
  font-size: var(--digi-font-size-medium, 1rem) !important; /* Ensure consistency */
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  background-color: transparent !important;
}

.sselectText .MuiInputLabel-root {
  /* Label styling */
  font-size: var(--digi-font-size-medium, 1rem);
  color: var(--digi-color-text-secondary, #757575);
}

.sselectText .MuiInputLabel-root.Mui-focused {
  color: var(--digi-color-text-accent, #007bff);
}

.sselectText .MuiOutlinedInput-root {
  /* Border and general appearance */
  border-radius: var(--digi-border-radius-medium, 4px);
  transition:
    border-color 0.2s ease-in-out,
    box-shadow 0.2s ease-in-out;
}

.sselectText .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
  border-color: var(--digi-color-border-neutral-hover, #888);
}

.sselectText .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: var(--digi-color-border-accent, #007bff);
  box-shadow: 0 0 0 2px var(--digi-color-focus-ring, rgba(0, 123, 255, 0.25));
}

.sselectText .MuiOutlinedInput-root.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: var(--digi-color-border-error, #d32f2f);
}

.sselectText .MuiOutlinedInput-root.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: var(--digi-color-border-disabled, #ccc);
  background-color: var(--digi-color-background-disabled, #f5f5f5);
}

.sselectText .MuiInputBase-input::placeholder {
  color: var(--digi-color-text-placeholder, #aaa);
  opacity: 1; /* Browsers have different default opacities */
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-weight: var(--digi-font-weight-regular, 400);
}

/* Remove webkit specific cancel button */
.sselectText input::-webkit-search-cancel-button {
  -webkit-appearance: none;
}

/*
  The .no-outline class is good, but focus is now handled by Mui-focused state above.
  If WTextField doesn't use Material-UI's focus outline by default, this might still be needed.
  However, WTextField likely uses Material-UI components, so its focus style should be preferred.
*/
/*
.no-outline:focus {
  outline: none;
  border-color: #0e6ba8; // This was an old color
}
*/

/* Styles for the loading icon (ensure WIcon uses this or similar animation) */
.loading-icon {
  animation: spin 1s linear infinite;
  color: var(--digi-color-icon-primary, #555);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Unused classes, can be removed or repurposed if needed later */
/*
.contactAddBtn {
  padding: 0 !important;
  margin: 0;
  min-width: 25px !important;
}

.register {
  padding: 0 9px !important;
  margin: 0;
}
*/

/* Ensure the WTextField's right button (for the dropdown arrow) is styled correctly */
.sselectText .MuiInputAdornment-root button {
  color: var(--digi-color-icon-actionable, #007bff);
}

.sselectText .MuiInputAdornment-root button:hover {
  background-color: var(--digi-color-background-actionable-hover, rgba(0, 123, 255, 0.08));
}

.sselectText.Mui-disabled .MuiInputAdornment-root button {
  color: var(--digi-color-icon-disabled, #ccc);
}

/* Add a class for when the dropdown is open to potentially style the icon differently */
.dropdown-open .sselectText .MuiInputAdornment-root button .WIcon {
  transform: rotate(180deg);
  transition: transform 0.2s ease-in-out;
}

.sselectText .MuiInputAdornment-root button .WIcon {
  transition: transform 0.2s ease-in-out;
}
