.language-select-container {
  position: relative;
  font-family: Arial, sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
  color: black;
}

.selected-language {
  display: flex;
  justify-content: center;
  padding: 5px;
  padding-left: 10px;
  width: 60px;
  height: 13px;
  /* Adjusted for better visibility */
  border: 1px solid #ccc;
  background-color: white;
  cursor: pointer;
  border-radius: 5px;
  text-align: center;
  /* Center text */
  line-height: 15px;
  font-size: 13px;
  /* Align text vertically */
}

.language-options-container {
  position: absolute;
  top: 100%;
  /* Position below the selected language */
  left: 0;
  padding: 5px;
  width: 65px;
  font-size: 13px;
  margin-top: -2px;
  border: 1px solid #ccc;
  background: white;
  color: black;
  max-height: 200px;
  overflow-y: auto;
  z-index: 2;
  /* Ensure it's above the selected language */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

.language-option {
  text-align: center;
  padding: 5px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-radius: 5px;
}

.language-option:hover {
  background-color: #f0f0f0;
}
